diff --git a/pom.xml b/pom.xml
index c85bd06..390662f 100644
--- a/pom.xml
+++ b/pom.xml
@@ -111,7 +111,7 @@
         <dependency>
             <groupId>com.appnomic.appsone</groupId>
             <artifactId>common</artifactId>
-            <version>2.0.0</version>
+            <version>2.1.1</version>
             <exclusions>
                 <exclusion>
                     <groupId>joda-time</groupId>
@@ -222,7 +222,7 @@
         <dependency>
             <groupId>io.netty</groupId>
             <artifactId>netty-handler</artifactId>
-            <version>4.1.115.Final</version>
+            <version>4.1.118.Final</version>
             <exclusions>
                 <exclusion>
                     <groupId>io.netty</groupId>
@@ -251,13 +251,13 @@
         <dependency>
             <groupId>io.netty</groupId>
             <artifactId>netty-common</artifactId>
-            <version>4.1.115.Final</version>
+            <version>4.1.118.Final</version>
         </dependency>
 
         <dependency>
             <groupId>com.heal</groupId>
             <artifactId>configuration-pojos</artifactId>
-            <version>2.1.0</version>
+            <version>2.5.0-MULTI-THRESHOLD-SNAPSHOT</version>
             <exclusions>
                 <exclusion>
                     <groupId>io.grpc</groupId>
@@ -417,7 +417,8 @@
                             <!--<stylesheet>summary.xsl</stylesheet>-->
                             <fileMappers>
                                 <!-- Configures the file extension of the output files. -->
-                                <fileMapper implementation="org.codehaus.plexus.components.io.filemappers.FileExtensionMapper">
+                                <fileMapper
+                                        implementation="org.codehaus.plexus.components.io.filemappers.FileExtensionMapper">
                                     <targetExtension>.html</targetExtension>
                                 </fileMapper>
                             </fileMappers>
diff --git a/src/main/java/com/heal/signal/detector/SignalDetectorMain.java b/src/main/java/com/heal/signal/detector/SignalDetectorMain.java
index 72f59b4..3ee22a6 100644
--- a/src/main/java/com/heal/signal/detector/SignalDetectorMain.java
+++ b/src/main/java/com/heal/signal/detector/SignalDetectorMain.java
@@ -5,6 +5,7 @@ import org.springframework.boot.SpringApplication;
 import org.springframework.boot.autoconfigure.SpringBootApplication;
 import org.springframework.cache.annotation.EnableCaching;
 import org.springframework.context.annotation.ComponentScan;
+import org.springframework.context.annotation.Profile;
 import org.springframework.context.annotation.PropertySource;
 import org.springframework.scheduling.annotation.EnableAsync;
 import org.springframework.scheduling.annotation.EnableScheduling;
@@ -13,6 +14,7 @@ import org.springframework.scheduling.annotation.EnableScheduling;
 @ComponentScan
 @PropertySource(value= "classpath:conf.properties")
 @EnableScheduling
+@Profile("!test")
 @EnableAsync
 @EnableCaching
 @Slf4j
diff --git a/src/main/java/com/heal/signal/detector/cache/CacheWrapper.java b/src/main/java/com/heal/signal/detector/cache/CacheWrapper.java
index b40adb2..b94dffe 100644
--- a/src/main/java/com/heal/signal/detector/cache/CacheWrapper.java
+++ b/src/main/java/com/heal/signal/detector/cache/CacheWrapper.java
@@ -24,7 +24,7 @@ public class CacheWrapper {
     @Autowired
     RedisUtilities redisUtilities;
 
-    public List<Account> getAccounts(){
+    public List<Account> getAccounts() {
         try {
             log.info("The mode value is {}", mode);
             if (mode == 1) {
@@ -38,7 +38,7 @@ public class CacheWrapper {
         }
     }
 
-    public Application getApplicationByIdentifier(String accIdentifier, String appIdentifier){
+    public Application getApplicationByIdentifier(String accIdentifier, String appIdentifier) {
         try {
             log.info("The mode value is {}", mode);
             if (mode == 1) {
@@ -52,7 +52,7 @@ public class CacheWrapper {
         }
     }
 
-    public List<BasicKpiEntity> getComponentKpis(String accIdentifier, String componentIdentifier){
+    public List<BasicKpiEntity> getComponentKpis(String accIdentifier, String componentIdentifier) {
         try {
             log.info("The mode value is {}", mode);
             if (mode == 1) {
@@ -66,7 +66,7 @@ public class CacheWrapper {
         }
     }
 
-    public BasicKpiEntity getComponentKpiByIdentifier(String accIdentifier, String componentIdentifier, String kpiIdentifier){
+    public BasicKpiEntity getComponentKpiByIdentifier(String accIdentifier, String componentIdentifier, String kpiIdentifier) {
         try {
             log.info("The mode value is {}", mode);
             if (mode == 1) {
@@ -94,7 +94,7 @@ public class CacheWrapper {
         }
     }
 
-    public List<MaintenanceDetails> getServiceMaintenanceDetails(String accIdentifier, String serviceIdentifier){
+    public List<MaintenanceDetails> getServiceMaintenanceDetails(String accIdentifier, String serviceIdentifier) {
         try {
             log.info("The mode value is {}", mode);
             if (mode == 1) {
@@ -108,7 +108,21 @@ public class CacheWrapper {
         }
     }
 
-    public List<MaintenanceDetails> getInstanceMaintenanceDetails(String accIdentifier, String instanceIdentifier){
+    public List<BasicEntity> getAccountServices(String accIdentifier) {
+        try {
+            log.info("The mode value is {}", mode);
+            if (mode == 1) {
+                log.info("Mode is one, Getting the data from redis");
+                return redisUtilities.getAccountServices(accIdentifier);
+            }
+            return cache.getAccountServices(accIdentifier);
+        } catch (Exception e) {
+            log.error("Error occurred while getting service list for account {}", accIdentifier);
+            return Collections.emptyList();
+        }
+    }
+
+    public List<MaintenanceDetails> getInstanceMaintenanceDetails(String accIdentifier, String instanceIdentifier) {
         try {
             log.info("The mode value is {}", mode);
             if (mode == 1) {
@@ -122,7 +136,7 @@ public class CacheWrapper {
         }
     }
 
-    public CompInstKpiEntity getInstanceKPIDetails(String accIdentifier, String instanceIdentifier, int kpiId){
+    public CompInstKpiEntity getInstanceKPIDetails(String accIdentifier, String instanceIdentifier, int kpiId) {
         try {
             log.info("The mode value is {}", mode);
             if (mode == 1) {
@@ -173,7 +187,7 @@ public class CacheWrapper {
             }
             return cache.getSeverityId(name);
         } catch (Exception e) {
-            log.error("Error occurred while getting severity id who's subTypeName is {}",name);
+            log.error("Error occurred while getting severity id who's subTypeName is {}", name);
             return 0;
         }
     }
@@ -220,4 +234,25 @@ public class CacheWrapper {
         }
     }
 
+    /**
+     * This method retrieves the list of applications mapped to a specific service.
+     *
+     * @param accountIdentifier The identifier for the account.
+     * @param serviceIdentifier The identifier for the service.
+     * @return A list of BasicEntity objects representing the applications mapped to the service.
+     */
+    public List<BasicEntity> getApplicationsMappedToService(String accountIdentifier, String serviceIdentifier) {
+        try {
+            log.trace("The mode value is {}", mode);
+            if (mode == 1) {
+                log.trace("Mode is one, Getting the data from redis");
+                return redisUtilities.getApplicationsMappedToService(accountIdentifier, serviceIdentifier);
+            }
+            return cache.getApplicationsMappedToService(accountIdentifier, serviceIdentifier);
+        } catch (Exception e) {
+            log.error("Error occurred while getting applications mapped to service for accountIdentifier : {}, service identifier : {}", accountIdentifier, serviceIdentifier, e);
+            return Collections.emptyList();
+        }
+    }
+
 }
diff --git a/src/main/java/com/heal/signal/detector/cache/LocalCache.java b/src/main/java/com/heal/signal/detector/cache/LocalCache.java
index a427605..b09423d 100644
--- a/src/main/java/com/heal/signal/detector/cache/LocalCache.java
+++ b/src/main/java/com/heal/signal/detector/cache/LocalCache.java
@@ -104,4 +104,22 @@ public class LocalCache {
         return redisUtilities.getAccountOutbounds(accountIdentifier);
     }
 
+    @Cacheable(value = Constants.ACCOUNT_SERVICES, key = "#accountIdentifier + ':services'", unless = "#result == null")
+    public List<BasicEntity> getAccountServices(String accountIdentifier) {
+        log.debug("Fetching service list for the accountIdentifier : {} for the first time from redis.", accountIdentifier);
+        return redisUtilities.getAccountServices(accountIdentifier);
+    }
+
+    /**
+     * This method retrieves the list of applications mapped to a specific service.
+     *
+     * @param accountIdentifier The identifier for the account.
+     * @param serviceIdentifier The identifier for the service.
+     * @return A list of BasicEntity objects representing the applications mapped to the service.
+     */
+    @Cacheable(value = Constants.APPLICATIONS, key = "#accountIdentifier + ':' + #serviceIdentifier", unless = "#result == null || #result.isEmpty()")
+    public List<BasicEntity> getApplicationsMappedToService(String accountIdentifier, String serviceIdentifier) {
+        log.debug("Fetching applications mapped to service for the accountIdentifier : {}, service : {} for the first time from redis", accountIdentifier, serviceIdentifier);
+        return redisUtilities.getApplicationsMappedToService(accountIdentifier, serviceIdentifier);
+    }
 }
diff --git a/src/main/java/com/heal/signal/detector/config/CacheConfig.java b/src/main/java/com/heal/signal/detector/config/CacheConfig.java
index f8a950a..c5abfef 100644
--- a/src/main/java/com/heal/signal/detector/config/CacheConfig.java
+++ b/src/main/java/com/heal/signal/detector/config/CacheConfig.java
@@ -94,6 +94,7 @@ public class CacheConfig {
         cacheManager.registerCustomCache(Constants.SERVICE_NEIGHBOURS_BY_IDENTIFIER, createCacheConfig(serviceNeighboursCacheMaxSize, serviceNeighboursCacheExpireTime).build());
         cacheManager.registerCustomCache(Constants.HEAL_TYPES, createCacheConfig(healTypesCacheMaxSize, healTypesCacheExpireTime).build());
         cacheManager.registerCustomCache(Constants.TENANTS, createCacheConfig(tenantsCacheMaxSize, tenantsCacheExpireTime).build());
+        cacheManager.registerCustomCache(Constants.ACCOUNT_SERVICES, createCacheConfig(accountsCacheMaxSize, accountsCacheExpireTime).build());
         cacheManager.setAllowNullValues(false);
         return cacheManager;
     }
diff --git a/src/main/java/com/heal/signal/detector/config/ExecutorConfig.java b/src/main/java/com/heal/signal/detector/config/ExecutorConfig.java
index b946a3d..e621950 100644
--- a/src/main/java/com/heal/signal/detector/config/ExecutorConfig.java
+++ b/src/main/java/com/heal/signal/detector/config/ExecutorConfig.java
@@ -37,7 +37,7 @@ public class ExecutorConfig {
     private int corePoolSizeHealthMetrics;
     @Value("${scheduler.healthMetrics.thread.pool.max.size:1}")
     private int maxPoolSizeHealthMetrics;
-    @Value("${scheduler.healthMetrics.thread.pool.queue.capacity:1}")
+    @Value("${scheduler.healthMetrics.thread.pool.queue.capacity:2}")
     private int queueCapacityHealthMetrics;
 
     @Value("${scheduler.droppedAnomalyChecker.thread.pool.core.size:1}")
diff --git a/src/main/java/com/heal/signal/detector/opensearch/SignalRepo.java b/src/main/java/com/heal/signal/detector/opensearch/SignalRepo.java
index e07d890..3221e29 100644
--- a/src/main/java/com/heal/signal/detector/opensearch/SignalRepo.java
+++ b/src/main/java/com/heal/signal/detector/opensearch/SignalRepo.java
@@ -7,18 +7,19 @@ import com.appnomic.appsone.opeasearchquery.queryopts.QueryOptions;
 import com.appnomic.appsone.opeasearchquery.results.NameValuePair;
 import com.appnomic.appsone.opeasearchquery.results.RawDocumentResults;
 import com.fasterxml.jackson.databind.ObjectMapper;
+import com.heal.configuration.enums.SignalType;
 import com.heal.configuration.pojos.AnomalySummary;
 import com.heal.configuration.pojos.opensearch.Anomalies;
 import com.heal.configuration.pojos.opensearch.SignalDetails;
 import com.heal.configuration.util.DateHelper;
 import com.heal.signal.detector.config.OpenSearchConfig;
 import com.heal.signal.detector.pojos.AnomalyHelper;
-import com.heal.signal.detector.pojos.RequestHelper;
 import com.heal.signal.detector.pojos.SignalStatus;
 import com.heal.signal.detector.process.SignalDetailProcessor;
 import com.heal.signal.detector.scheduler.OSDataPushScheduler;
 import com.heal.signal.detector.util.Commons;
 import com.heal.signal.detector.util.HealthMetrics;
+import com.heal.signal.detector.util.RedisUtilities;
 import lombok.extern.slf4j.Slf4j;
 import org.opensearch.client.opensearch.OpenSearchClient;
 import org.opensearch.client.opensearch._types.OpenSearchException;
@@ -68,12 +69,97 @@ public class SignalRepo {
     @Autowired
     public OpenSearchConfig openSearchConfig;
 
+    @Autowired
+    public RedisUtilities redisUtilities;
+
     @Autowired
     @Lazy
     public Commons commons;
 
-    public boolean insertSignal(SignalDetails signalDetails, String accountIdentifier, AnomalySummary anomalySummary) {
-        String indexPrefix = signalsIndex + "_" + accountIdentifier.toLowerCase();
+    public Set<SignalDetails> getOpenSignals(String accountIdentifier, boolean isAddAccountFilter, boolean isMLEExclude) throws Exception {
+
+        List<String> indexNames = new ArrayList<>();
+
+        List<NameValuePair> matchFields = new ArrayList<>();
+        matchFields.add(new NameValuePair("currentStatus", "OPEN"));
+        if (isAddAccountFilter) {
+            matchFields.add(new NameValuePair("accountIdentifiers", accountIdentifier));
+        }
+
+        QueryOptions.QueryOptionsBuilder queryOptionsBuilder;
+        if (openSignalOffset == 0) {
+            indexNames.add(signalsIndex + "_*");
+
+            queryOptionsBuilder = QueryOptions.builder()
+                    .indexNames(indexNames)
+                    .isTimeSeriesData(false)
+                    .matchAllFields(Optional.of(matchFields))
+                    .fetchAllRecords(true);
+//                    .allIndex(true);
+
+        } else {
+            long toTime = System.currentTimeMillis();
+            long fromTime = toTime - TimeUnit.DAYS.toMillis(openSignalOffset);
+            DateHelper.getWeeksAsString(fromTime, toTime).forEach(date ->
+                    indexNames.add(signalsIndex + "_" + date));
+
+            queryOptionsBuilder = QueryOptions.builder()
+                    .indexNames(indexNames)
+                    .epochFieldName("updatedTime")
+                    .epochFromDate(fromTime)
+                    .epochToDate(toTime)
+                    .isTimeSeriesData(false)
+                    .matchAllFields(Optional.of(matchFields))
+                    .fetchAllRecords(true);
+        }
+
+        QueryOptions queryOptions;
+
+        if (isMLEExclude) {
+            List<NameValuePair> matchNoneFields = new ArrayList<>();
+            matchNoneFields.add(new NameValuePair("metadata.Source", "MLE"));
+            queryOptionsBuilder.matchNoneOfFields(Optional.of(new ArrayList<>() {{
+                add(matchNoneFields);
+            }}));
+        }
+        queryOptions = queryOptionsBuilder.build();
+
+        try {
+            log.debug("OS query for fetching open signals data: {}", queryOptions);
+            Set<SignalDetails> signalDetails = new HashSet<>();
+            ObjectMapper objectMapper = commons.getObjectMapperWithHtmlEncoder();
+            for (int retry = insertRetry; retry > 0; retry--) {
+                try {
+                    OpenSearchClient openSearchClient = openSearchConfig.getOpenSearchClient(accountIdentifier, signalsIndex);
+                    if (openSearchClient == null) {
+                        log.error("Could not get open signal details because of open search connection issue. Retry:{}, accountIdentifier:{}", retry, accountIdentifier);
+                        continue;
+                    }
+
+                    RawDocumentResults rawDocuments = OpenSearchQueryHelper.getRawDocuments(queryOptions, openSearchClient);
+                    if (rawDocuments == null || rawDocuments.getDocuments() == null || rawDocuments.getDocuments().isEmpty()) {
+                        return Collections.emptySet();
+                    }
+
+                    for (Documents hit : rawDocuments.getDocuments())
+                        signalDetails.add(commons.getObjectMapperWithHtmlEncoder().readValue(hit.getSource(), SignalDetails.class));
+                    break;
+                } catch (OpenSearchException sto) {
+                    log.error("OpenSearchException occurred while getting open signal details. Index:{}, retry:{}, accountIdentifier:{}", indexNames, retry, accountIdentifier, sto);
+                    throw sto;
+                } catch (Exception e) {
+                    log.error("General exception occurred. Retrying. Index:{}, retry:{}, accountIdentifier:{}", indexNames, retry, accountIdentifier, e);
+                }
+            }
+            return signalDetails;
+        } catch (OpenSearchException e) {
+            healthMetrics.updateErrors();
+            log.error("Error in fetching data from index {}. Details: accountId:{}", indexNames, accountIdentifier, e);
+            throw e;
+        }
+    }
+
+    public boolean insertSignal(SignalDetails signalDetails, String accountIdentifier, AnomalySummary anomalySummary, boolean isAddSignalDetails, Set<String> appIds, boolean isWorkload, int anomalitySeverityId) {
         try {
             OpenSearchClient openSearchClient = openSearchConfig.getOpenSearchClient(accountIdentifier, signalsIndex);
             if (openSearchClient == null) {
@@ -90,7 +176,7 @@ public class SignalRepo {
                 return false;
             }
 
-            String indexName = indexPrefix + "_" + indexDates.get(0);
+            String indexName = signalsIndex + "_" + indexDates.get(0);
             IndexRequest<Object> indexRequest = new IndexRequest.Builder<>()
                     .index(indexName)
                     .id(signalDetails.getSignalId())
@@ -99,6 +185,7 @@ public class SignalRepo {
 
             log.debug("severity id before pushing to os for signal id {} is {}", signalDetails.getSignalId(),
                     signalDetails.getSeverityId());
+            log.debug("Query for inserting signal detail : {}", indexRequest);
 
             boolean result = false;
 
@@ -121,9 +208,14 @@ public class SignalRepo {
             }
 
             if (result) {
-                signalDetailProcessor.updateServiceSignals(accountIdentifier, signalDetails.getSignalId(), anomalySummary.getServiceId(),
-                        signalDetails.getStartedTime(), signalDetails.getCurrentStatus());
-                signalDetailProcessor.updateSignalDetailsIntoRedis(accountIdentifier, signalDetails.getSignalId(), anomalySummary);
+                appIds.forEach(appId -> {
+                    signalDetailProcessor.updateServiceSignals(signalDetails.getSignalId(), anomalySummary.getServiceId(),
+                            signalDetails.getStartedTime(), signalDetails.getCurrentStatus(), appId);
+                });
+                signalDetailProcessor.updateSignalDetailsIntoRedis(signalDetails.getSignalId(), anomalySummary, appIds, isWorkload, anomalitySeverityId);
+                if (isAddSignalDetails) {
+                    signalDetailProcessor.updateSignalDetails(signalDetails, false);
+                }
 
                 return true;
             } else {
@@ -141,8 +233,8 @@ public class SignalRepo {
 
     public boolean updateSignal(long updatedTime, String currentStatus, String statusDetails,
                                 Map<String, String> metaDataMap, String anomalyId, String signalId,
-                                String accountIdentifier, int severityId, AnomalySummary anomalySummary, long signalStartTime) {
-        String indexPrefix = signalsIndex + "_" + accountIdentifier.toLowerCase();
+                                String accountIdentifier, int severityId, AnomalySummary anomalySummary,
+                                long signalStartTime, boolean isAddSignalDetails, Set<String> appIds, boolean isWorkload, int anomalitySeverityId) {
         String indexName = null;
         try {
             OpenSearchClient openSearchClient = openSearchConfig.getOpenSearchClient(accountIdentifier, signalsIndex);
@@ -151,19 +243,38 @@ public class SignalRepo {
                 return false;
             }
 
-            SignalDetails signalDetails = getSignalById(signalId, signalStartTime, accountIdentifier);
-            if (signalDetails == null) {
+            List<SignalDetails> signalDetailsList = new ArrayList<>();
+            if (isAddSignalDetails) {
+                SignalDetails signalDetails = redisUtilities.getSignalDetails(signalId);
+                if (signalDetails != null) {
+                    signalDetailsList.add(signalDetails);
+                }
+            } else {
+                signalDetailsList = getSignalById(Collections.singleton(signalId), signalStartTime, System.currentTimeMillis(), accountIdentifier);
+            }
+            if (signalDetailsList == null || signalDetailsList.isEmpty()) {
                 log.error("Error occurred while getting the signal details. signalId:{}, anomalyId:{}, startTime:{}, accountId:{}", signalId, anomalyId, signalStartTime, accountIdentifier);
                 healthMetrics.updateErrors();
                 return false;
             }
+
+            SignalDetails signalDetails = signalDetailsList.get(0);
             signalDetails.getAnomalies().add(anomalyId);
             signalDetails.getStatusDetails().add(currentStatus);
             signalDetails.setUpdatedTime(updatedTime);
             signalDetails.setSeverityId(severityId);
+
+            signalDetails.setAccountIdentifiers(new HashSet<>() {{
+                add(accountIdentifier);
+                if (signalDetails.getAccountIdentifiers() != null) {
+                    addAll(signalDetails.getAccountIdentifiers());
+                }
+            }});
+
             signalDetails.getMetadata().putAll(metaDataMap);
+
             signalDetails.setTimestamp(DateHelper.getDate(anomalySummary.getEventTime()));
-            signalDetails.getServiceIds().add(anomalySummary.getServiceId());
+            signalDetails.getServiceIds().addAll(anomalySummary.getServiceId());
 
             List<String> indexDates = DateHelper.getWeeksAsString(signalDetails.getStartedTime(), signalDetails.getStartedTime());
             if (indexDates == null || indexDates.isEmpty()) {
@@ -171,14 +282,17 @@ public class SignalRepo {
                 healthMetrics.updateErrors();
                 return false;
             }
-            indexName = indexPrefix + "_" + indexDates.get(0);
+            indexName = signalsIndex + "_" + indexDates.get(0);
 
             UpdateRequest<Object, Object> updateRequest = new UpdateRequest.Builder<>()
                     .index(indexName)
                     .id(signalId)
                     .doc(signalDetails)
+                    .retryOnConflict(3)
                     .build();
 
+            log.debug("Query for updating signal detail : {}", updateRequest);
+
             boolean queryResult = false;
             for (int retry = insertRetry; retry > 0; retry--) {
                 try {
@@ -200,10 +314,14 @@ public class SignalRepo {
                 }
             }
 
-
             if (queryResult) {
-                signalDetailProcessor.updateServiceSignals(accountIdentifier, signalId, anomalySummary.getServiceId(), signalStartTime, currentStatus);
-                signalDetailProcessor.updateSignalDetailsIntoRedis(accountIdentifier, signalId, anomalySummary);
+                appIds.forEach(appId -> {
+                    signalDetailProcessor.updateServiceSignals(signalId, anomalySummary.getServiceId(), signalStartTime, currentStatus, appId);
+                });
+                signalDetailProcessor.updateSignalDetailsIntoRedis(signalId, anomalySummary, appIds, isWorkload, anomalitySeverityId);
+                if (isAddSignalDetails) {
+                    signalDetailProcessor.updateSignalDetails(signalDetails, false);
+                }
 
                 return true;
             } else {
@@ -218,6 +336,249 @@ public class SignalRepo {
         }
     }
 
+    /**
+     * Removes an anomaly from a signal's anomaly list and updates the signal in OpenSearch.
+     * Returns true if update is successful, false otherwise.
+     *
+     * @param anomalyId The ID of the anomaly to be removed.
+     * @param signalId The ID of the signal from which the anomaly is to be removed.
+     * @param accountIdentifier The identifier for the account.
+     * @param signalStartTime The start time of the signal.
+     * @param currentStatus The current status of the signal.
+     * @param serviceIdentifiers The set of service identifiers associated with the signal.
+     * @param isAddSignalDetails Flag indicating whether to add signal details.
+     * @param appIds The set of application IDs associated with the signal.
+     * @return boolean indicating success or failure of the operation.
+     */
+    public boolean removeAnomalyFromSignal(String anomalyId, String signalId, String accountIdentifier, long signalStartTime, String currentStatus, Set<String> serviceIdentifiers, boolean isAddSignalDetails, Set<String> appIds) {
+        String indexName = null;
+        try {
+            OpenSearchClient openSearchClient = openSearchConfig.getOpenSearchClient(accountIdentifier, signalsIndex);
+            if (openSearchClient == null) {
+                log.error("Could not update signal index because of open search connection issue. SignalId:{}, anomalyId:{}", signalId, anomalyId);
+                return false;
+            }
+
+            List<SignalDetails> signalDetailsList = new ArrayList<>();
+            if (isAddSignalDetails) {
+                SignalDetails signalDetails = redisUtilities.getSignalDetails(signalId);
+                if (signalDetails != null) {
+                    signalDetailsList.add(signalDetails);
+                }
+            } else {
+                signalDetailsList = getSignalById(Collections.singleton(signalId), signalStartTime, System.currentTimeMillis(), accountIdentifier);
+            }
+            if (signalDetailsList == null || signalDetailsList.isEmpty()) {
+                log.error("Error occurred while getting the signal details. signalId:{}, anomalyId:{}, startTime:{}, accountId:{}", signalId, anomalyId, signalStartTime, accountIdentifier);
+                healthMetrics.updateErrors();
+                return false;
+            }
+
+            SignalDetails signalDetails = signalDetailsList.get(0);
+            boolean removed = signalDetails.getAnomalies().remove(anomalyId);
+            if (!removed) {
+                log.warn("AnomalyId:{} not found in signalId:{} anomaly list.", anomalyId, signalId);
+            }
+            signalDetails.setUpdatedTime(System.currentTimeMillis());
+
+            List<String> indexDates = DateHelper.getWeeksAsString(signalDetails.getStartedTime(), signalDetails.getStartedTime());
+            if (indexDates == null || indexDates.isEmpty()) {
+                log.error("Could not get the index for signalId:{}, signalTime:{}, anomalyId:{}.", signalDetails.getSignalId(), signalDetails.getStartedTime(), anomalyId);
+                healthMetrics.updateErrors();
+                return false;
+            }
+            indexName = signalsIndex + "_" + indexDates.get(0);
+
+            UpdateRequest<Object, Object> updateRequest = new UpdateRequest.Builder<>()
+                    .index(indexName)
+                    .id(signalId)
+                    .doc(signalDetails)
+                    .retryOnConflict(3)
+                    .build();
+
+            log.debug("Query for removing anomaly from signal detail : {}", updateRequest);
+
+            boolean queryResult = false;
+            for (int retry = insertRetry; retry > 0; retry--) {
+                try {
+                    openSearchClient = openSearchConfig.getOpenSearchClient(accountIdentifier, signalsIndex);
+                    if (openSearchClient == null) {
+                        log.error("Could not update signal index because of open search connection issue. Retry:{}, signalId:{}, anomalyId:{}", retry, signalDetails.getSignalId(), anomalyId);
+                        continue;
+                    }
+
+                    UpdateResponse<Object> response = openSearchClient.update(updateRequest, Object.class);
+                    if (response.result() == Result.Updated) {
+                        queryResult = true;
+                    }
+                    break;
+                } catch (Exception e) {
+                    log.error("Failed to update signal to remove anomaly for index:{}, documentId:{}, retry:{}, signal:{}, anomalyId:{}", indexName, retry, signalId, signalDetails, anomalyId, e);
+                }
+            }
+
+            if (queryResult) {
+                appIds.forEach(appId -> {
+                    signalDetailProcessor.updateServiceSignals(signalId, serviceIdentifiers, signalStartTime, currentStatus, appId);
+                });
+                signalDetailProcessor.removeAnomalyFromSignalInRedis(signalId, anomalyId);
+                if (isAddSignalDetails) {
+                    signalDetailProcessor.updateSignalDetails(signalDetails, false);
+                }
+                return true;
+            } else {
+                healthMetrics.updateErrors();
+                log.error("Error occurred while updating document to remove anomaly from signal. responseStatus:{}, signalId:{}, anomalyId:{}, max retried:{}", queryResult, signalDetails.getSignalId(), anomalyId, insertRetry);
+                return false;
+            }
+        } catch (Exception e) {
+            log.error("Error occurred while removing anomaly from signal. indexName:{}, signalId:{}, anomalyId:{}, accountId:{}, startTime:{}", indexName, signalId, anomalyId, accountIdentifier, signalStartTime, e);
+            healthMetrics.updateErrors();
+            return false;
+        }
+    }
+
+    public boolean signalDetailsUpdateStatus(long updatedTime, String updatedStatus, Map<String, String> metaData, String signalId,
+                                             Set<String> relatedSignals, String accountIdentifier, long signalStartTime, boolean anomalyUpdateReq) {
+        List<String> indexNames = new ArrayList<>();
+        OpenSearchClient openSearchClient = openSearchConfig.getOpenSearchClient(accountIdentifier, signalsIndex);
+        if (openSearchClient == null) {
+            log.error("Could not insert into signal index because of open search connection issue. SignalId:{}", signalId);
+            return false;
+        }
+
+        SignalDetails signalDetails = redisUtilities.getSignalDetails(signalId);
+        if (signalDetails == null) {
+            log.error("Error occurred while getting the signal details. signalId:{}, startTime:{}, accountId:{}", signalId, signalStartTime, accountIdentifier);
+            healthMetrics.updateErrors();
+            return false;
+        }
+
+        if (anomalyUpdateReq) {
+            //Update Related Anomalies SignalIds List
+            log.debug("Anomalies list size [{}]. Early Warning signal id {}, Problem signal ids {}", signalDetails.getAnomalies().size(), signalId,
+                    relatedSignals);
+            signalDetails.getAnomalies().forEach(c -> {
+                String[] anomalyIdArr = c.split("-");
+                String anomalyTime = anomalyIdArr[anomalyIdArr.length - 1];
+                updateAnomaly(c, Long.parseLong(anomalyTime) * 60000, relatedSignals, accountIdentifier);
+            });
+        }
+
+        signalDetails.setAccountIdentifiers(new HashSet<>() {{
+            add(accountIdentifier);
+            if (signalDetails.getAccountIdentifiers() != null) {
+                addAll(signalDetails.getAccountIdentifiers());
+            }
+        }});
+
+        signalDetails.getMetadata().putAll(metaData);
+
+        signalDetails.setUpdatedTime(updatedTime);
+        if (!signalDetails.getCurrentStatus().equalsIgnoreCase(updatedStatus)) {
+            signalDetails.setCurrentStatus(updatedStatus);
+        }
+        signalDetails.getStatusDetails().add(updatedStatus);
+        if (relatedSignals != null && !relatedSignals.isEmpty()) {
+            if (signalDetails.getRelatedSignals() != null && !signalDetails.getRelatedSignals().isEmpty()) {
+                signalDetails.getRelatedSignals().addAll(relatedSignals);
+            } else {
+                signalDetails.setRelatedSignals(relatedSignals);
+            }
+        }
+
+        DateHelper.getWeeksAsString(signalStartTime, signalStartTime).forEach(date ->
+                indexNames.add(signalsIndex + "_" + date));
+
+        String indexName = indexNames.get(0);
+        UpdateRequest<Object, Object> updateRequest = new UpdateRequest.Builder<>()
+                .index(indexName)
+                .id(signalId)
+                .doc(signalDetails)
+                .retryOnConflict(3)
+                .build();
+
+        log.debug("Query for updating signal status detail : {}", updateRequest);
+
+        boolean queryResult = false;
+        for (int retry = insertRetry; retry > 0; retry--) {
+            try {
+                openSearchClient = openSearchConfig.getOpenSearchClient(accountIdentifier, signalsIndex);
+                if (openSearchClient == null) {
+                    log.error("Error occurred while updating signal because of open search connection issue. Retry:{}, SignalId:{}", retry, signalDetails.getSignalId());
+                    continue;
+                }
+
+                UpdateResponse<Object> response = openSearchClient.update(updateRequest, Object.class);
+                if (response.result() == Result.Updated) {
+                    queryResult = true;
+                }
+                break;
+            } catch (Exception e) {
+                log.error("Error occurred update into open search document for index:{}, documentId:{}, retry:{}, signal:{}", indexName, signalDetails.getSignalId(), retry, signalDetails);
+            }
+        }
+
+        if (queryResult) {
+            signalDetailProcessor.updateSignalDetails(signalDetails, false);
+            return true;
+        } else {
+            healthMetrics.updateErrors();
+            log.error("Error occurred in updating the document into open search for closing signal. responseStatus:{}, signalId:{}, max retried:{}", queryResult, signalDetails.getSignalId(), insertRetry);
+            return false;
+        }
+    }
+
+    public Set<Anomalies> getAnomalyById(Set<String> anomalyIdSet, Set<String> accountIdentifiers, long anomalyTime) {
+        Set<Anomalies> result = new HashSet<>();
+
+        List<String> indexNames = new ArrayList<>();
+        try {
+            ObjectMapper objectMapper = commons.getObjectMapperWithHtmlEncoder();
+            List<NameValuePair> matchAnyFieldsAnomalyId = new ArrayList<>();
+            anomalyIdSet.forEach(anomalyId -> matchAnyFieldsAnomalyId.add(new NameValuePair("anomalyId", anomalyId)));
+
+            accountIdentifiers.forEach(accountIdentifier ->
+                    DateHelper.getWeeksAsString(anomalyTime, anomalyTime).forEach(date ->
+                            indexNames.add(anomaliesIndex + "_" + accountIdentifier.toLowerCase() + "_" + date)));
+
+            QueryOptions queryOptions = QueryOptions.builder()
+                    .indexNames(indexNames)
+                    .isTimeSeriesData(false)
+                    .matchAnyOfFields(Optional.of(List.of(matchAnyFieldsAnomalyId)))
+                    .numberOfRawRecords(anomalyIdSet.size())
+                    .build();
+
+            log.debug("Query for getting anomaly detail : {}", queryOptions);
+
+            for (int retry = insertRetry; retry > 0; retry--) {
+                try {
+                    OpenSearchClient elasticClient = openSearchConfig.getOpenSearchClient(accountIdentifiers.iterator().next(), anomaliesIndex);
+                    if (elasticClient == null) {
+                        log.error("Could not get anomaly details because of OpenSearch connection issue. Retry:{}, anomalyIds:{}", retry, anomalyIdSet);
+                        continue;
+                    }
+
+                    RawDocumentResults rawDocuments = OpenSearchQueryHelper.getRawDocuments(queryOptions, elasticClient);
+                    if (rawDocuments == null || rawDocuments.getDocuments() == null || rawDocuments.getDocuments().isEmpty()) {
+                        return null;
+                    }
+
+                    for (Documents hit : rawDocuments.getDocuments())
+                        result.add(objectMapper.readValue(hit.getSource(), Anomalies.class));
+                } catch (Exception e) {
+                    log.error("Failed to get anomaly detail because of OpenSearch issue. Index:{}, retry:{}, anomalyIds:{}", indexNames, retry, anomalyIdSet, e);
+                }
+            }
+
+        } catch (Exception e) {
+            healthMetrics.updateErrors();
+            log.error("Error in getting doc from indexNames:{}, anomalyIds:{}, accountIds:{}.", indexNames, anomalyIdSet, accountIdentifiers, e);
+        }
+
+        return result;
+    }
+
     public boolean updateAnomaly(String anomalyId, long anomalyTime, Set<String> signalIds, String accountId) {
         return scheduler.addToAnomalyHelper(AnomalyHelper.builder()
                 .anomalyId(anomalyId)
@@ -227,7 +588,7 @@ public class SignalRepo {
                 .retry(maxRetry).build());
     }
 
-    public boolean updateAnomalyIndex(String anomalyId, long anomalyTime, Set<String> signalIds, String accountId) {
+    public boolean updateAnomalyIndex(String anomalyId, long anomalyTime, Set<String> signalIds, String accountId, Anomalies anomalyDetail) {
         String indexPrefix = anomaliesIndex + "_" + accountId.toLowerCase();
         List<String> indexNames = new ArrayList<>();
         try {
@@ -239,33 +600,48 @@ public class SignalRepo {
                 return false;
             }
 
-            Set<String> existingSignalIds = anomalies.getSignalIds() == null ? new HashSet<>() : anomalies.getSignalIds();
-            existingSignalIds.addAll(signalIds);
-
-            anomalies.setSignalIds(existingSignalIds);
             DateHelper.getWeeksAsString(anomalyTime, anomalyTime).forEach(date ->
                     indexNames.add(indexPrefix + "_" + date));
-
             if (indexNames.isEmpty()) {
                 log.error("Could not get the index for signalIds:{}, anomalyId:{}, anomalyTime:{}.", signalIds, anomalyId, anomalyTime);
                 healthMetrics.updateErrors();
                 return false;
             }
 
+            Set<String> existingSignalIds = anomalyDetail.getSignalIds() == null ? new HashSet<>() : anomalyDetail.getSignalIds();
+            existingSignalIds.addAll(signalIds);
+            anomalyDetail.setSignalIds(existingSignalIds);
+
             UpdateRequest<Object, Object> updateRequest = new UpdateRequest.Builder<>()
                     .index(indexNames.get(0))
                     .id(anomalyId)
                     .doc(anomalies)
+                    .retryOnConflict(3)
                     .build();
 
             log.trace("Anomaly found for update the signal, existing signalsIds:{}, indexNames:{}, anomalyId:{}, signalIds:{}, accountId:{}", signalIds, indexNames, anomalyId, signalIds, accountId);
-            boolean isAnomalyAddedToQueue = scheduler.addToQueue(RequestHelper.builder().accountIdentifier(accountId).updateRequest(updateRequest).indexName(anomaliesIndex).build());
-            if (!isAnomalyAddedToQueue) {
-                log.error("Anomaly is not added to scheduler queue for anomaly update, indexNames:{}, anomalyId:{}, signalIds:{}, accountId:{}", indexNames, anomalyId, signalIds, accountId);
+            // TODO: Temp fix as anomaly was not getting updated
+//            boolean isAnomalyAddedToQueue = scheduler.addToQueue(RequestHelper.builder().accountIdentifier(accountId).updateRequest(updateRequest).indexName(anomaliesIndex).build());
+//            if (!isAnomalyAddedToQueue) {
+//                log.error("Anomaly is not added to scheduler queue for anomaly update, indexNames:{}, anomalyId:{}, signalIds:{}, accountId:{}", indexNames, anomalyId, signalIds, accountId);
+//            }
+//            return isAnomalyAddedToQueue;
+
+            OpenSearchClient openSearchClient = openSearchConfig.getOpenSearchClient(accountId, indexPrefix);
+            if (openSearchClient == null) {
+                log.error("Could not update anomaly index because of open search connection issue. AnomalyId:{}", anomalyId);
+                return false;
             }
-            return isAnomalyAddedToQueue;
+            IndexRequest<Object> indexRequest = new IndexRequest.Builder<>()
+                    .index(indexNames.get(0))
+                    .id(anomalyId)
+                    .document(anomalyDetail)
+                    .build();
+            IndexResponse response = openSearchClient.index(indexRequest);
+            return response.result() == Result.Updated;
         } catch (Exception e) {
-            log.error("Error occurred while updating anomaly. indexNames:{}, anomalyId:{}, signalId:{}, accountId:{}", indexNames, anomalyId, signalIds, accountId, e);
+            log.error("Error occurred while preparing anomaly update request. IndexNames:{}, AnomalyId: [{}], SignalIds to add: [{}], Account: [{}]",
+                    indexNames, anomalyId, signalIds, accountId, e);
             healthMetrics.updateErrors();
             return false;
         }
@@ -294,29 +670,62 @@ public class SignalRepo {
         return !OpenSearchQueryHelper.checkAndGetExistingIndex(queryOptions, openSearchClient).isEmpty();
     }
 
-    public SignalDetails getSignalById(String signalId, long startTime, String accountIdentifier) {
-        String indexPrefix = signalsIndex + "_" + accountIdentifier.toLowerCase();
+    public List<String> getExistingSignalsIndexNames(String accountIdentifier) throws Exception {
+        try {
+            List<String> signalIndexList = new ArrayList<>();
+            OpenSearchClient client = openSearchConfig.getOpenSearchClient(accountIdentifier, signalsIndex);
+            if (client == null) {
+                log.error("Could not get index list because of openSearch connection issue. Skipping index signal status update check in Redis." +
+                        " Account Identifier:{}, Index: {}", accountIdentifier, signalsIndex);
+                throw new Exception("Couldn't establish OpenSearch connection");
+            }
+
+            List<String> indexNames = new ArrayList<>();
+            indexNames.add(signalsIndex + "_*");
+            QueryOptions queryOptions = QueryOptions.builder()
+                    .indexNames(indexNames)
+                    .build();
+
+            RawDocumentResults rawDocuments = OpenSearchQueryHelper.getRawDocuments(queryOptions, client);
+            if (rawDocuments == null || rawDocuments.getDocuments() == null || rawDocuments.getDocuments().isEmpty()) {
+                return signalIndexList;
+            }
+
+            for (Documents hit : rawDocuments.getDocuments())
+                signalIndexList.add(hit.getIndexName());
+
+            return signalIndexList;
+        } catch (Exception e) {
+            log.error(e.getMessage());
+            throw new Exception(e.getMessage());
+        }
+    }
+
+    public List<SignalDetails> getSignalById(Set<String> signalIdSet, long startTime, long endTime, String accountIdentifier) {
+        List<SignalDetails> signalDetailsList = new ArrayList<>();
         List<String> indexNames = new ArrayList<>();
         try {
             ObjectMapper objectMapper = commons.getObjectMapperWithHtmlEncoder();
-            DateHelper.getWeeksAsString(startTime, startTime).forEach(date ->
-                    indexNames.add(indexPrefix + "_" + date));
+            DateHelper.getWeeksAsString(startTime, endTime).forEach(date ->
+                    indexNames.add(signalsIndex + "_" + date));
 
-            List<NameValuePair> matchFields = new ArrayList<>();
-            matchFields.add(new NameValuePair("_id", signalId));
+            List<NameValuePair> matchAnyFieldsSignalId = new ArrayList<>();
+            signalIdSet.forEach(signalId -> matchAnyFieldsSignalId.add(new NameValuePair("_id", signalId)));
 
             QueryOptions queryOptions = QueryOptions.builder()
                     .indexNames(indexNames)
                     .isTimeSeriesData(false)
-                    .fetchAllRecords(true)
-                    .matchAllFields(Optional.of(matchFields))
+                    .numberOfRawRecords(signalIdSet.size())
+                    .matchAnyOfFields(Optional.of(new ArrayList<>() {{
+                        add(matchAnyFieldsSignalId);
+                    }}))
                     .build();
 
             log.debug("Query for getting signal detail : {}", queryOptions);
 
             OpenSearchClient client = openSearchConfig.getOpenSearchClient(accountIdentifier, signalsIndex);
             if (client == null) {
-                log.error("Could not get signal details because of open search connection issue. SignalId:{}", signalId);
+                log.error("Could not get signal details because of open search connection issue. SignalId:{}", signalIdSet);
                 return null;
             }
 
@@ -326,7 +735,7 @@ public class SignalRepo {
                 try {
                     OpenSearchClient openSearchClient = openSearchConfig.getOpenSearchClient(accountIdentifier, signalsIndex);
                     if (openSearchClient == null) {
-                        log.error("Could not get signal details because of open search connection issue. Retry:{}, signalId:{}", retry, signalId);
+                        log.error("Could not get signal details because of open search connection issue. Retry:{}, signalId:{}", retry, signalIdSet);
                         continue;
                     }
 
@@ -337,21 +746,74 @@ public class SignalRepo {
                     if (rawDocuments == null || rawDocuments.getDocuments() == null || rawDocuments.getDocuments().isEmpty()) {
                         return null;
                     }
-                    String doc = rawDocuments.getDocuments().get(0).getSource();
-                    return objectMapper.readValue(doc, SignalDetails.class);
+
+                    for (Documents hit : rawDocuments.getDocuments())
+                        signalDetailsList.add(objectMapper.readValue(hit.getSource(), SignalDetails.class));
+
+                    return signalDetailsList;
                 } catch (Exception e) {
-                    log.error("Failed to get signal details because of open search issue. Index:{}, retry:{}, signalId:{}", indexNames, retry, signalId, e);
+                    log.error("Failed to get signal details because of open search issue. Index:{}, retry:{}, signalId:{}", indexNames, retry, signalIdSet, e);
                 }
             }
 
         } catch (Exception e) {
             healthMetrics.updateErrors();
-            log.error("Error while getting signal details from indexNames:{}, signalId:{}, accountId:{}.", indexNames, signalId, accountIdentifier, e);
+            log.error("Error while getting signal details from indexNames:{}, signalId:{}, accountId:{}.", indexNames, signalIdSet, accountIdentifier, e);
+        }
+        return null;
+    }
+
+    /**
+     * Fetches signal details by their IDs from OpenSearch.
+     *
+     * @param signalIdSet Set of signal IDs to fetch.
+     * @param accountIdentifier Identifier for the account.
+     * @return List of SignalDetails objects or null if an error occurs.
+     */
+    public List<SignalDetails> getSignalById(Set<String> signalIdSet, String accountIdentifier) {
+        List<SignalDetails> signalDetailsList = new ArrayList<>();
+        String indexPrefix = signalsIndex + "_*";
+
+        try {
+            OpenSearchClient client = openSearchConfig.getOpenSearchClient(accountIdentifier, signalsIndex);
+            if (client == null) {
+                log.error("Could not get signal details because of open search connection issue. SignalId:{}", signalIdSet);
+                return null;
+            }
+
+            ObjectMapper objectMapper = commons.getObjectMapperWithHtmlEncoder();
+
+            List<NameValuePair> matchAnyFieldsSignalId = new ArrayList<>();
+            signalIdSet.forEach(signalId -> matchAnyFieldsSignalId.add(new NameValuePair("_id", signalId)));
+
+            QueryOptions queryOptions = QueryOptions.builder()
+                    .indexNames(Collections.singletonList(indexPrefix))
+                    .isTimeSeriesData(false)
+                    .numberOfRawRecords(signalIdSet.size())
+                    .matchAnyOfFields(Optional.of(new ArrayList<>() {{
+                        add(matchAnyFieldsSignalId);
+                    }}))
+                    .build();
+
+            log.debug("Query for getting signal detail : {}", queryOptions);
+
+            RawDocumentResults rawDocuments = OpenSearchQueryHelper.getRawDocuments(queryOptions, client);
+            if (rawDocuments == null || rawDocuments.getDocuments() == null || rawDocuments.getDocuments().isEmpty()) {
+                return signalDetailsList;
+            }
+
+            for (Documents hit : rawDocuments.getDocuments())
+                signalDetailsList.add(objectMapper.readValue(hit.getSource(), SignalDetails.class));
+
+            return signalDetailsList;
+        } catch (Exception e) {
+            healthMetrics.updateErrors();
+            log.error("Error while getting signal details from indexPrefix:{}, signalId:{}, accountId:{}.", indexPrefix, signalIdSet, accountIdentifier, e);
         }
         return null;
     }
 
-    private Anomalies getAnomalyById(String anomalyId, String accountIdentifier, long anomalyTime) {
+    public Anomalies getAnomalyById(String anomalyId, String accountIdentifier, long anomalyTime) {
         String indexPrefix = anomaliesIndex + "_" + accountIdentifier.toLowerCase();
         List<String> indexNames = new ArrayList<>();
         try {
@@ -398,30 +860,32 @@ public class SignalRepo {
         return null;
     }
 
-    public boolean closeSignal(SignalDetails signalDetails, String accountIdentifier) {
-        String indexPrefix = signalsIndex + "_" + accountIdentifier.toLowerCase();
-
+    public boolean closeSignal(SignalDetails signalDetails, String accountIdentifier, String appIdentifier) {
         String indexName = null;
         try {
 
             OpenSearchClient openSearchClient = openSearchConfig.getOpenSearchClient(accountIdentifier, signalsIndex);
             if (openSearchClient == null) {
-                log.error("Could not insert into signal index because of open search connection issue. SignalId:{}", signalDetails.getSignalId());
+                log.error("Could not insert into signal index because of OpenSearch connection issue. SignalId:{}", signalDetails.getSignalId());
                 return false;
             }
+
             List<String> indexDates = DateHelper.getWeeksAsString(signalDetails.getStartedTime(), signalDetails.getStartedTime());
             if (indexDates == null || indexDates.isEmpty()) {
                 log.error("Could not get the index for signalId:{}, signalTime:{}.", signalDetails.getSignalId(), signalDetails.getStartedTime());
                 healthMetrics.updateErrors();
                 return false;
             }
-            indexName = indexPrefix + "_" + indexDates.get(0);
+            indexName = signalsIndex + "_" + indexDates.get(0);
             UpdateRequest<Object, Object> updateRequest = new UpdateRequest.Builder<>()
                     .index(indexName)
                     .id(signalDetails.getSignalId())
                     .doc(signalDetails)
+                    .retryOnConflict(3)
                     .build();
 
+            log.debug("Signal Closing request to OpenSearch for signal id : {}, Details : {}", signalDetails.getSignalId(), updateRequest);
+
             boolean queryResult = false;
             for (int retry = insertRetry; retry > 0; retry--) {
                 try {
@@ -437,12 +901,21 @@ public class SignalRepo {
                     }
                     break;
                 } catch (Exception e) {
-                    log.error("Error occurred update into open search document for index:{}, documentId:{}, retry:{}, signal:{}", indexName, signalDetails.getSignalId(), retry, signalDetails);
+                    if (retry == 1) {
+                        log.error("Error occurred while update into open search document for index:{}, documentId:{}, retry:{}, signal:{}, exception:{}",
+                                indexName, signalDetails.getSignalId(), retry, signalDetails, e.getMessage());
+                    } else {
+                        log.error("Error occurred while update into open search document for index:{}, documentId:{}, retry:{}, signal:{}, exception: ",
+                                indexName, signalDetails.getSignalId(), retry, signalDetails, e);
+                    }
                 }
             }
 
             if (queryResult) {
-                signalDetails.getServiceIds().forEach(s -> signalDetailProcessor.updateServiceSignals(accountIdentifier, signalDetails.getSignalId(), s, signalDetails.getStartedTime(), SignalStatus.CLOSED.name()));
+                signalDetailProcessor.updateServiceSignals(signalDetails.getSignalId(), signalDetails.getServiceIds(), signalDetails.getStartedTime(), SignalStatus.CLOSED.name(), appIdentifier);
+                if (signalDetails.getSignalType().equals(SignalType.EARLY_WARNING.name()) || signalDetails.getSignalType().equals(SignalType.PROBLEM.name())) {
+                    signalDetailProcessor.updateSignalDetails(signalDetails, true);
+                }
                 return true;
             } else {
                 healthMetrics.updateErrors();
@@ -457,177 +930,182 @@ public class SignalRepo {
         }
     }
 
-    public boolean signalDetailsUpdateStatus(long updatedTime, String updatedStatus, Map<String, String> mapData, String signalId,
-                                             Set<String> relatedSignals, String accountIdentifier, long signalStartTime, boolean anomalyUpdateReq) {
-        String indexPrefix = signalsIndex + "_" + accountIdentifier.toLowerCase();
-
-        List<String> indexNames = new ArrayList<>();
-        OpenSearchClient openSearchClient = openSearchConfig.getOpenSearchClient(accountIdentifier, signalsIndex);
-        if (openSearchClient == null) {
-            log.error("Could not insert into signal index because of open search connection issue. SignalId:{}", signalId);
-            return false;
-        }
-        SignalDetails signalDetails = getSignalById(signalId, signalStartTime, accountIdentifier);
-
-        if (anomalyUpdateReq) {
-            //Update Related Anomalies SignalIds List
-            log.debug("Anomalies list size [{}]. Early Warning signal id {}, Problem signal ids {}", signalDetails.getAnomalies().size(), signalId,
-                    relatedSignals);
-            signalDetails.getAnomalies().forEach(c -> {
-                String[] anomalyIdArr = c.split("-");
-                String anomalyTime = anomalyIdArr[anomalyIdArr.length - 1];
-                updateAnomaly(c, Long.parseLong(anomalyTime) * 60000, relatedSignals, accountIdentifier);
-            });
-        }
-
-        signalDetails.getMetadata().putAll(mapData);
-        signalDetails.setUpdatedTime(updatedTime);
-        if (!signalDetails.getCurrentStatus().equalsIgnoreCase(updatedStatus)) {
-            signalDetails.setCurrentStatus(updatedStatus);
-        }
-        signalDetails.getStatusDetails().add(updatedStatus);
-        if (relatedSignals != null && !relatedSignals.isEmpty()) {
-            if (signalDetails.getRelatedSignals() != null && !signalDetails.getRelatedSignals().isEmpty()) {
-                signalDetails.getRelatedSignals().addAll(relatedSignals);
-            } else {
-                signalDetails.setRelatedSignals(relatedSignals);
-            }
-        }
-
-        DateHelper.getWeeksAsString(signalStartTime, signalStartTime).forEach(date ->
-                indexNames.add(indexPrefix + "_" + date));
-
-        String indexName = indexNames.get(0);
-        UpdateRequest<Object, Object> updateRequest = new UpdateRequest.Builder<>()
-                .index(indexName)
-                .id(signalId)
-                .doc(signalDetails)
-                .build();
-
-        boolean queryResult = false;
-        for (int retry = insertRetry; retry > 0; retry--) {
-            try {
-                openSearchClient = openSearchConfig.getOpenSearchClient(accountIdentifier, signalsIndex);
-                if (openSearchClient == null) {
-                    log.error("Error occurred while updating signal because of open search connection issue. Retry:{}, SignalId:{}", retry, signalDetails.getSignalId());
-                    continue;
-                }
-
-                UpdateResponse<Object> response = openSearchClient.update(updateRequest, Object.class);
-                if (response.result() == Result.Updated) {
-                    queryResult = true;
-                }
-                break;
-            } catch (Exception e) {
-                log.error("Error occurred update into open search document for index:{}, documentId:{}, retry:{}, signal:{}", indexName, signalDetails.getSignalId(), retry, signalDetails);
-            }
-        }
-
-        if (queryResult) {
-            return true;
-        } else {
-            healthMetrics.updateErrors();
-            log.error("Error occurred in updating the document into open search for closing signal. responseStatus:{}, signalId:{}, max retried:{}", queryResult, signalDetails.getSignalId(), insertRetry);
-            return false;
-        }
-
-    }
-
-    public Set<SignalDetails> getOpenSignals(String accountIdentifier, boolean isMLEExclude) throws Exception {
+//    public boolean signalDetailsUpdateStatus(long updatedTime, String updatedStatus, Map<String, String> mapData, String signalId,
+//                                             Set<String> relatedSignals, String accountIdentifier, long signalStartTime, boolean anomalyUpdateReq) {
+//        String indexPrefix = signalsIndex + "_" + accountIdentifier.toLowerCase();
+//
+//        List<String> indexNames = new ArrayList<>();
+//        OpenSearchClient openSearchClient = openSearchConfig.getOpenSearchClient(accountIdentifier, signalsIndex);
+//        if (openSearchClient == null) {
+//            log.error("Could not insert into signal index because of open search connection issue. SignalId:{}", signalId);
+//            return false;
+//        }
+//        SignalDetails signalDetails = getSignalById(signalId, signalStartTime, accountIdentifier);
+//
+//        if (anomalyUpdateReq) {
+//            //Update Related Anomalies SignalIds List
+//            log.debug("Anomalies list size [{}]. Early Warning signal id {}, Problem signal ids {}", signalDetails.getAnomalies().size(), signalId,
+//                    relatedSignals);
+//            signalDetails.getAnomalies().forEach(c -> {
+//                String[] anomalyIdArr = c.split("-");
+//                String anomalyTime = anomalyIdArr[anomalyIdArr.length - 1];
+//                updateAnomaly(c, Long.parseLong(anomalyTime) * 60000, relatedSignals, accountIdentifier);
+//            });
+//        }
+//
+//        signalDetails.getMetadata().putAll(mapData);
+//        signalDetails.setUpdatedTime(updatedTime);
+//        if (!signalDetails.getCurrentStatus().equalsIgnoreCase(updatedStatus)) {
+//            signalDetails.setCurrentStatus(updatedStatus);
+//        }
+//        signalDetails.getStatusDetails().add(updatedStatus);
+//        if (relatedSignals != null && !relatedSignals.isEmpty()) {
+//            if (signalDetails.getRelatedSignals() != null && !signalDetails.getRelatedSignals().isEmpty()) {
+//                signalDetails.getRelatedSignals().addAll(relatedSignals);
+//            } else {
+//                signalDetails.setRelatedSignals(relatedSignals);
+//            }
+//        }
+//
+//        DateHelper.getWeeksAsString(signalStartTime, signalStartTime).forEach(date ->
+//                indexNames.add(indexPrefix + "_" + date));
+//
+//        String indexName = indexNames.get(0);
+//        UpdateRequest<Object, Object> updateRequest = new UpdateRequest.Builder<>()
+//                .index(indexName)
+//                .id(signalId)
+//                .doc(signalDetails)
+//                .build();
+//
+//        boolean queryResult = false;
+//        for (int retry = insertRetry; retry > 0; retry--) {
+//            try {
+//                openSearchClient = openSearchConfig.getOpenSearchClient(accountIdentifier, signalsIndex);
+//                if (openSearchClient == null) {
+//                    log.error("Error occurred while updating signal because of open search connection issue. Retry:{}, SignalId:{}", retry, signalDetails.getSignalId());
+//                    continue;
+//                }
+//
+//                UpdateResponse<Object> response = openSearchClient.update(updateRequest, Object.class);
+//                if (response.result() == Result.Updated) {
+//                    queryResult = true;
+//                }
+//                break;
+//            } catch (Exception e) {
+//                log.error("Error occurred update into open search document for index:{}, documentId:{}, retry:{}, signal:{}", indexName, signalDetails.getSignalId(), retry, signalDetails);
+//            }
+//        }
+//
+//        if (queryResult) {
+//            return true;
+//        } else {
+//            healthMetrics.updateErrors();
+//            log.error("Error occurred in updating the document into open search for closing signal. responseStatus:{}, signalId:{}, max retried:{}", queryResult, signalDetails.getSignalId(), insertRetry);
+//            return false;
+//        }
+//
+//    }
+//
+//    public Set<SignalDetails> getOpenSignals(String accountIdentifier, boolean isMLEExclude) throws Exception {
+//
+//        List<String> indexNames = new ArrayList<>();
+//
+//        List<NameValuePair> matchFields = new ArrayList<>();
+//        matchFields.add(new NameValuePair("currentStatus", "OPEN"));
+//
+//        QueryOptions.QueryOptionsBuilder queryOptionsBuilder;
+//        if (openSignalOffset == 0) {
+//            String indexName = signalsIndex + "_" + accountIdentifier.toLowerCase() + "_*";
+//            indexNames.add(indexName);
+//
+//            queryOptionsBuilder = QueryOptions.builder()
+//                    .indexNames(Collections.singletonList(indexName))
+//                    .isTimeSeriesData(false)
+//                    .matchAllFields(Optional.of(matchFields))
+//                    .fetchAllRecords(true);
+//
+//        } else {
+//            String indexPrefix = signalsIndex + "_" + accountIdentifier.toLowerCase();
+//
+//            long toTime = System.currentTimeMillis();
+//            long fromTime = toTime - TimeUnit.DAYS.toMillis(openSignalOffset);
+//            DateHelper.getWeeksAsString(fromTime, toTime).forEach(date ->
+//                    indexNames.add(indexPrefix + "_" + date));
+//
+//            queryOptionsBuilder = QueryOptions.builder()
+//                    .indexNames(indexNames)
+//                    .epochFieldName("updatedTime")
+//                    .epochFromDate(fromTime)
+//                    .epochToDate(toTime)
+//                    .isTimeSeriesData(false)
+//                    .matchAllFields(Optional.of(matchFields))
+//                    .fetchAllRecords(true);
+//        }
+//
+//        QueryOptions queryOptions;
+//
+//        if (isMLEExclude) {
+//            List<NameValuePair> matchNoneFields = new ArrayList<>();
+//            matchNoneFields.add(new NameValuePair("metadata.Source", "MLE"));
+//            queryOptionsBuilder.matchNoneOfFields(Optional.of(new ArrayList<>() {{
+//                add(matchNoneFields);
+//            }}));
+//        }
+//        queryOptions = queryOptionsBuilder.build();
+//
+//        try {
+//            log.debug("OS query for fetching anomaly data: {}", queryOptions);
+//            Set<SignalDetails> signalDetails = new HashSet<>();
+//            for (int retry = insertRetry; retry > 0; retry--) {
+//                try {
+//                    OpenSearchClient openSearchClient = openSearchConfig.getOpenSearchClient(accountIdentifier, signalsIndex);
+//                    if (openSearchClient == null) {
+//                        log.error("Could not get open signal details because of open search connection issue. Retry:{}, accountIdentifier:{}", retry, accountIdentifier);
+//                        continue;
+//                    }
+//
+//                    RawDocumentResults rawDocuments = OpenSearchQueryHelper.getRawDocuments(queryOptions, openSearchClient);
+//                    if (rawDocuments == null || rawDocuments.getDocuments() == null || rawDocuments.getDocuments().isEmpty()) {
+//                        return Collections.emptySet();
+//                    }
+//
+//                    for (Documents hit : rawDocuments.getDocuments())
+//                        signalDetails.add(commons.getObjectMapperWithHtmlEncoder().readValue(hit.getSource(), SignalDetails.class));
+//                    break;
+//
+//                } catch (OpenSearchException ose) {
+//                    log.error("OpenSearchException occurred while getting open signal details. Index:{}, retry:{}, accountIdentifier:{}", indexNames, retry, accountIdentifier, ose);
+//                    throw ose;
+//                } catch (Exception e) {
+//                    log.error("General exception occurred. Retrying. Index:{}, retry:{}, accountIdentifier:{}", indexNames, retry, accountIdentifier, e);
+//                }
+//            }
+//
+//            return signalDetails;
+//
+//        } catch (OpenSearchException ose) {
+//            healthMetrics.updateErrors();
+//            log.error("OpenSearchException bubbled up while fetching data from index {}. Details: accountId:{}", indexNames, accountIdentifier, ose);
+//            throw ose;
+//        } catch (Exception e) {
+//            healthMetrics.updateErrors();
+//            log.error("Unexpected error while fetching data from index {}. Details: accountId:{}", indexNames, accountIdentifier, e);
+//            return Collections.emptySet();
+//        }
+//    }
 
+    public SignalDetails getMLESignalById(String signalId, long startTime, String accountIdentifier) {
         List<String> indexNames = new ArrayList<>();
-
-        List<NameValuePair> matchFields = new ArrayList<>();
-        matchFields.add(new NameValuePair("currentStatus", "OPEN"));
-
-        QueryOptions.QueryOptionsBuilder queryOptionsBuilder;
-        if (openSignalOffset == 0) {
-            String indexName = signalsIndex + "_" + accountIdentifier.toLowerCase() + "_*";
-            indexNames.add(indexName);
-
-            queryOptionsBuilder = QueryOptions.builder()
-                    .indexNames(Collections.singletonList(indexName))
-                    .isTimeSeriesData(false)
-                    .matchAllFields(Optional.of(matchFields))
-                    .fetchAllRecords(true);
-
-        } else {
-            String indexPrefix = signalsIndex + "_" + accountIdentifier.toLowerCase();
-
-            long toTime = System.currentTimeMillis();
-            long fromTime = toTime - TimeUnit.DAYS.toMillis(openSignalOffset);
-            DateHelper.getWeeksAsString(fromTime, toTime).forEach(date ->
-                    indexNames.add(indexPrefix + "_" + date));
-
-            queryOptionsBuilder = QueryOptions.builder()
-                    .indexNames(indexNames)
-                    .epochFieldName("updatedTime")
-                    .epochFromDate(fromTime)
-                    .epochToDate(toTime)
-                    .isTimeSeriesData(false)
-                    .matchAllFields(Optional.of(matchFields))
-                    .fetchAllRecords(true);
-        }
-
-        QueryOptions queryOptions;
-
-        if (isMLEExclude) {
-            List<NameValuePair> matchNoneFields = new ArrayList<>();
-            matchNoneFields.add(new NameValuePair("metadata.Source", "MLE"));
-            queryOptionsBuilder.matchNoneOfFields(Optional.of(new ArrayList<>() {{
-                add(matchNoneFields);
-            }}));
-        }
-        queryOptions = queryOptionsBuilder.build();
-
         try {
-            log.debug("OS query for fetching anomaly data: {}", queryOptions);
-            Set<SignalDetails> signalDetails = new HashSet<>();
-            for (int retry = insertRetry; retry > 0; retry--) {
-                try {
-                    OpenSearchClient openSearchClient = openSearchConfig.getOpenSearchClient(accountIdentifier, signalsIndex);
-                    if (openSearchClient == null) {
-                        log.error("Could not get open signal details because of open search connection issue. Retry:{}, accountIdentifier:{}", retry, accountIdentifier);
-                        continue;
-                    }
-
-                    RawDocumentResults rawDocuments = OpenSearchQueryHelper.getRawDocuments(queryOptions, openSearchClient);
-                    if (rawDocuments == null || rawDocuments.getDocuments() == null || rawDocuments.getDocuments().isEmpty()) {
-                        return Collections.emptySet();
-                    }
-
-                    for (Documents hit : rawDocuments.getDocuments())
-                        signalDetails.add(commons.getObjectMapperWithHtmlEncoder().readValue(hit.getSource(), SignalDetails.class));
-                    break;
-
-                } catch (OpenSearchException ose) {
-                    log.error("OpenSearchException occurred while getting open signal details. Index:{}, retry:{}, accountIdentifier:{}", indexNames, retry, accountIdentifier, ose);
-                    throw ose;
-                } catch (Exception e) {
-                    log.error("General exception occurred. Retrying. Index:{}, retry:{}, accountIdentifier:{}", indexNames, retry, accountIdentifier, e);
-                }
+            OpenSearchClient client = openSearchConfig.getOpenSearchClient(accountIdentifier, signalsIndex);
+            if (client == null) {
+                log.error("Could not get signal details because of open search connection issue. SignalId:{}", signalId);
+                return null;
             }
 
-            return signalDetails;
-
-        } catch (OpenSearchException ose) {
-            healthMetrics.updateErrors();
-            log.error("OpenSearchException bubbled up while fetching data from index {}. Details: accountId:{}", indexNames, accountIdentifier, ose);
-            throw ose;
-        } catch (Exception e) {
-            healthMetrics.updateErrors();
-            log.error("Unexpected error while fetching data from index {}. Details: accountId:{}", indexNames, accountIdentifier, e);
-            return Collections.emptySet();
-        }
-    }
-
-    public SignalDetails getMLESignalById(String signalId, long startTime, String accountIdentifier) {
-        String indexPrefix = signalsIndex + "_" + accountIdentifier.toLowerCase();
-        List<String> indexNames = new ArrayList<>();
-        try {
             ObjectMapper objectMapper = commons.getObjectMapperWithHtmlEncoder();
             DateHelper.getWeeksAsString(startTime, startTime).forEach(date ->
-                    indexNames.add(indexPrefix + "_" + date));
+                    indexNames.add(signalsIndex + "_" + date));
 
             List<NameValuePair> matchFields = new ArrayList<>();
             matchFields.add(new NameValuePair("_id", signalId));
@@ -636,18 +1114,12 @@ public class SignalRepo {
             QueryOptions queryOptions = QueryOptions.builder()
                     .indexNames(indexNames)
                     .isTimeSeriesData(false)
-                    .fetchAllRecords(true)
+                    .numberOfRawRecords(1)
                     .matchAllFields(Optional.of(matchFields))
                     .build();
 
             log.debug("Query for getting signal detail : {}", queryOptions);
 
-            OpenSearchClient client = openSearchConfig.getOpenSearchClient(accountIdentifier, signalsIndex);
-            if (client == null) {
-                log.error("Could not get signal details because of open search connection issue. SignalId:{}", signalId);
-                return null;
-            }
-
             RefreshRequest refreshRequest = new RefreshRequest.Builder().index(indexNames.get(0)).build();
 
             for (int retry = insertRetry; retry > 0; retry--) {
@@ -665,24 +1137,67 @@ public class SignalRepo {
                     if (rawDocuments == null || rawDocuments.getDocuments() == null || rawDocuments.getDocuments().isEmpty()) {
                         return null;
                     }
+
                     String doc = rawDocuments.getDocuments().get(0).getSource();
                     return objectMapper.readValue(doc, SignalDetails.class);
                 } catch (Exception e) {
                     log.error("Failed to get signal details because of open search issue. Index:{}, retry:{}, signalId:{}", indexNames, retry, signalId, e);
                 }
             }
-
         } catch (Exception e) {
             healthMetrics.updateErrors();
             log.error("Error while getting signal details from indexNames:{}, signalId:{}, accountId:{}.", indexNames, signalId, accountIdentifier, e);
         }
+
+        return null;
+    }
+
+    public List<SignalDetails> getMLERelatedSignalsById(Set<String> signalIdSet, String accountIdentifier) {
+        List<SignalDetails> signalDetailsList = new ArrayList<>();
+        String indexPrefix = signalsIndex + "_*";
+
+        try {
+            OpenSearchClient openSearchClient = openSearchConfig.getOpenSearchClient(accountIdentifier, signalsIndex);
+            if (openSearchClient == null) {
+                log.error("Could not get signal details because of open search connection issue. SignalId:{}", signalIdSet);
+                return null;
+            }
+
+            ObjectMapper objectMapper = commons.getObjectMapperWithHtmlEncoder();
+
+            List<NameValuePair> matchAnyFieldsSignalId = new ArrayList<>();
+            signalIdSet.forEach(signalId -> matchAnyFieldsSignalId.add(new NameValuePair("_id", signalId)));
+
+            QueryOptions queryOptions = QueryOptions.builder()
+                    .indexNames(Collections.singletonList(indexPrefix))
+                    .isTimeSeriesData(false)
+                    .numberOfRawRecords(signalIdSet.size())
+                    .matchAnyOfFields(Optional.of(new ArrayList<>() {{
+                        add(matchAnyFieldsSignalId);
+                    }}))
+                    .build();
+
+            log.debug("Query for getting signal detail : {}", queryOptions);
+
+            RawDocumentResults rawDocuments = OpenSearchQueryHelper.getRawDocuments(queryOptions, openSearchClient);
+            if (rawDocuments == null || rawDocuments.getDocuments() == null || rawDocuments.getDocuments().isEmpty()) {
+                return signalDetailsList;
+            }
+
+            for (Documents hit : rawDocuments.getDocuments())
+                signalDetailsList.add(objectMapper.readValue(hit.getSource(), SignalDetails.class));
+
+            return signalDetailsList;
+        } catch (Exception e) {
+            healthMetrics.updateErrors();
+            log.error("Error while getting signal details from indexPrefix:{}, signalId:{}, accountId:{}.", indexPrefix, signalIdSet, accountIdentifier, e);
+        }
         return null;
     }
 
-    public boolean updateMLESignal(long updatedTime, String currentStatus, String statusDetails,
-                                   Map<String, String> metaDataMap, String anomalyId, String signalId,
-                                   String accountIdentifier, int severityId, AnomalySummary anomalySummary, Set<String> relatedSignal, SignalDetails mleSignal) {
-        String indexPrefix = signalsIndex + "_" + accountIdentifier.toLowerCase();
+    public boolean updateMLESignal(long updatedTime, String currentStatus, Map<String, String> metaDataMap, String anomalyId,
+                                   String signalId, String accountIdentifier, int severityId, AnomalySummary anomalySummary, Set<String> relatedSignal,
+                                   SignalDetails mleSignal, Set<String> appIds, boolean isWorkload, int anomalitySeverityId) {
         String indexName = null;
         try {
             OpenSearchClient openSearchClient = openSearchConfig.getOpenSearchClient(accountIdentifier, signalsIndex);
@@ -692,12 +1207,21 @@ public class SignalRepo {
             }
 
             mleSignal.getAnomalies().add(anomalyId);
+            if (!mleSignal.getCurrentStatus().equals(currentStatus)) {
+                mleSignal.setCurrentStatus(currentStatus);
+            }
             mleSignal.getStatusDetails().add(currentStatus);
             mleSignal.setUpdatedTime(updatedTime);
             mleSignal.setSeverityId(severityId);
+
+            mleSignal.getAccountIdentifiers().add(accountIdentifier);
+
+            if (currentStatus.equals(SignalStatus.CLOSED.name()) || currentStatus.equals(SignalStatus.UPGRADED.name())) {
+                metaDataMap.put("end_time", String.valueOf(updatedTime));
+            }
             mleSignal.getMetadata().putAll(metaDataMap);
             mleSignal.setTimestamp(DateHelper.getDate(anomalySummary.getEventTime()));
-            mleSignal.getServiceIds().add(anomalySummary.getServiceId());
+            mleSignal.getServiceIds().addAll(anomalySummary.getServiceId());
             if (!relatedSignal.isEmpty()) {
                 Set<String> osRelatedSignal = mleSignal.getRelatedSignals();
                 if (osRelatedSignal == null) {
@@ -706,18 +1230,20 @@ public class SignalRepo {
                     osRelatedSignal.addAll(relatedSignal);
                 }
             }
+
             List<String> indexDates = DateHelper.getWeeksAsString(mleSignal.getStartedTime(), mleSignal.getStartedTime());
             if (indexDates == null || indexDates.isEmpty()) {
                 log.error("Could not get the index for signalId:{}, signalTime:{}, anomalyId:{}.", mleSignal.getSignalId(), mleSignal.getStartedTime(), anomalyId);
                 healthMetrics.updateErrors();
                 return false;
             }
-            indexName = indexPrefix + "_" + indexDates.get(0);
+            indexName = signalsIndex + "_" + indexDates.get(0);
 
             UpdateRequest<Object, Object> updateRequest = new UpdateRequest.Builder<>()
                     .index(indexName)
                     .id(signalId)
                     .doc(mleSignal)
+                    .retryOnConflict(3)
                     .build();
 
             log.info("severity id found for signal details while updating. severityId {}, signalId {}", severityId,
@@ -744,8 +1270,11 @@ public class SignalRepo {
 
 
             if (queryResult) {
-                signalDetailProcessor.updateServiceSignals(accountIdentifier, signalId, anomalySummary.getServiceId(), mleSignal.getStartedTime(), currentStatus);
-                signalDetailProcessor.updateSignalDetailsIntoRedis(accountIdentifier, signalId, anomalySummary);
+                appIds.forEach(appId -> {
+                    signalDetailProcessor.updateServiceSignals(signalId, anomalySummary.getServiceId(), mleSignal.getStartedTime(), currentStatus, appId);
+                });
+                signalDetailProcessor.updateSignalDetailsIntoRedis(signalId, anomalySummary, appIds, isWorkload, anomalitySeverityId);
+//                signalDetailProcessor.updateSignalDetails(mleSignal);
 
                 return true;
             } else {
@@ -759,5 +1288,4 @@ public class SignalRepo {
             return false;
         }
     }
-
 }
\ No newline at end of file
diff --git a/src/main/java/com/heal/signal/detector/pojos/FailedOpenSignalPojo.java b/src/main/java/com/heal/signal/detector/pojos/FailedOpenSignalPojo.java
index 3ac033d..3617f43 100644
--- a/src/main/java/com/heal/signal/detector/pojos/FailedOpenSignalPojo.java
+++ b/src/main/java/com/heal/signal/detector/pojos/FailedOpenSignalPojo.java
@@ -10,6 +10,8 @@ import lombok.Builder;
 import lombok.Data;
 import lombok.NoArgsConstructor;
 
+import java.util.Set;
+
 @Data
 @Builder
 @AllArgsConstructor
@@ -20,4 +22,6 @@ public class FailedOpenSignalPojo {
     private Account account;
     private SignalType signalType;
     private CompInstKpiEntity compInstKpiEntity;
+    private Set<String> appIds;
+    private Set<String> signalIds;
 }
diff --git a/src/main/java/com/heal/signal/detector/pojos/SignalStatus.java b/src/main/java/com/heal/signal/detector/pojos/SignalStatus.java
index 48d6587..f789bad 100644
--- a/src/main/java/com/heal/signal/detector/pojos/SignalStatus.java
+++ b/src/main/java/com/heal/signal/detector/pojos/SignalStatus.java
@@ -4,4 +4,5 @@ public enum SignalStatus {
     OPEN,
     UPGRADED,
     CLOSED,
+    DOWNGRADED,
 }
diff --git a/src/main/java/com/heal/signal/detector/process/BatchSignalProcessor.java b/src/main/java/com/heal/signal/detector/process/BatchSignalProcessor.java
index e0cb9d8..21cd9a0 100644
--- a/src/main/java/com/heal/signal/detector/process/BatchSignalProcessor.java
+++ b/src/main/java/com/heal/signal/detector/process/BatchSignalProcessor.java
@@ -21,6 +21,7 @@ import org.springframework.beans.factory.annotation.Value;
 import org.springframework.stereotype.Component;
 
 import java.util.Collections;
+import java.util.HashSet;
 import java.util.Map;
 import java.util.Set;
 
@@ -54,32 +55,35 @@ public class BatchSignalProcessor {
     public void processBatchJobEvent(AnomalyEventProtos.AnomalyEvent anomalyEvent, Account account) {
         long st = System.currentTimeMillis();
 
-        Set<SignalDetails> signalDetailsSet;
+        Set<SignalDetails> openSignalsFromOS;
         try {
-            signalDetailsSet = signalRepo.getOpenSignals(account.getIdentifier(), true);
-        } catch (Exception os) {
-            log.error("Error occurred while fetching open signals so pushing in failed queue will be processing it later " +
-                            "for accountIdentifier: {}, anomalyId: {}",
-                    account.getIdentifier(), anomalyEvent.getAnomalyId(), os);
-            localQueues.addToFailedOpenSignalQueue(FailedOpenSignalPojo.builder().signalType(SignalType.BATCH_JOB)
-                    .anomalyEvent(anomalyEvent)
-                    .account(account)
-                    .build());
-            return;
-        }
 
-        try {
+            try {
+                openSignalsFromOS = signalRepo.getOpenSignals(account.getIdentifier(), true, true);
+            } catch (Exception e) {
+                log.error("Error occurred while fetching open signals. So pushing in failed queue. Will be processing it later " +
+                                "for accountIdentifier: {}, anomalyId: {}",
+                        account.getIdentifier(), anomalyEvent.getAnomalyId(), e);
+
+                localQueues.addToFailedOpenSignalQueue(FailedOpenSignalPojo.builder()
+                        .signalType(SignalType.BATCH_JOB)
+                        .anomalyEvent(anomalyEvent)
+                        .account(account)
+                        .build());
+                return;
+            }
+
             String appId = anomalyEvent.getAppId(0);
-            int higherSeverityId = wrapper.getSeverityId("Severe");
 
-            BasicKpiEntity kpiEntity = wrapper.getComponentKpiByIdentifier(globalAccountIdentifier, batchComponentIdentifier, anomalyEvent.getBatchInfo().getKpiId());
+            BasicKpiEntity kpiEntity = wrapper.getComponentKpiByIdentifier(globalAccountIdentifier, batchComponentIdentifier,
+                    anomalyEvent.getBatchInfo().getKpiId());
             if (kpiEntity == null) {
                 log.error("Invalid kpi identifier, account Identifier:{}, component identifier:{}, Anomaly event details:{}",
                         globalAccountIdentifier, batchComponentIdentifier, anomalyEvent);
                 return;
             }
 
-            SignalDetails signalDetails = signalDetailsSet
+            SignalDetails signalDetails = openSignalsFromOS
                     .parallelStream()
                     .filter(s -> s.getSignalType().equalsIgnoreCase(SignalType.BATCH_JOB.name()))
                     .filter(s -> s.getServiceIds().contains(appId))
@@ -92,62 +96,65 @@ public class BatchSignalProcessor {
                 return;
             }
 
-            log.debug("Open batch anomaly id:{}, signals:{}", anomalyEvent.getAnomalyId(), signalDetails != null ? signalDetails.getSignalId():null);
+            log.debug("Open batch anomaly id:{}, signals:{}", anomalyEvent.getAnomalyId(), signalDetails != null ? signalDetails.getSignalId() : null);
+
             Map<String, String> metaData = commons.getSignalMetaData(anomalyEvent, SignalType.BATCH_JOB);
+            Map<String, String> anomalyEventMetadataMap = anomalyEvent.getBatchInfo().getMetadataMap();
 
             // No open signals for batch
-            if (signalDetails == null) {
+            int severityId = Integer.parseInt(anomalyEvent.getBatchInfo().getThresholdSeverity());
 
-                int severityId = wrapper.getSeverityId(anomalyEvent.getBatchInfo().getThresholdSeverity());
+            if (signalDetails == null) {
 
                 String signalId = commons.createSignalId("B", account.getId(), kpiEntity.getId(), application.getId(), anomalyEvent.getEndTimeGMT() / 1000);
 
                 signalDetails = commons.createSignal(signalId, anomalyEvent.getEndTimeGMT(), anomalyEvent.getEndTimeGMT(), 0L, SignalStatus.OPEN.name(),
                         Collections.singleton(anomalyEvent.getAnomalyId()), SignalType.BATCH_JOB, Collections.singleton(appId), Collections.singleton(appId),
-                        Collections.singleton(anomalyEvent.getAnomalyId()), null, null, severityId, metaData);
+                        Collections.singleton(anomalyEvent.getAnomalyId()), null, null, severityId, metaData, anomalyEvent.getAccountId());
 
                 AnomalySummary anomalySummary = commons.getAnomalySummary(anomalyEvent.getKpis().getInstanceId(), kpiEntity.getIdentifier(),
-                        kpiEntity.getCategoryDetails().getIdentifier(), application.getIdentifier(), SignalType.BATCH_JOB,
+                        kpiEntity.getCategoryDetails().getIdentifier(), Collections.singleton(application.getIdentifier()), SignalType.BATCH_JOB,
                         severityId, anomalyEvent.getEndTimeGMT(), false, anomalyEvent.getAnomalyId(), "ALL", "0",
-                        anomalyEvent.getThresholdType(), anomalyEvent.getOperationType(), anomalyEvent.getBatchInfo().getMetadataMap(),
-                        anomalyEvent.getBatchInfo().getValue(), anomalyEvent.getBatchInfo().getThresholdsMap());
+                        anomalyEvent.getThresholdType(), anomalyEvent.getOperationType(), anomalyEventMetadataMap,
+                        anomalyEvent.getBatchInfo().getValue(), anomalyEvent.getBatchInfo().getThresholdsMap(), anomalyEvent.getAccountId());
+
+                boolean insertStatus = signalRepo.insertSignal(signalDetails, anomalyEvent.getAccountId(), anomalySummary, false, new HashSet<>(anomalyEvent.getAppIdList()), false, Integer.parseInt(anomalyEvent.getBatchInfo().getThresholdSeverity()));
 
-                boolean insertStatus = signalRepo.insertSignal(signalDetails, anomalyEvent.getAccountId(), anomalySummary);
                 metrics.updateSignalOpenCount(1);
                 metrics.updateSnapshots(signalDetails.getSignalId() + "_CREATED", 1);
 
-                if(insertStatus) {
-                    forwarder.sendSignalMessages(commons.getSignalProto(account.getIdentifier(), signalDetails, false,
+                if (insertStatus) {
+                    forwarder.sendSignalMessages(commons.getSignalProto(signalDetails, false,
                             false, false, false, anomalySummary));
                 }
                 log.info("Batch signal is created,anomaly id:{}, signal details:{}", anomalyEvent.getAnomalyId(), signalDetails);
             } else {
-                int severityId = wrapper.getSeverityId(anomalyEvent.getBatchInfo().getThresholdSeverity());
-                if (signalDetails.getSeverityId() == severityId || higherSeverityId == signalDetails.getSeverityId()) {
+                if (signalDetails.getSeverityId() > severityId) {
                     severityId = signalDetails.getSeverityId();
                 }
 
                 AnomalySummary anomalySummary = commons.getAnomalySummary(anomalyEvent.getKpis().getInstanceId(), kpiEntity.getIdentifier(),
-                        kpiEntity.getCategoryDetails().getIdentifier(), application.getIdentifier(), SignalType.BATCH_JOB,
+                        kpiEntity.getCategoryDetails().getIdentifier(), Collections.singleton(application.getIdentifier()), SignalType.BATCH_JOB,
                         severityId, anomalyEvent.getEndTimeGMT(), false, anomalyEvent.getAnomalyId(), "ALL", "0",
-                        anomalyEvent.getThresholdType(), anomalyEvent.getOperationType(), anomalyEvent.getBatchInfo().getMetadataMap(),
-                        anomalyEvent.getBatchInfo().getValue(), anomalyEvent.getBatchInfo().getThresholdsMap());
+                        anomalyEvent.getThresholdType(), anomalyEvent.getOperationType(), anomalyEventMetadataMap,
+                        anomalyEvent.getBatchInfo().getValue(), anomalyEvent.getBatchInfo().getThresholdsMap(), anomalyEvent.getAccountId());
 
                 boolean updateStatus = signalRepo.updateSignal(anomalyEvent.getEndTimeGMT(), SignalStatus.OPEN.name(),
                         SignalStatus.OPEN.name(), metaData, anomalyEvent.getAnomalyId(), signalDetails.getSignalId(),
-                        anomalyEvent.getAccountId(), severityId, anomalySummary, signalDetails.getStartedTime());
+                        anomalyEvent.getAccountId(), severityId, anomalySummary, signalDetails.getStartedTime(), false, new HashSet<>(anomalyEvent.getAppIdList()), false, Integer.parseInt(anomalyEvent.getBatchInfo().getThresholdSeverity()));
                 metrics.updateSignalUpdateCount(1);
                 metrics.updateSnapshots(signalDetails.getSignalId() + "_UPDATED", 1);
 
-                if(updateStatus) {
-                    forwarder.sendSignalMessages(commons.getSignalProto(account.getIdentifier(), signalDetails, false,
+                if (updateStatus) {
+                    forwarder.sendSignalMessages(commons.getSignalProto(signalDetails, false,
                             severityId == signalDetails.getSeverityId(), false, false, anomalySummary));
                 }
                 log.info("Batch signal is updated,anomaly id:{}, signal details:{}", anomalyEvent.getAnomalyId(), signalDetails);
             }
+
             boolean queueUpdateStatus = signalRepo.updateAnomaly(anomalyEvent.getAnomalyId(), anomalyEvent.getEndTimeGMT(),
                     Collections.singleton(signalDetails.getSignalId()), anomalyEvent.getAccountId());
-            if(queueUpdateStatus) {
+            if (queueUpdateStatus) {
                 log.trace("Anomaly updated into scheduler queue for batch signal, signalId:{}, anomalyId:{}", signalDetails.getSignalId(), anomalyEvent.getAnomalyId());
             } else {
                 log.error("Anomaly not updated into scheduler queue for batch signal, signalId:{}, anomalyId:{}, because of this anomaly wont be update with signal id.", signalDetails.getSignalId(), anomalyEvent.getAnomalyId());
diff --git a/src/main/java/com/heal/signal/detector/process/GenericSignalProcessor.java b/src/main/java/com/heal/signal/detector/process/GenericSignalProcessor.java
new file mode 100644
index 0000000..5b2d456
--- /dev/null
+++ b/src/main/java/com/heal/signal/detector/process/GenericSignalProcessor.java
@@ -0,0 +1,265 @@
+package com.heal.signal.detector.process;
+
+import com.appnomic.appsone.common.protbuf.AnomalyEventProtos;
+import com.heal.configuration.enums.SignalType;
+import com.heal.configuration.pojos.*;
+import com.heal.configuration.pojos.opensearch.SignalDetails;
+import com.heal.signal.detector.cache.CacheWrapper;
+import com.heal.signal.detector.opensearch.SignalRepo;
+import com.heal.signal.detector.pojos.FailedOpenSignalPojo;
+import com.heal.signal.detector.pojos.SignalStatus;
+import com.heal.signal.detector.service.ForwarderToQueue;
+import com.heal.signal.detector.util.Commons;
+import com.heal.signal.detector.util.HealthMetrics;
+import com.heal.signal.detector.util.LocalQueues;
+import com.heal.signal.detector.util.RedisUtilities;
+import lombok.extern.slf4j.Slf4j;
+import org.springframework.beans.factory.annotation.Autowired;
+import org.springframework.beans.factory.annotation.Value;
+import org.springframework.stereotype.Component;
+
+import java.util.*;
+import java.util.stream.Collectors;
+
+@Component
+@Slf4j
+public class GenericSignalProcessor {
+
+    @Autowired
+    RedisUtilities redisUtilities;
+
+    @Autowired
+    Commons commons;
+
+    @Autowired
+    HealthMetrics metrics;
+
+    @Autowired
+    SignalRepo signalRepo;
+
+    @Autowired
+    ForwarderToQueue forwarder;
+
+    @Autowired
+    CacheWrapper wrapper;
+
+    @Autowired
+    LocalQueues localQueues;
+
+    @Value("${signal.severity.id.high:433}")
+    String highSeverityIdSignal;
+
+    @Value("${signal.severity.id.medium:432}")
+    String mediumSeverityIdSignal;
+
+    @Value("${signal.severity.id.low:431}")
+    String lowSeverityIdSignal;
+
+    /**
+     * Common method to process both EARLY_WARNING and PROBLEM signals.
+     * @param signalType SignalType.EARLY_WARNING or SignalType.PROBLEM
+     * @param anomalyEvent The anomaly event
+     * @param kpiEntity The KPI entity
+     * @param account The account
+     * @param appIds Set of application IDs
+     * @param signalIds Optional signal id provided by caller. If present, method skips searching for open signals and proceeds to create/update accordingly.sent, method skips searching for open signals and proceeds to create/update accordingly.
+     * @return The signal ID if a new signal is created, or null if no new signal is created or an error occurs.
+     */
+    public String processSignalEvent(
+            SignalType signalType,
+            AnomalyEventProtos.AnomalyEvent anomalyEvent,
+            BasicKpiEntity kpiEntity,
+            Account account,
+            Set<String> appIds,
+            Set<String> signalIds
+    ) {
+        long st = System.currentTimeMillis();
+        Set<SignalDetails> openSignalsFromOS;
+        try {
+            try {
+                openSignalsFromOS = signalRepo.getOpenSignals(anomalyEvent.getAccountId(), true, true);
+            } catch (Exception e) {
+                log.error("Error occurred while fetching open signals. So pushing in failed queue. Will be processing it later " +
+                        "for accountIdentifier: {}, anomalyId: {}",
+                        account.getIdentifier(), anomalyEvent.getAnomalyId(), e);
+                localQueues.addToFailedOpenSignalQueue(FailedOpenSignalPojo.builder()
+                        .signalType(signalType)
+                        .anomalyEvent(anomalyEvent)
+                        .account(account)
+                        .kpiEntity(kpiEntity)
+                        .appIds(appIds)
+                        .signalIds(signalIds)
+                        .build());
+                return null;
+            }
+
+            Set<Service> services = anomalyEvent.getKpis().getSvcIdList()
+                    .stream()
+                    .map(serviceIdentifier -> {
+                        Service service = wrapper.getServiceDetailsByIdentifier(anomalyEvent.getAccountId(), serviceIdentifier);
+                        if (service == null) {
+                            log.error("Invalid service identifier, Anomaly event details:{}", anomalyEvent);
+                            metrics.updateErrors();
+                        }
+                        return service;
+                    }).filter(Objects::nonNull)
+                    .collect(Collectors.toSet());
+
+            long start = System.currentTimeMillis();
+            Set<String> serviceIdentifiers = services.stream().map(Service::getIdentifier).collect(Collectors.toSet());
+
+            Set<ControllerAlias> serviceAliasSet = redisUtilities.getServiceAliases();
+            Map<String, String> dcDrServiceMap = new HashMap<>();
+            Map<String, String> drDcServiceMap = new HashMap<>();
+            if (serviceAliasSet != null && !serviceAliasSet.isEmpty()) {
+                dcDrServiceMap.putAll(serviceAliasSet.stream().collect(Collectors.toMap(ControllerAlias::getDcControllerIdentifier, ControllerAlias::getDrControllerIdentifier)));
+                drDcServiceMap.putAll(serviceAliasSet.stream().collect(Collectors.toMap(ControllerAlias::getDrControllerIdentifier, ControllerAlias::getDcControllerIdentifier)));
+            }
+
+            Set<SignalDetails> signals = new HashSet<>();
+            Set<SignalDetails> openSignals = new HashSet<>();
+            try {
+                signals = commons.processAndGetOpenSignals(services, account.getIdentifier(), anomalyEvent.getAnomalyId(),
+                        dcDrServiceMap, drDcServiceMap, openSignalsFromOS, signalType == SignalType.EARLY_WARNING, appIds);
+            } catch (Exception e) {
+                log.error("Error occurred while fetching open signals from Redis. So pushing in failed queue. Will be processing it later " +
+                        "for accountIdentifier: {}, anomalyId: {}", account.getIdentifier(), anomalyEvent.getAnomalyId(), e);
+                localQueues.addToFailedOpenSignalQueue(FailedOpenSignalPojo.builder()
+                        .signalType(signalType)
+                        .anomalyEvent(anomalyEvent)
+                        .account(account)
+                        .kpiEntity(kpiEntity)
+                        .appIds(appIds)
+                        .signalIds(signalIds)
+                        .build());
+
+                return null;
+            }
+
+            if (!signals.isEmpty() && signalIds != null && !signalIds.isEmpty()) {
+                openSignals = signals.stream()
+                        .filter(s -> signalIds.contains(s.getSignalId()))
+                        .collect(Collectors.toSet());
+            }
+
+            log.debug("Open signals in Generic processor, anomaly id:{}, signal ids: {}",
+                    anomalyEvent.getAnomalyId(), openSignals.stream().map(SignalDetails::getSignalId).collect(Collectors.joining()));
+
+            metrics.updateProcessDetails("GenericOpenSignals", System.currentTimeMillis() - start);
+
+            log.debug("Open {} signals for anomalyId:{}, accountId:{}, serviceIdentifiers:{}, signal ids:{}",
+                    signalType.name(), anomalyEvent.getAnomalyId(), account.getIdentifier(), serviceIdentifiers,
+                    openSignals.stream().map(SignalDetails::getSignalId).collect(Collectors.joining(", ")));
+
+            Map<String, String> metaData = commons.getSignalMetaData(anomalyEvent, signalType);
+            Map<String, String> anomalyEventMetadataMap = new HashMap<>(anomalyEvent.getKpis().getMetadataMap());
+
+            if (openSignals.isEmpty()) {
+                start = System.currentTimeMillis();
+                log.info("No {} signal is found. So, we will create a new {} signal for anomalyId:{}, accountId:{}, serviceId:{}.",
+                        signalType.name(), signalType.name(), anomalyEvent.getAnomalyId(), anomalyEvent.getAccountId(), String.join(",", serviceIdentifiers));
+
+                int severityId = Integer.parseInt(anomalyEvent.getKpis().getThresholdSeverity());
+                int servicesHashCode = String.join("-", serviceIdentifiers).hashCode();
+                String newSignalId = commons.createSignalId(signalType == SignalType.EARLY_WARNING ? "E" : "P", account.getId(), Integer.parseInt(anomalyEvent.getKpis().getKpiId()),
+                        servicesHashCode, anomalyEvent.getEndTimeGMT() / 1000);
+
+                SignalDetails signalDetails = commons.createSignal(newSignalId, anomalyEvent.getEndTimeGMT(), anomalyEvent.getEndTimeGMT(), 0L,
+                        SignalStatus.OPEN.name(), Collections.singleton(anomalyEvent.getAnomalyId()), signalType, serviceIdentifiers,
+                        serviceIdentifiers, Collections.singleton(anomalyEvent.getAnomalyId()), null, null, severityId, metaData, anomalyEvent.getAccountId());
+
+                AnomalySummary anomalySummary = commons.getAnomalySummary(anomalyEvent.getKpis().getInstanceId(), kpiEntity.getIdentifier(),
+                        kpiEntity.getCategoryDetails().getIdentifier(), serviceIdentifiers, signalType, severityId, anomalyEvent.getEndTimeGMT(),
+                        anomalyEvent.getKpis().getIsWorkload(), anomalyEvent.getAnomalyId(), anomalyEvent.getKpis().getKpiAttribute(),
+                        kpiEntity.getGroupIdentifier(), anomalyEvent.getThresholdType(), anomalyEvent.getOperationType(),
+                        anomalyEventMetadataMap, anomalyEvent.getKpis().getValue(), anomalyEvent.getKpis().getThresholdsMap(), anomalyEvent.getAccountId());
+
+                boolean insertStatus = signalRepo.insertSignal(signalDetails, anomalyEvent.getAccountId(), anomalySummary, true, appIds, anomalyEvent.getKpis().getIsWorkload(), Integer.parseInt(anomalyEvent.getKpis().getThresholdSeverity()));
+
+                if (insertStatus) {
+                    boolean queueUpdateStatus = signalRepo.updateAnomaly(anomalyEvent.getAnomalyId(), anomalyEvent.getEndTimeGMT(),
+                            Collections.singleton(newSignalId), anomalyEvent.getAccountId());
+                    if (queueUpdateStatus) {
+                        log.trace("Anomaly updated into scheduler queue for {} signal, signalId:{}, anomalyId:{}",
+                                signalType.name(), signalDetails.getSignalId(), anomalyEvent.getAnomalyId());
+                    } else {
+                        log.error("Anomaly not updated into scheduler queue for {} signal, signalId:{}, anomalyId:{}," +
+                                " because of this anomaly wont be update with signal id.", signalType.name(), signalDetails.getSignalId(), anomalyEvent.getAnomalyId());
+                    }
+                    metrics.updateSignalOpenCount(1);
+                    metrics.updateSnapshots(signalDetails.getSignalId() + "_CREATED", 1);
+                    forwarder.sendSignalMessages(commons.getSignalProto(signalDetails, false,
+                            false, false, false, anomalySummary));
+                    log.info("{} signal is created for anomalyId:{}, signal  id:{}, severity:{}, type:{}", signalType.name(), anomalyEvent.getAnomalyId(),
+                            signalDetails.getSignalId(), signalDetails.getSeverityId(), signalDetails.getSignalType());
+                } else {
+                    log.error("{} signal creation failed, anomaly event will dropped for anomaly:{}, signalId:{}",
+                            signalType.name(), anomalyEvent.getAnomalyId(), signalDetails.getSignalId());
+                }
+                metrics.updateProcessDetails("GenericCreateSignals", System.currentTimeMillis() - start);
+                return newSignalId;
+            } else {
+                // Update existing signals if applicable, or upgrade path
+                start = System.currentTimeMillis();
+                List<String> severityLevels = Arrays.asList(lowSeverityIdSignal, mediumSeverityIdSignal, highSeverityIdSignal);
+                log.info("Open signals found for anomalyId:{}, accountId:{}, serviceIdentifiers:{}, signals:{}.",
+                        anomalyEvent.getAnomalyId(), anomalyEvent.getAccountId(), serviceIdentifiers,
+                        openSignals.stream().map(SignalDetails::getSignalId).collect(Collectors.joining(",")));
+
+                openSignals.forEach(signal -> {
+                    int severityId = signal.getSeverityId();
+                    boolean severityUpdated = false;
+                    if (severityLevels.indexOf(signal.getSeverityId().toString()) < severityLevels.indexOf(anomalyEvent.getKpis().getThresholdSeverity())) {
+                        severityId = Integer.parseInt(anomalyEvent.getKpis().getThresholdSeverity());
+                        severityUpdated = true;
+                    }
+                    Map<String, String> updatedMetaData = signal.getMetadata();
+                    updatedMetaData.putAll(metaData);
+
+                    AnomalySummary anomalySummary = commons.getAnomalySummary(anomalyEvent.getKpis().getInstanceId(), kpiEntity.getIdentifier(),
+                            kpiEntity.getCategoryDetails().getIdentifier(), serviceIdentifiers, SignalType.valueOf(signal.getSignalType()),
+                            severityId, anomalyEvent.getEndTimeGMT(), anomalyEvent.getKpis().getIsWorkload(), anomalyEvent.getAnomalyId(),
+                            anomalyEvent.getKpis().getKpiAttribute(), kpiEntity.getGroupIdentifier(), anomalyEvent.getThresholdType(), anomalyEvent.getOperationType(),
+                            anomalyEvent.getKpis().getMetadataMap(), anomalyEvent.getKpis().getValue(), anomalyEvent.getKpis().getThresholdsMap(), anomalyEvent.getAccountId());
+
+                    boolean updateStatus = false;
+                    if (signalType.name().equals(SignalType.PROBLEM.name()) && signal.getSignalType().equals(SignalType.EARLY_WARNING.name())) {
+                        signal.setCurrentStatus(SignalStatus.UPGRADED.name());
+                        String createdSignalId = processSignalEvent(SignalType.PROBLEM, anomalyEvent, kpiEntity, account, appIds, null);
+                        if (signal.getRelatedSignals() == null) {
+                            signal.setRelatedSignals(new HashSet<>());
+                        }
+                        signal.getRelatedSignals().add(createdSignalId);
+                        updateStatus = signalRepo.signalDetailsUpdateStatus(System.currentTimeMillis(), SignalStatus.UPGRADED.name(), signal.getMetadata(), signal.getSignalId(), signal.getRelatedSignals(), account.getIdentifier(), signal.getStartedTime(), true);
+                    } else {
+                        updateStatus = signalRepo.updateSignal(anomalyEvent.getEndTimeGMT(), signal.getCurrentStatus(), signal.getCurrentStatus(), updatedMetaData, anomalyEvent.getAnomalyId(), signal.getSignalId(), anomalyEvent.getAccountId(),
+                                severityId, anomalySummary, anomalyEvent.getStartTimeGMT(), true, appIds, anomalyEvent.getKpis().getIsWorkload(), Integer.parseInt(anomalyEvent.getKpis().getThresholdSeverity()));
+                    }
+
+                    if (updateStatus) {
+                        metrics.updateSignalUpdateCount(1);
+                        metrics.updateSnapshots(signal.getSignalId() + "_UPDATED", 1);
+
+                        log.trace("Signal updated in OS for {} signal, signalId:{}, anomalyId:{}", signalType.name(),
+                                signal.getSignalId(), anomalyEvent.getAnomalyId());
+                        forwarder.sendSignalMessages(commons.getSignalProto(signal, false,
+                                severityUpdated, false, signal.getSignalType().equalsIgnoreCase(SignalStatus.UPGRADED.name()), anomalySummary));
+                    } else {
+                        log.error("Failed to update signal in OS for {} signal, signalId:{}, anomalyId:{}",
+                                signalType.name(), signal.getSignalId(), anomalyEvent.getAnomalyId());
+                    }
+                });
+                metrics.updateProcessDetails("GenericUpdateSignals", System.currentTimeMillis() - start);
+            }
+        } catch (Exception e) {
+            log.error("Error occurred while processing anomaly event. anomalyEvent:{}, account:{}, kpiEntity:{}", anomalyEvent, account, kpiEntity, e);
+            metrics.updateErrors();
+        } finally {
+            long processTime = System.currentTimeMillis() - st;
+            log.debug("{}:Anomaly process time for {} signal is {} ms.", anomalyEvent.getAnomalyId(), signalType.name(), processTime);
+            metrics.updateProcessDetails("GenericAnomalyProcessTime", processTime);
+            metrics.updateProcessDetailsCounter("GenericAnomalyProcessCounter", processTime);
+        }
+        return null;
+    }
+}
diff --git a/src/main/java/com/heal/signal/detector/process/InfoSignalProcessor.java b/src/main/java/com/heal/signal/detector/process/InfoSignalProcessor.java
index 3cdc9ac..1392ccc 100644
--- a/src/main/java/com/heal/signal/detector/process/InfoSignalProcessor.java
+++ b/src/main/java/com/heal/signal/detector/process/InfoSignalProcessor.java
@@ -19,9 +19,7 @@ import lombok.extern.slf4j.Slf4j;
 import org.springframework.beans.factory.annotation.Autowired;
 import org.springframework.stereotype.Component;
 
-import java.util.Collections;
-import java.util.Map;
-import java.util.Set;
+import java.util.*;
 
 @Component
 @Slf4j
@@ -47,45 +45,53 @@ public class InfoSignalProcessor {
     public void processInfoEvent(AnomalyEventProtos.AnomalyEvent anomalyEvent, CompInstKpiEntity kpiEntity, Account account) {
 
         long st = System.currentTimeMillis();
-        Set<SignalDetails> signalDetailsSet;
+        Set<SignalDetails> openSignalsFromOS;
         try {
-            signalDetailsSet = signalRepo.getOpenSignals(account.getIdentifier(), true);
-        } catch (Exception e) {
-            log.error("Error occurred while fetching open signals so pushing in failed queue will be processing it later " +
-                            "for accountIdentifier: {}, anomalyId: {}", account.getIdentifier(), anomalyEvent.getAnomalyId(), e);
-            localQueues.addToFailedOpenSignalQueue(FailedOpenSignalPojo.builder().signalType(SignalType.INFO)
-                    .anomalyEvent(anomalyEvent)
-                    .account(account)
-                    .compInstKpiEntity(kpiEntity)
-                    .build());
-            return;
-        }
 
+            try {
+                openSignalsFromOS = signalRepo.getOpenSignals(account.getIdentifier(), true, true);
+            } catch (Exception e) {
+                log.error("Error occurred while fetching open signals. So pushing in failed queue. Will be processing it later " +
+                                "for accountIdentifier: {}, anomalyId: {}",
+                        account.getIdentifier(), anomalyEvent.getAnomalyId(), e);
+
+                localQueues.addToFailedOpenSignalQueue(FailedOpenSignalPojo.builder()
+                        .signalType(SignalType.INFO)
+                        .anomalyEvent(anomalyEvent)
+                        .account(account)
+                        .compInstKpiEntity(kpiEntity)
+                        .build());
+                return;
+            }
 
-        try {
             String serviceId = anomalyEvent.getKpis().getSvcId(0);
+
             Service service = wrapper.getServiceDetailsByIdentifier(anomalyEvent.getAccountId(), serviceId);
             if (service == null) {
                 log.error("Invalid service identifier, Anomaly event details:{}", anomalyEvent);
                 metrics.updateErrors();
                 return;
             }
-            Map<String, String> metaData = commons.getSignalMetaData(anomalyEvent, SignalType.INFO);
 
-             SignalDetails signalDetails = signalDetailsSet
+            SignalDetails signalDetails = openSignalsFromOS
                     .parallelStream()
                     .filter(s -> s.getSignalType().equalsIgnoreCase(SignalType.INFO.name()))
-                    .filter(s -> s.getMetadata().containsKey("account_id") && s.getMetadata().get("account_id").equalsIgnoreCase(anomalyEvent.getAccountId()))
+                    .filter(s -> s.getAccountIdentifiers().contains(anomalyEvent.getAccountId()))
                     .filter(s -> s.getServiceIds().contains(serviceId))
-                    .filter(s -> s.getMetadata().containsKey("category_id") && s.getMetadata().get("category_id").equalsIgnoreCase(kpiEntity.getCategoryDetails().getIdentifier()))
+                    .filter(s -> s.getMetadata().containsKey("category_id")
+                            && s.getMetadata().get("category_id").equalsIgnoreCase(kpiEntity.getCategoryDetails().getIdentifier()))
                     .findAny()
                     .orElse(null);
 
             log.debug("Open info signal: anomaly id:{}, category id:{}, service id:{}, account id:{}, signal details:{}", anomalyEvent.getAnomalyId(),
                     kpiEntity.getCategoryDetails(), serviceId, anomalyEvent.getAccountId(), (signalDetails != null) ? signalDetails.getSignalId() : null);
 
-            int severityId = wrapper.getSeverityId(anomalyEvent.getKpis().getThresholdSeverity());
-            int higherSeverityId = wrapper.getSeverityId("Severe");
+            int severityId = Integer.parseInt(anomalyEvent.getKpis().getThresholdSeverity());
+
+            Map<String, String> metaData = commons.getSignalMetaData(anomalyEvent, SignalType.INFO);
+            //NOTE:- Proto will give immutable map. Don't remove new HashMap<>() here, else java.lang.UnsupportedOperationException
+            // will come in case of map.put().
+            Map<String, String> anomalyEventMetadataMap = new HashMap<>(anomalyEvent.getKpis().getMetadataMap());
 
             if (signalDetails == null) {
                 log.info("No info signal is found. So, we will create a new info signal for anomaly id:{}, accountId:{}, serviceId:{}, categoryId:{}.",
@@ -97,20 +103,20 @@ public class InfoSignalProcessor {
 
                 signalDetails = commons.createSignal(signalId, anomalyEvent.getEndTimeGMT(), anomalyEvent.getEndTimeGMT(), 0L, SignalStatus.OPEN.name(),
                         Collections.singleton(anomalyEvent.getAnomalyId()), SignalType.INFO, Collections.singleton(serviceId), Collections.singleton(serviceId),
-                        Collections.singleton(anomalyEvent.getAnomalyId()), null, null, severityId, metaData);
+                        Collections.singleton(anomalyEvent.getAnomalyId()), null, null, severityId, metaData, anomalyEvent.getAccountId());
 
                 AnomalySummary anomalySummary = commons.getAnomalySummary(anomalyEvent.getKpis().getInstanceId(), kpiEntity.getIdentifier(),
-                        kpiEntity.getCategoryDetails().getIdentifier(), service.getIdentifier(), SignalType.INFO,
+                        kpiEntity.getCategoryDetails().getIdentifier(), Collections.singleton(service.getIdentifier()), SignalType.INFO,
                         severityId, anomalyEvent.getEndTimeGMT(), false, anomalyEvent.getAnomalyId(),
                         anomalyEvent.getKpis().getKpiAttribute(), kpiEntity.getGroupIdentifier(), anomalyEvent.getThresholdType(), anomalyEvent.getOperationType(),
-                        anomalyEvent.getKpis().getMetadataMap(), anomalyEvent.getKpis().getValue(), anomalyEvent.getKpis().getThresholdsMap());
+                        anomalyEventMetadataMap, anomalyEvent.getKpis().getValue(), anomalyEvent.getKpis().getThresholdsMap(), anomalyEvent.getAccountId());
 
-                boolean insertStatus = signalRepo.insertSignal(signalDetails, anomalyEvent.getAccountId(), anomalySummary);
+                boolean insertStatus = signalRepo.insertSignal(signalDetails, anomalyEvent.getAccountId(), anomalySummary, false, new HashSet<>(anomalyEvent.getAppIdList()), false, Integer.parseInt(anomalyEvent.getKpis().getThresholdSeverity()));
                 metrics.updateSignalOpenCount(1);
                 metrics.updateSnapshots(signalDetails.getSignalId() + "_CREATED", 1);
 
-                if(insertStatus) {
-                    forwarder.sendSignalMessages(commons.getSignalProto(account.getIdentifier(), signalDetails, false,
+                if (insertStatus) {
+                    forwarder.sendSignalMessages(commons.getSignalProto(signalDetails, false,
                             false, false, false, anomalySummary));
                 }
                 log.info("Info signal is created, anomaly id:{}, category id:{}, service id:{}, account id:{}, signal details:{}", anomalyEvent.getAnomalyId(),
@@ -119,32 +125,32 @@ public class InfoSignalProcessor {
             } else {
 
                 boolean isSeverityUpdated = false;
-                if (higherSeverityId != signalDetails.getSeverityId() && higherSeverityId == severityId) {
-                    severityId = signalDetails.getSeverityId();
+                if (severityId > signalDetails.getSeverityId()) {
+                    signalDetails.setSeverityId(severityId);
                     isSeverityUpdated = true;
                 }
 
                 AnomalySummary anomalySummary = commons.getAnomalySummary(anomalyEvent.getKpis().getInstanceId(), kpiEntity.getIdentifier(),
-                        kpiEntity.getCategoryDetails().getIdentifier(), service.getIdentifier(), SignalType.INFO,
-                        severityId, anomalyEvent.getEndTimeGMT(), false, anomalyEvent.getAnomalyId(),
+                        kpiEntity.getCategoryDetails().getIdentifier(), Collections.singleton(service.getIdentifier()), SignalType.INFO,
+                        signalDetails.getSeverityId(), anomalyEvent.getEndTimeGMT(), false, anomalyEvent.getAnomalyId(),
                         anomalyEvent.getKpis().getKpiAttribute(), kpiEntity.getGroupIdentifier(), anomalyEvent.getThresholdType(), anomalyEvent.getOperationType(),
-                        anomalyEvent.getKpis().getMetadataMap(), anomalyEvent.getKpis().getValue(), anomalyEvent.getKpis().getThresholdsMap());
+                        anomalyEventMetadataMap, anomalyEvent.getKpis().getValue(), anomalyEvent.getKpis().getThresholdsMap(), anomalyEvent.getAccountId());
 
                 boolean updateStatus = signalRepo.updateSignal(anomalyEvent.getEndTimeGMT(), SignalStatus.OPEN.name(),
                         SignalStatus.OPEN.name(), metaData, anomalyEvent.getAnomalyId(), signalDetails.getSignalId(),
-                        anomalyEvent.getAccountId(), severityId, anomalySummary, signalDetails.getStartedTime());
+                        anomalyEvent.getAccountId(), signalDetails.getSeverityId(), anomalySummary, signalDetails.getStartedTime(), false, new HashSet<>(anomalyEvent.getAppIdList()), false, Integer.parseInt(anomalyEvent.getKpis().getThresholdSeverity()));
                 metrics.updateSignalUpdateCount(1);
                 metrics.updateSnapshots(signalDetails.getSignalId() + "_UPDATED", 1);
 
-                if(updateStatus) {
-                    forwarder.sendSignalMessages(commons.getSignalProto(account.getIdentifier(), signalDetails, false,
+                if (updateStatus) {
+                    forwarder.sendSignalMessages(commons.getSignalProto(signalDetails, false,
                             isSeverityUpdated, false, false, anomalySummary));
                 }
                 log.info("Info signal is updated,anomaly id:{}, signal id:{}", anomalyEvent.getAnomalyId(), signalDetails.getSignalId());
             }
             boolean queueUpdateStatus = signalRepo.updateAnomaly(anomalyEvent.getAnomalyId(), anomalyEvent.getEndTimeGMT(),
                     Collections.singleton(signalDetails.getSignalId()), anomalyEvent.getAccountId());
-            if(queueUpdateStatus) {
+            if (queueUpdateStatus) {
                 log.trace("Anomaly updated into scheduler queue for info signal, signalId:{}, anomalyId:{}", signalDetails.getSignalId(), anomalyEvent.getAnomalyId());
             } else {
                 log.error("Anomaly not updated into scheduler queue for info signal, signalId:{}, anomalyId:{}, because of this anomaly wont be update with signal id.", signalDetails.getSignalId(), anomalyEvent.getAnomalyId());
diff --git a/src/main/java/com/heal/signal/detector/process/MLESignalProcessor.java b/src/main/java/com/heal/signal/detector/process/MLESignalProcessor.java
index 45961cb..9659fb9 100644
--- a/src/main/java/com/heal/signal/detector/process/MLESignalProcessor.java
+++ b/src/main/java/com/heal/signal/detector/process/MLESignalProcessor.java
@@ -3,12 +3,16 @@ package com.heal.signal.detector.process;
 import com.appnomic.appsone.common.protbuf.SignalProtos;
 import com.heal.configuration.enums.SignalType;
 import com.heal.configuration.pojos.AnomalySummary;
+import com.heal.configuration.pojos.BasicEntity;
+import com.heal.configuration.pojos.SignalSummary;
 import com.heal.configuration.pojos.opensearch.SignalDetails;
 import com.heal.signal.detector.cache.CacheWrapper;
 import com.heal.signal.detector.opensearch.SignalRepo;
+import com.heal.signal.detector.pojos.SignalStatus;
 import com.heal.signal.detector.service.ForwarderToQueue;
 import com.heal.signal.detector.util.Commons;
 import com.heal.signal.detector.util.HealthMetrics;
+import com.heal.signal.detector.util.RedisUtilities;
 import lombok.extern.slf4j.Slf4j;
 import org.springframework.beans.factory.annotation.Autowired;
 import org.springframework.stereotype.Component;
@@ -26,32 +30,44 @@ public class MLESignalProcessor {
 
     @Autowired
     HealthMetrics healthMetrics;
-
     @Autowired
     SignalRepo signalRepo;
     @Autowired
     Commons commons;
-
+    @Autowired
+    CacheWrapper cacheWrapper;
+    @Autowired
+    RedisUtilities redisUtilities;
     @Autowired
     ForwarderToQueue forwarder;
     @Autowired
-    CacheWrapper wrapper;
+    SignalHandler signalHandler;
 
     public void processSignalsProtoForMLE(SignalProtos.SignalDetails signalDetails) {
         log.debug("AI-OPS: Method:processSignalsProtoForMLE - MLE Signal Processor. SignalID {}",
                 signalDetails.getSignalId());
         long startTime = System.currentTimeMillis();
-        try {
 
-            if(signalDetails.getAnomalyDetailsList().size() != 1) {
+        try {
+            // To process closed signals
+            if (signalDetails.getSignalStatus().equalsIgnoreCase(SignalStatus.CLOSED.name()) || signalDetails.getMetadataMap().containsKey("end_time")) {
+                List<SignalDetails> signals = signalRepo.getSignalById(Collections.singleton(signalDetails.getSignalId()), signalDetails.getAccountId());
+                for(SignalDetails signal : signals) {
+                    SignalSummary signalSummary = redisUtilities.getSignalSummary(signal.getSignalId());
+                    signalHandler.closeSignal(signal, signalDetails.getAccountId(), signalSummary.getApplicationIds(),
+                            signalDetails.getMetadataMap().getOrDefault("closing_reason", null),
+                            signalDetails.getMetadataMap().getOrDefault("end_time", null));
+                }
+                return;
+            }
+            if (signalDetails.getAnomalyDetailsList().size() != 1) {
                 log.error("AI-OPS: Rejecting signals as it contains {} anomaly. SignalID {}",
                         signalDetails.getAnomalyDetailsList().size(), signalDetails.getSignalId());
                 healthMetrics.updateErrors();
                 return;
             }
 
-            SignalDetails signalById = signalRepo.getMLESignalById(signalDetails.getSignalId(),
-                    signalDetails.getStartTime(),
+            SignalDetails signalById = signalRepo.getMLESignalById(signalDetails.getSignalId(), signalDetails.getStartTime(),
                     signalDetails.getAccountId());
 
             Set<String> anomalyIds = signalDetails.getAnomalyDetailsList()
@@ -62,123 +78,126 @@ public class MLESignalProcessor {
             SignalProtos.AnomalyDetail anomalyEvent = signalDetails.getAnomalyDetailsList().get(0);
             log.debug("AI-OPS: anomaly event {} for signal id {}", anomalyEvent, signalDetails.getSignalId());
 
-            int severityId = Long.valueOf(anomalyEvent.getSeverityId()).intValue();
-            int signalSeverity = Long.valueOf(signalDetails.getSignalSeverityId()).intValue();
+            int anomalySeverityId = Long.valueOf(anomalyEvent.getSeverityId()).intValue();
+            int signalSeverityId = Long.valueOf(signalDetails.getSignalSeverityId()).intValue();
+
+            log.info("AI-OPS: severity id found from anomaly details coming from signal protos. signalId {}, anomaly " +
+                            "severity Id {}, signal severity Id {}, signal update time {}",
+                    signalDetails.getSignalId(), anomalySeverityId, signalSeverityId, signalDetails.getUpdateTime());
 
-            log.info("AI-OPS: severity id found from anomaly details coming from signal protos. signalId {} anomaly " +
-                            "severityId {}, signal severity Id {}, signal updatetime {}",
-                    signalDetails.getSignalId(), severityId, signalSeverity, signalDetails.getUpdateTime());
+            anomalySeverityId = signalSeverityId == 0 ? anomalySeverityId : signalSeverityId;
 
-            severityId = signalSeverity == 0 ? severityId : signalSeverity;
+            //NOTE:- Proto will give immutable map. Don't remove new HashMap<>() here, else java.lang.UnsupportedOperationException
+            // will come in case of map.put().
+            Map<String, String> metadataMap = new HashMap<>(anomalyEvent.getMetadataMap());
+            metadataMap.put("Source", "MLE");
 
             if (signalById == null) {
                 log.warn("AI-OPS: No open signals found for the account:{}, signalId:{}",
                         signalDetails.getAccountId(), signalDetails.getSignalId());
 
-                Set<String> serviceIds = new HashSet<>();
-                serviceIds.add(anomalyEvent.getServiceId());
+                Set<String> rootCauseServiceIds = new HashSet<>();
+                if (!signalDetails.getRelatedSignalsList().isEmpty()) {
+                    List<SignalDetails> relatedSignalDetails = signalRepo.getMLERelatedSignalsById(new HashSet<>(signalDetails.getRelatedSignalsList()),
+                            signalDetails.getAccountId());
+                    if (relatedSignalDetails != null && !relatedSignalDetails.isEmpty()) {
+                        rootCauseServiceIds.addAll(relatedSignalDetails.stream()
+                                .map(SignalDetails::getRootCauseServiceIds)
+                                .flatMap(Collection::stream)
+                                .collect(Collectors.toSet()));
+                    } else {
+                        log.warn("AI-OPS: No related signals details found in OpenSearch for signal ids {}.", signalDetails.getRelatedSignalsList());
+                    }
+                }
 
-                Map<String, String> metadataMap = new HashMap<>(anomalyEvent.getMetadataMap());
-                metadataMap.put("Source", "MLE");
+                Set<String> serviceIds = new HashSet<>(anomalyEvent.getServiceIdsList());
+
+                Set<String> appIds = serviceIds.stream()
+                    .flatMap(serviceId -> cacheWrapper.getApplicationsMappedToService(signalDetails.getAccountId(), serviceId).stream())
+                    .map(BasicEntity::getName)
+                    .collect(Collectors.toSet());
 
-                List<String> relatedSignal = signalDetails.getRelatedSignalsList();
-                log.info("AI-OPS: total  related signal is {}, Entry point service Id {} ", relatedSignal.size(), signalDetails.getEntryServiceId());
 
+                List<String> relatedSignal = signalDetails.getRelatedSignalsList();
+                log.info("AI-OPS: total related signal is {}, Entry point service Id {} ", relatedSignal.size(), signalDetails.getEntryServiceIdsList());
                 Set<String> relatedSignalSet = new HashSet<>();
-                if(!relatedSignal.isEmpty()) {
+                if (!relatedSignal.isEmpty()) {
                     log.debug("AI-OPS: Related signal for signal Id {} with related signal {}",
-                            signalDetails.getSignalId(),
-                            relatedSignal);
+                            signalDetails.getSignalId(), relatedSignal);
                     relatedSignalSet.addAll(relatedSignal);
                 }
-                signalDetails.getEntryServiceId();
-                String entryServiceId = signalDetails.getEntryServiceId().isEmpty() ? null : signalDetails.getEntryServiceId();
 
-                SignalDetails newSignal = commons.createSignal(signalDetails.getSignalId(), anomalyEvent.getAnomalyTime(),
-                        anomalyEvent.getAnomalyTime(), 0L, signalDetails.getSignalStatus(),
-                        Collections.singleton(anomalyEvent.getAnomalyId()),
-                        SignalType.valueOf(signalDetails.getSignalType()),
-                        serviceIds, serviceIds, anomalyIds, entryServiceId, relatedSignalSet,
-                        severityId, metadataMap);
+                Set<String> entryServiceId = signalDetails.getEntryServiceIdsList().isEmpty() ? null : new HashSet<>(signalDetails.getEntryServiceIdsList());
+
+                SignalDetails newSignal = commons.createSignal(signalDetails.getSignalId(), signalDetails.getStartTime(),
+                        signalDetails.getUpdateTime(), 0L, signalDetails.getSignalStatus(),
+                        Collections.singleton(anomalyEvent.getAnomalyId()), SignalType.valueOf(signalDetails.getSignalType()),
+                        serviceIds, rootCauseServiceIds.isEmpty() ? serviceIds : rootCauseServiceIds,
+                        anomalyIds, entryServiceId, relatedSignalSet, anomalySeverityId, metadataMap, signalDetails.getAccountId());
 
                 AnomalySummary anomalySummary = commons.getAnomalySummary(anomalyEvent.getInstanceId(), anomalyEvent.getKpiId(),
-                        anomalyEvent.getCategoryId(), anomalyEvent.getServiceId(), SignalType.valueOf(signalDetails.getSignalType()),
-                        severityId, anomalyEvent.getAnomalyTime(), anomalyEvent.getIsWorkLoad(), anomalyEvent.getAnomalyId(),
-                        anomalyEvent.getKpiAttribute(), anomalyEvent.getKpiGroupId(), anomalyEvent.getThresholdType(),
-                        anomalyEvent.getOperationType(),
-                        metadataMap, anomalyEvent.getKpiValue(),
-                        anomalyEvent.getThresholdsMap());
+                        anomalyEvent.getCategoryId(), new HashSet<>(anomalyEvent.getServiceIdsList()), SignalType.valueOf(signalDetails.getSignalType()),
+                        anomalySeverityId, anomalyEvent.getAnomalyTime(), anomalyEvent.getIsWorkLoad(), anomalyEvent.getAnomalyId(),
+                        anomalyEvent.getKpiAttribute(), anomalyEvent.getKpiGroupId(), anomalyEvent.getThresholdType(), anomalyEvent.getOperationType(),
+                        metadataMap, anomalyEvent.getKpiValue(), anomalyEvent.getThresholdsMap(), signalDetails.getAccountId());
 
-                boolean insertStatus = signalRepo.insertSignal(newSignal, signalDetails.getAccountId(), anomalySummary);
+                boolean insertStatus = signalRepo.insertSignal(newSignal, signalDetails.getAccountId(), anomalySummary, false, appIds, anomalyEvent.getIsWorkLoad(), anomalySeverityId);
                 if (insertStatus) {
-                    log.info("AI-OPS: MLE signal inserted successfully for the account {} with signalID {}",
-                            signalDetails.getAccountId(),
-                            signalDetails.getSignalId());
+                    log.info("AI-OPS: MLE signal inserted successfully for the anomalyId:{}, accountId:{} with signalID:{}",
+                            anomalyEvent.getAnomalyId(), signalDetails.getAccountId(), signalDetails.getSignalId());
 
                     // TODO: send signal protos to rmq with updated value
-                    forwarder.sendSignalMessages(commons.getSignalProto(signalDetails.getAccountId(), newSignal, false,
+                    forwarder.sendSignalMessages(commons.getSignalProto(newSignal, false,
                             false, false, false, anomalySummary));
-                    log.debug("AIO-PS: push signal protos to rmq. SignalId {}, AccountId {}", newSignal.getSignalId(),
+                    log.debug("AI-OPS: Pushed signal protos to rmq. AnomalyId:{}, SignalId:{}, AccountId:{}", anomalyEvent.getAnomalyId(), newSignal.getSignalId(),
                             signalDetails.getAccountId());
 
                 } else {
-                    log.error("AIO-PS: Fail to insert mle generated signals {}, account {}",
-                            signalDetails.getSignalId(),
-                            signalDetails.getAccountId());
+                    log.error("AI-OPS: Fail to insert mle generated anomalyId:{},signalId:{}, accountId:{}", anomalyEvent.getAnomalyId(),
+                            signalDetails.getSignalId(), signalDetails.getAccountId());
                 }
                 healthMetrics.updateProcessDetails("MLEBasedCreateSignals", System.currentTimeMillis() - startTime);
             } else {
-                log.debug("signal details found in OS is {}", signalById);
-                log.warn("AI-OPS: signals found for the account:{}, signalId:{}", signalDetails.getAccountId(),
-                        signalDetails.getSignalId());
-                if(signalById.getMetadata().containsKey("Source") && signalById.getMetadata().get("Source").equalsIgnoreCase("MLE")){
-                    boolean isSeverityChanged = false;
-                    if(severityId == 295 && signalById.getSeverityId() == 296) {
-                        isSeverityChanged = true;
-                    }
+                log.debug("AI-OPS signal details found in OS is signalId:{}, accountId:{}, anomalyId:{}", signalById, signalDetails.getAccountId(), anomalyEvent.getAnomalyId());
 
-                    boolean isServiceAdded = !signalById.getServiceIds().contains(anomalyEvent.getServiceId());
-
-                    Map<String, String> metadataMap = new HashMap<>(anomalyEvent.getMetadataMap());
-                    metadataMap.put("Source", "MLE");
-
-                    AnomalySummary anomalySummary = commons.getAnomalySummary(anomalyEvent.getInstanceId(), anomalyEvent.getKpiId(),
-                            anomalyEvent.getCategoryId(), anomalyEvent.getServiceId(), SignalType.valueOf(signalDetails.getSignalType()),
-                            severityId, anomalyEvent.getAnomalyTime(), anomalyEvent.getIsWorkLoad(), anomalyEvent.getAnomalyId(),
-                            anomalyEvent.getKpiAttribute(), anomalyEvent.getKpiGroupId(), anomalyEvent.getThresholdType(),
-                            anomalyEvent.getOperationType(),
-                            metadataMap, anomalyEvent.getKpiValue(),
-                            anomalyEvent.getThresholdsMap());
-
-                    log.debug("AI-OPS: related signal for signal id {} is {}", signalDetails.getSignalId(), signalDetails.getRelatedSignalsList().size());
-                    Set<String> relatedSignalList = new HashSet<>(signalDetails.getRelatedSignalsList());
-
-                    boolean updateStatus = signalRepo.updateMLESignal(signalDetails.getUpdateTime(),
-                            signalDetails.getSignalStatus(), "",
-                            metadataMap, anomalyEvent.getAnomalyId(), signalDetails.getSignalId(),
-                            signalDetails.getAccountId(), severityId, anomalySummary,
-                            relatedSignalList, signalById);
-
-                    if(updateStatus) {
-                        log.info("AI-OPS: MLE signal updated successfully for the account {} with signalID {}",
-                                signalDetails.getAccountId(),
-                                signalDetails.getSignalId());
-
-                        // TODO: send signal protos to rmq with updated value
-                        forwarder.sendSignalMessages(commons.getSignalProto(signalDetails.getAccountId(), signalById, false,
-                                isSeverityChanged, isServiceAdded, true, anomalySummary));
-                        log.debug("AI-OPS: push signal protos to rmq. SignalId {}, AccountId {}",
-                                signalDetails.getSignalId(),
-                                signalDetails.getAccountId());
-                    } else {
-                        log.error("AI-OPS: Fail to update mle generated signals {}, account {}",
-                                signalDetails.getSignalId(),
-                                    signalDetails.getAccountId());
-                    }
-                    healthMetrics.updateProcessDetails("MLEBasedUpdateSignals", System.currentTimeMillis() - startTime);
+                if (signalById.getCurrentStatus().equals(SignalStatus.CLOSED.name()) || signalById.getCurrentStatus().equals(SignalStatus.UPGRADED.name()) || signalById.getCurrentStatus().equals(SignalStatus.DOWNGRADED.name())) {
+                    log.warn("AI-OPS Signal {} is already in {} state. No further update allowed on it.", signalById.getSignalId(), signalById.getCurrentStatus());
+                    return;
                 }
-            }
 
+                SignalSummary signalSummary = redisUtilities.getSignalSummary(signalById.getSignalId());
+
+                boolean isSeverityChanged = anomalySeverityId > signalById.getSeverityId();
+                boolean isServiceAdded = !signalById.getServiceIds().containsAll(anomalyEvent.getServiceIdsList());
+                AnomalySummary anomalySummary = commons.getAnomalySummary(anomalyEvent.getInstanceId(), anomalyEvent.getKpiId(),
+                        anomalyEvent.getCategoryId(), new HashSet<>(anomalyEvent.getServiceIdsList()), SignalType.valueOf(signalDetails.getSignalType()),
+                        anomalySeverityId, anomalyEvent.getAnomalyTime(), anomalyEvent.getIsWorkLoad(), anomalyEvent.getAnomalyId(),
+                        anomalyEvent.getKpiAttribute(), anomalyEvent.getKpiGroupId(), anomalyEvent.getThresholdType(),
+                        anomalyEvent.getOperationType(), metadataMap, anomalyEvent.getKpiValue(), anomalyEvent.getThresholdsMap(), signalDetails.getAccountId());
+
+                log.debug("AI-OPS: related signal for signal id {} is {}", signalDetails.getSignalId(), signalDetails.getRelatedSignalsList().size());
+                Set<String> relatedSignalList = new HashSet<>(signalDetails.getRelatedSignalsList());
+
+                boolean updateStatus = signalRepo.updateMLESignal(signalDetails.getUpdateTime(), signalDetails.getSignalStatus(),
+                        metadataMap, anomalyEvent.getAnomalyId(), signalDetails.getSignalId(), signalDetails.getAccountId(),
+                        isSeverityChanged ? anomalySeverityId : signalById.getSeverityId(), anomalySummary, relatedSignalList, signalById, signalSummary.getApplicationIds(), anomalyEvent.getIsWorkLoad(), anomalySeverityId);
+
+                if (updateStatus) {
+                    log.info("AI-OPS: MLE signal updated successfully for the accountId:{} with signalId:{}, anomalyId:{}",
+                            signalDetails.getAccountId(), signalDetails.getSignalId(), anomalyEvent.getAnomalyId());
+
+                    // TODO: send signal protos to rmq with updated value
+                    forwarder.sendSignalMessages(commons.getSignalProto(signalById, false,
+                            isSeverityChanged, isServiceAdded, true, anomalySummary));
+                    log.debug("AI-OPS: push signal protos to rmq. SignalId:{}, AccountId:{}, anomalyId:{}",
+                            signalDetails.getSignalId(), signalDetails.getAccountId(), anomalyEvent.getAnomalyId());
+                } else {
+                    log.error("AI-OPS: Fail to update mle generated signalId:{}, accountId:{}, anomalyId:{}",
+                            signalDetails.getSignalId(), signalDetails.getAccountId(), anomalyEvent.getAnomalyId());
+                }
+                healthMetrics.updateProcessDetails("MLEBasedUpdateSignals", System.currentTimeMillis() - startTime);
+
+            }
 
         } catch (Exception ex) {
             healthMetrics.updateErrors();
diff --git a/src/main/java/com/heal/signal/detector/process/ProblemSignalProcessor.java b/src/main/java/com/heal/signal/detector/process/ProblemSignalProcessor.java
index ef1c2b9..6f43e67 100644
--- a/src/main/java/com/heal/signal/detector/process/ProblemSignalProcessor.java
+++ b/src/main/java/com/heal/signal/detector/process/ProblemSignalProcessor.java
@@ -11,7 +11,10 @@ import com.heal.signal.detector.pojos.SignalChanges;
 import com.heal.signal.detector.pojos.SignalHelper;
 import com.heal.signal.detector.pojos.SignalStatus;
 import com.heal.signal.detector.service.ForwarderToQueue;
-import com.heal.signal.detector.util.*;
+import com.heal.signal.detector.util.Commons;
+import com.heal.signal.detector.util.HealthMetrics;
+import com.heal.signal.detector.util.LocalQueues;
+import com.heal.signal.detector.util.RedisUtilities;
 import lombok.extern.slf4j.Slf4j;
 import org.springframework.beans.factory.annotation.Autowired;
 import org.springframework.stereotype.Component;
@@ -22,6 +25,7 @@ import java.util.stream.Collectors;
 @Component
 @Slf4j
 public class ProblemSignalProcessor {
+
     @Autowired
     Commons commons;
 
@@ -40,64 +44,97 @@ public class ProblemSignalProcessor {
     @Autowired
     CacheWrapper wrapper;
 
+    @Autowired
+    RedisUtilities redisUtilities;
+
     @Autowired
     LocalQueues localQueues;
 
     public void processProblemEvent(AnomalyEventProtos.AnomalyEvent anomalyEvent, BasicKpiEntity kpiEntity, Account account) {
 
         long st = System.currentTimeMillis();
-        Set<SignalDetails> openSignals;
+        Set<SignalDetails> openSignalsFromOS;
         try {
-            openSignals = signalRepo.getOpenSignals(account.getIdentifier(), true);
-        } catch (Exception e) {
-            log.error("Error occurred while fetching open signals so pushing in failed queue will be processing it later " +
-                            "for accountIdentifier: {}, anomalyId: {}",
-                    account.getIdentifier(), anomalyEvent.getAnomalyId(), e);
-            localQueues.addToFailedOpenSignalQueue(FailedOpenSignalPojo.builder().signalType(SignalType.PROBLEM)
-                    .anomalyEvent(anomalyEvent)
-                    .account(account)
-                    .kpiEntity(kpiEntity)
-                    .build());
-            return;
-        }
-        try {
-            String serviceId = anomalyEvent.getKpis().getSvcId(0);
-            Service service = wrapper.getServiceDetailsByIdentifier(anomalyEvent.getAccountId(), serviceId);
-            if (service == null) {
-                log.error("Signal will not be processed. Reason: Invalid service identifier:{}, Anomaly event details:{}", serviceId, anomalyEvent);
+            try {
+                openSignalsFromOS = signalRepo.getOpenSignals(account.getIdentifier(), true, true);
+            } catch (Exception e) {
+                log.error("Error occurred while fetching open signals. So pushing in failed queue. Will be processing it later " +
+                                "for accountIdentifier: {}, anomalyId: {}",
+                        account.getIdentifier(), anomalyEvent.getAnomalyId(), e);
+
+                localQueues.addToFailedOpenSignalQueue(FailedOpenSignalPojo.builder()
+                        .signalType(SignalType.PROBLEM)
+                        .anomalyEvent(anomalyEvent)
+                        .account(account)
+                        .kpiEntity(kpiEntity)
+                        .build());
+
                 return;
             }
 
+            Set<Service> services = anomalyEvent.getKpis().getSvcIdList()
+                    .stream()
+                    .map(serviceIdentifier -> {
+                        Service service = wrapper.getServiceDetailsByIdentifier(anomalyEvent.getAccountId(), serviceIdentifier);
+                        if (service == null) {
+                            log.error("Invalid service identifier, Anomaly event details:{}", anomalyEvent);
+                            metrics.updateErrors();
+                        }
+                        return service;
+                    }).filter(Objects::nonNull)
+                    .collect(Collectors.toSet());
+
+            Set<String> serviceIdentifiers = services.stream().map(Service::getIdentifier).collect(Collectors.toSet());
+
             Map<String, String> metaData = commons.getSignalMetaData(anomalyEvent, SignalType.PROBLEM);
-            Set<SignalDetails> signals = commons.getOpenSignals(account.getIdentifier(), openSignals, serviceId);
+            //NOTE:- Proto will give immutable map. Don't remove new HashMap<>() here, else java.lang.UnsupportedOperationException
+            // will come in case of map.put().
+            Map<String, String> anomalyEventMetadataMap = new HashMap<>(anomalyEvent.getKpis().getMetadataMap());
+
+            Set<ControllerAlias> serviceAliasSet = redisUtilities.getServiceAliases();
+            Map<String, String> dcDrServiceMap = new HashMap<>();
+            Map<String, String> drDcServiceMap = new HashMap<>();
+            if (serviceAliasSet != null && !serviceAliasSet.isEmpty()) {
+                dcDrServiceMap.putAll(serviceAliasSet.stream().collect(Collectors.toMap(ControllerAlias::getDcControllerIdentifier, ControllerAlias::getDrControllerIdentifier)));
+                drDcServiceMap.putAll(serviceAliasSet.stream().collect(Collectors.toMap(ControllerAlias::getDrControllerIdentifier, ControllerAlias::getDcControllerIdentifier)));
+            }
+
+            Set<SignalDetails> signals;
+            try {
+                signals = commons.processAndGetOpenSignals(services, account.getIdentifier(), anomalyEvent.getAnomalyId(),
+                        dcDrServiceMap, drDcServiceMap, openSignalsFromOS, false, new HashSet<>(anomalyEvent.getAppIdList()));
+            } catch (Exception e) {
+                log.error("Error occurred while fetching open signals. So pushing in failed queue. Will be processing it later " +
+                        "for accountIdentifier: {}, anomalyId: {}", account.getIdentifier(), anomalyEvent.getAnomalyId(), e);
+                localQueues.addToFailedOpenSignalQueue(FailedOpenSignalPojo.builder().signalType(SignalType.PROBLEM)
+                        .anomalyEvent(anomalyEvent)
+                        .account(account)
+                        .kpiEntity(kpiEntity)
+                        .build());
 
-            log.debug("Open signals in problem processor, anomaly id:{}, signal ids: {}", anomalyEvent.getAnomalyId(), signals.stream().map(SignalDetails::getSignalId).collect(Collectors.joining()));
-            Set<BasicEntity> destServicesMap = wrapper.getNeighbours(account.getIdentifier(), serviceId);
-            if(destServicesMap.isEmpty()){
-                log.warn("Could not find service neighbours from redis for service {} of the account {}", service, account.getIdentifier());
+                return;
             }
-            /*Set<BasicEntity> destServicesMap = redisUtilities.getNeighbours(account.getIdentifier(), serviceId);*/
-            destServicesMap.forEach(destService -> signals.addAll(commons.getOpenSignals(account.getIdentifier(), openSignals, destService.getIdentifier())));
 
             if (signals.isEmpty()) {
-                log.info("Problem wont be created because the first anomaly event in entry service will be created as early warning.");
+                log.info("Problem won't be created because the first anomaly event in entry service will be created as early warning.");
                 warningProcessor.processEarlyWarningEvent(anomalyEvent, kpiEntity, account);
                 return;
             }
 
-            log.info("Number of signals:{} found for anomalyId:{}", signals.size(), anomalyEvent.getAnomalyId());
+            log.debug("Open signals in problem processor, anomaly id:{}, signal ids: {}", anomalyEvent.getAnomalyId(), signals.stream().map(SignalDetails::getSignalId).collect(Collectors.joining(", ")));
+
             int higherSeverityId = wrapper.getSeverityId("Severe");
 
             SignalHelper ewSignalHelper = new SignalHelper();
             ewSignalHelper.getAnomalies().add(anomalyEvent.getAnomalyId());
             ewSignalHelper.getRcaAnomalies().add(anomalyEvent.getAnomalyId());
-            ewSignalHelper.getRcaServices().add(serviceId);
+            ewSignalHelper.getRcaServices().addAll(serviceIdentifiers);
 
             SignalHelper pSignalHelper = new SignalHelper();
             int severityId = wrapper.getSeverityId(anomalyEvent.getKpis().getThresholdSeverity());
             pSignalHelper.getAnomalies().add(anomalyEvent.getAnomalyId());
             pSignalHelper.getRcaAnomalies().add(anomalyEvent.getAnomalyId());
-            pSignalHelper.getRcaServices().add(serviceId);
+            pSignalHelper.getRcaServices().addAll(serviceIdentifiers);
 
             Set<String> serviceIds = new HashSet<>();
 
@@ -107,17 +144,25 @@ public class ProblemSignalProcessor {
             for (SignalDetails signalDetails : signals) {
 
                 if (anomalyEvent.getEndTimeGMT() < signalDetails.getStartedTime()) {
-                    log.error("We will drop this anomaly because anomaly time:{} is less than the signal id:{}, " +
-                            "start time:{} for anomaly:{}", anomalyEvent.getEndTimeGMT(), signalDetails.getSignalId(), signalDetails.getStartedTime(), anomalyEvent);
+                    log.error("We will drop this anomaly because anomaly time:{} is less than the signal id:{}, start time:{} for anomaly:{}",
+                            anomalyEvent.getEndTimeGMT(), signalDetails.getSignalId(), signalDetails.getStartedTime(), anomalyEvent);
                     isValidAnomaly = false;
                     metrics.updateErrors();
                     break;
                 }
+
                 SignalChanges signalChanges = SignalChanges.builder()
                         .isSeverityChanged(false)
                         .isServiceAdded(false)
                         .build();
 
+                signalDetails.setAccountIdentifiers(new HashSet<>() {{
+                    add(anomalyEvent.getAccountId());
+                    if (signalDetails.getAccountIdentifiers() != null) {
+                        addAll(signalDetails.getAccountIdentifiers());
+                    }
+                }});
+
                 signalDetails.getMetadata().putAll(metaData);
                 if (signalDetails.getSignalType().equalsIgnoreCase(SignalType.EARLY_WARNING.name())) {
                     ewSignalHelper.getSignals().add(signalDetails.getSignalId());
@@ -138,10 +183,10 @@ public class ProblemSignalProcessor {
                     log.trace("Service ids:{} for signalId:{}", signalDetails.getServiceIds(), signalDetails.getSignalId());
                     serviceIds.addAll(signalDetails.getServiceIds());
 
-                    signalChanges.setServiceAdded(!signalDetails.getServiceIds().contains(serviceId));
+                    signalChanges.setServiceAdded(!signalDetails.getServiceIds().containsAll(serviceIdentifiers));
                     ewSignalHelper.getSignalDetailChanges().put(signalDetails.getSignalId(), signalChanges);
                     ewSignalHelper.getSignalDetailsMap().put(signalDetails.getSignalId(), signalDetails);
-                } else if (signalDetails.getServiceIds().contains(serviceId)) {
+                } else if (signalDetails.getServiceIds().containsAll(serviceIdentifiers)) {
                     pSignalHelper.getSignals().add(signalDetails.getSignalId());
                     // Check the signal is updating from default to severe
                     if (higherSeverityId != signalDetails.getSeverityId() && higherSeverityId == severityId) {
@@ -149,7 +194,7 @@ public class ProblemSignalProcessor {
                         signalChanges.setSeverityChanged(true);
                     }
 
-                    signalChanges.setServiceAdded(!signalDetails.getServiceIds().contains(serviceId));
+                    signalChanges.setServiceAdded(!signalDetails.getServiceIds().containsAll(serviceIdentifiers));
 
                     pSignalHelper.getSignalDetailChanges().put(signalDetails.getSignalId(), signalChanges);
                     pSignalHelper.getSignalDetailsMap().put(signalDetails.getSignalId(), signalDetails);
@@ -160,27 +205,30 @@ public class ProblemSignalProcessor {
                 return;
             }
 
-            serviceIds.add(serviceId);
+            serviceIds.addAll(serviceIdentifiers);
 
             Set<String> problems = new HashSet<>();
 
             // Create a problem
             if (pSignalHelper.getSignals().isEmpty()) {
+                int servicesHashCode = String.join("-", serviceIdentifiers).hashCode();
+
+                String signalId = commons.createSignalId("P", account.getId(), Integer.parseInt(anomalyEvent.getKpis().getKpiId()), servicesHashCode, anomalyEvent.getEndTimeGMT() / 1000);
 
-                String signalId = commons.createSignalId("P", account.getId(), Integer.parseInt(anomalyEvent.getKpis().getKpiId()), service.getId(), anomalyEvent.getEndTimeGMT() / 1000);
                 log.info("No problem signal is found. So, we will create a new problem signal for anomalyId:{}, accountId:{}, serviceId:{}, serviceIds size:{}, signalId:{}.",
-                        anomalyEvent.getAnomalyId(), anomalyEvent.getAccountId(), serviceId, serviceIds.size(), signalId);
+                        anomalyEvent.getAnomalyId(), anomalyEvent.getAccountId(), serviceIdentifiers, serviceIds.size(), signalId);
+
                 SignalDetails signalDetails = commons.createSignal(signalId, anomalyEvent.getEndTimeGMT(), anomalyEvent.getEndTimeGMT(), anomalyEvent.getEndTimeGMT(), SignalStatus.OPEN.name(),
                         ewSignalHelper.getRcaAnomalies(), SignalType.PROBLEM, serviceIds, ewSignalHelper.getRcaServices(),
-                        ewSignalHelper.getAnomalies(), serviceId, ewSignalHelper.getSignals(), severityId, metaData);
+                        ewSignalHelper.getAnomalies(), serviceIdentifiers, ewSignalHelper.getSignals(), severityId, metaData, anomalyEvent.getAccountId());
 
                 AnomalySummary anomalySummary = commons.getAnomalySummary(anomalyEvent.getKpis().getInstanceId(), kpiEntity.getIdentifier(),
-                        kpiEntity.getCategoryDetails().getIdentifier(), service.getIdentifier(), SignalType.PROBLEM,
+                        kpiEntity.getCategoryDetails().getIdentifier(), serviceIdentifiers, SignalType.PROBLEM,
                         severityId, anomalyEvent.getEndTimeGMT(), true, anomalyEvent.getAnomalyId(),
                         anomalyEvent.getKpis().getKpiAttribute(), kpiEntity.getGroupIdentifier(), anomalyEvent.getThresholdType(), anomalyEvent.getOperationType(),
-                        anomalyEvent.getKpis().getMetadataMap(), anomalyEvent.getKpis().getValue(), anomalyEvent.getKpis().getThresholdsMap());
+                        anomalyEventMetadataMap, anomalyEvent.getKpis().getValue(), anomalyEvent.getKpis().getThresholdsMap(), anomalyEvent.getAccountId());
 
-                boolean insertStatus = signalRepo.insertSignal(signalDetails, anomalyEvent.getAccountId(), anomalySummary);
+                boolean insertStatus = signalRepo.insertSignal(signalDetails, anomalyEvent.getAccountId(), anomalySummary, true, new HashSet<>(anomalyEvent.getAppIdList()), true, Integer.parseInt(anomalyEvent.getKpis().getThresholdSeverity()));
                 boolean queueUpdateStatus = signalRepo.updateAnomaly(anomalyEvent.getAnomalyId(), anomalyEvent.getEndTimeGMT(),
                         Collections.singleton(signalId), anomalyEvent.getAccountId());
                 if (queueUpdateStatus) {
@@ -193,7 +241,7 @@ public class ProblemSignalProcessor {
                 metrics.updateSignalOpenCount(1);
                 metrics.updateSnapshots(signalDetails.getSignalId() + "_CREATED", 1);
                 if (insertStatus) {
-                    forwarder.sendSignalMessages(commons.getSignalProto(account.getIdentifier(), signalDetails, false,
+                    forwarder.sendSignalMessages(commons.getSignalProto(signalDetails, false,
                             false, false, false, anomalySummary));
                 }
                 log.info("Problem signal is created for anomalyId:{}, signal details:{}", anomalyEvent.getAnomalyId(), signalDetails);
@@ -205,14 +253,14 @@ public class ProblemSignalProcessor {
                     SignalChanges signalChanges = pSignalHelper.getSignalDetailChanges().get(pSignalId);
 
                     AnomalySummary anomalySummary = commons.getAnomalySummary(anomalyEvent.getKpis().getInstanceId(), kpiEntity.getIdentifier(),
-                            kpiEntity.getCategoryDetails().getIdentifier(), service.getIdentifier(), SignalType.PROBLEM,
+                            kpiEntity.getCategoryDetails().getIdentifier(), serviceIdentifiers, SignalType.PROBLEM,
                             signalChanges.isSeverityChanged() ? severityId : signalDetails.getSeverityId(), anomalyEvent.getEndTimeGMT(), true, anomalyEvent.getAnomalyId(),
                             anomalyEvent.getKpis().getKpiAttribute(), kpiEntity.getGroupIdentifier(), anomalyEvent.getThresholdType(), anomalyEvent.getOperationType(),
-                            anomalyEvent.getKpis().getMetadataMap(), anomalyEvent.getKpis().getValue(), anomalyEvent.getKpis().getThresholdsMap());
+                            anomalyEventMetadataMap, anomalyEvent.getKpis().getValue(), anomalyEvent.getKpis().getThresholdsMap(), anomalyEvent.getAccountId());
 
                     boolean updateStatus = signalRepo.updateSignal(anomalyEvent.getEndTimeGMT(), SignalStatus.OPEN.name(), SignalStatus.OPEN.name(),
                             signalDetails.getMetadata(), anomalyEvent.getAnomalyId(), pSignalId, account.getIdentifier(),
-                            signalDetails.getSeverityId(), anomalySummary, signalDetails.getStartedTime());
+                            signalDetails.getSeverityId(), anomalySummary, signalDetails.getStartedTime(), true, new HashSet<>(anomalyEvent.getAppIdList()), true, Integer.parseInt(anomalyEvent.getKpis().getThresholdSeverity()));
                     boolean queueUpdateStatus = signalRepo.updateAnomaly(anomalyEvent.getAnomalyId(), anomalyEvent.getEndTimeGMT(),
                             Collections.singleton(pSignalId), account.getIdentifier());
                     if (queueUpdateStatus) {
@@ -224,7 +272,7 @@ public class ProblemSignalProcessor {
                     metrics.updateSnapshots(signalDetails.getSignalId() + "_UPDATED", 1);
 
                     if (updateStatus) {
-                        forwarder.sendSignalMessages(commons.getSignalProto(account.getIdentifier(), signalDetails, false,
+                        forwarder.sendSignalMessages(commons.getSignalProto(signalDetails, false,
                                 signalChanges.isSeverityChanged(), signalChanges.isServiceAdded(), false, anomalySummary));
                     }
                     log.info("Problem signal is updated for anomalyId:{}, signal details:{}", anomalyEvent.getAnomalyId(), signalDetails);
@@ -254,7 +302,7 @@ public class ProblemSignalProcessor {
                 metrics.updateSnapshots(signalDetails.getSignalId() + "_UPDATED", 1);
 
                 if (updateStatus) {
-                    forwarder.sendSignalMessages(commons.getSignalProto(account.getIdentifier(), signalDetails, true,
+                    forwarder.sendSignalMessages(commons.getSignalProto(signalDetails, true,
                             signalChanges.isSeverityChanged(), signalChanges.isServiceAdded(), true, null));
                 }
                 log.info("PSP:Early warning signal id:{} is upgraded to problem id:{} for anomalyId:{}, signal details:{}", signalDetails.getSignalId(), problems, anomalyEvent.getAnomalyId(), signalDetails);
diff --git a/src/main/java/com/heal/signal/detector/process/ProtoValidation.java b/src/main/java/com/heal/signal/detector/process/ProtoValidation.java
index c4406f7..efa76c6 100644
--- a/src/main/java/com/heal/signal/detector/process/ProtoValidation.java
+++ b/src/main/java/com/heal/signal/detector/process/ProtoValidation.java
@@ -32,7 +32,6 @@ public class ProtoValidation {
     @Autowired
     CustomThreadExecutionService customThreadExecutionService;
 
-    //    @Async("ThreadPoolTaskExecutor")
     public void validateAndProcessInputAnomalyEvent(AnomalyEventProtos.AnomalyEvent anomalyEvent) {
         try {
             if (anomalyEvent == null) {
@@ -111,7 +110,7 @@ public class ProtoValidation {
                 mapKey = String.valueOf(anomalyEvent.getAppIdList().stream().sorted().collect(Collectors.toCollection(LinkedHashSet::new)).hashCode());
             }
 
-            if(customThreadExecutionService.getThreadPoolCoreSize() > 0) {
+            if (customThreadExecutionService.getThreadPoolCoreSize() > 0) {
                 customThreadExecutionService.executeOnAppThread(mapKey, () -> signalHandler.processAnomalyEvent(anomalyEvent));
             } else {
                 signalHandler.processAnomalyEvent(anomalyEvent);
@@ -124,27 +123,27 @@ public class ProtoValidation {
 
     public void validateAndProcessMLESignals(SignalProtos.SignalDetails signalDetails) {
         try {
-            if(signalDetails == null) {
+            if (signalDetails == null) {
                 log.error("AI-OPS: Signal details is null.");
                 metrics.updateErrors();
                 return;
             }
 
-            if(signalDetails.getSignalId().trim().isEmpty()) {
+            if (signalDetails.getSignalId().trim().isEmpty()) {
                 log.error("AI-OPS: Invalid mle generated signal/incidents is received. Signal details:{}",
                         signalDetails);
                 metrics.updateErrors();
                 return;
             }
 
-            if(signalDetails.getAccountId().trim().isEmpty()) {
+            if (signalDetails.getAccountId().trim().isEmpty()) {
                 log.error("AI-OPS: Invalid mle generated signal/incidents is received. No account identifier found " +
                         "in Signal details:{}", signalDetails);
                 metrics.updateErrors();
                 return;
             }
 
-            if(signalDetails.getAnomalyDetailsList().isEmpty()) {
+            if (signalDetails.getAnomalyDetailsList().isEmpty()) {
                 log.error("AI-OPS: Invalid mle generated signal/incidents is received, No anomaly list found in signal details: {}", signalDetails);
                 return;
             }
@@ -153,21 +152,21 @@ public class ProtoValidation {
                     signalDetails.getAnomalyDetailsList().size());
 
 
-            if(signalDetails.getSignalType().trim().isEmpty()) {
+            if (signalDetails.getSignalType().trim().isEmpty()) {
                 log.error("AI-OPS: Invalid mle generated signal/incidents is received. Signal has empty signal type ." +
                         " Signal details:{}", signalDetails);
                 metrics.updateErrors();
                 return;
             }
 
-            if(signalDetails.getSignalStatus().trim().isEmpty()) {
+            if (signalDetails.getSignalStatus().trim().isEmpty()) {
                 log.error("AI-OPS: Invalid mle generated signal/incidents is received. Signal has empty signal " +
                         "status . Signal details:{}", signalDetails);
                 metrics.updateErrors();
                 return;
             }
 
-            if(signalDetails.getMetadataCount() == 0) {
+            if (signalDetails.getMetadataCount() == 0) {
                 log.error("AI-OPS: Invalid mle generated signal/incidents is received. Signal has no metadata . " +
                         "Signal details:{}", signalDetails);
                 metrics.updateErrors();
diff --git a/src/main/java/com/heal/signal/detector/process/SignalDetailProcessor.java b/src/main/java/com/heal/signal/detector/process/SignalDetailProcessor.java
index 5e72243..afae8b8 100644
--- a/src/main/java/com/heal/signal/detector/process/SignalDetailProcessor.java
+++ b/src/main/java/com/heal/signal/detector/process/SignalDetailProcessor.java
@@ -1,8 +1,8 @@
 package com.heal.signal.detector.process;
 
 import com.heal.configuration.pojos.*;
+import com.heal.configuration.pojos.opensearch.SignalDetails;
 import com.heal.signal.detector.util.HealthMetrics;
-import com.heal.signal.detector.cache.LocalCache;
 import com.heal.signal.detector.util.RedisUtilities;
 import lombok.extern.slf4j.Slf4j;
 import org.springframework.beans.factory.annotation.Autowired;
@@ -25,130 +25,234 @@ public class SignalDetailProcessor {
     @Autowired
     RedisUtilities redisUtilities;
 
-    @Autowired
-    LocalCache cache;
-
     @Value("${signal.latest.events.count:10}")
     public int lastNEvents;
 
-    public void updateSignalDetailsIntoRedis(String accountId, String signalId, AnomalySummary anomalySummary) {
+    public void updateSignalDetailsIntoRedis(String signalId, AnomalySummary anomalySummary, Set<String> appIds, boolean isWorkload, int anomalitySeverityId) {
         try {
-            if(anomalySummary == null) {
-                log.error("Anomaly summary is null for signalId:{}, accountId:{}", signalId, accountId);
+            if (anomalySummary == null) {
+                log.error("Anomaly summary is null for signalId:{}", signalId);
                 return;
             }
-            SignalSummary signalSummary = redisUtilities.getSignalDetails(accountId, signalId);
-            if(signalSummary == null) {
+
+            SignalSummary signalSummary = redisUtilities.getSignalSummary(signalId);
+            if (signalSummary == null) {
+                SignalSummary.AnomalySummary anomaly = SignalSummary.AnomalySummary.builder()
+                        .anomalyId(anomalySummary.getAnomalyId())
+                        .isWorkload(isWorkload)
+                        .severityId(anomalitySeverityId)
+                        .build();
+
+                Set<SignalSummary.AnomalySummary> anomalySummaries = new HashSet<>();
+                anomalySummaries.add(anomaly);
+
                 signalSummary = SignalSummary.builder()
                         .signalId(signalId)
                         .startTime(anomalySummary.getEventTime())
                         .latestEventTime(anomalySummary.getEventTime())
-                        .anomalies(1)
+                        .anomalies(anomalySummaries)
+                        .signalType(anomalySummary.getSignalType().name())
+                        .severityId(anomalySummary.getSeverityId())
+                        .applicationIds(appIds)
                         .build();
             } else {
+                signalSummary.getAnomalies().removeIf(a -> a.getAnomalyId().equals(anomalySummary.getAnomalyId()));
+                SignalSummary.AnomalySummary anomaly = SignalSummary.AnomalySummary.builder()
+                        .anomalyId(anomalySummary.getAnomalyId())
+                        .isWorkload(isWorkload)
+                        .severityId(anomalitySeverityId)
+                        .build();
+                signalSummary.setSignalType(anomalySummary.getSignalType().name());
+                signalSummary.setSeverityId(anomalySummary.getSeverityId());
                 signalSummary.setLatestEventTime(anomalySummary.getEventTime());
-                signalSummary.setAnomalies(signalSummary.getAnomalies() +1);
+                signalSummary.getAnomalies().add(anomaly);
             }
 
-            Set<AnomalySummary> anomalySummaries = redisUtilities.getSignalAnomalies(accountId, signalId);
+            Set<AnomalySummary> anomalySummaries = redisUtilities.getSignalAnomalies(signalId);
             anomalySummaries.add(anomalySummary);
 
-            Set<AnomalySummary> topNAnomalies = anomalySummaries
-                    .stream()
+            Set<AnomalySummary> topNAnomalies = anomalySummaries.stream()
                     .sorted(Comparator.comparing(AnomalySummary::getEventTime, Comparator.reverseOrder()))
                     .limit(lastNEvents)
                     .collect(Collectors.toSet());
 
-            redisUtilities.updateAnomalyForSignal(accountId, signalId, topNAnomalies);
-            String serviceId = anomalySummary.getServiceId();
+            redisUtilities.updateAnomalyForSignal(signalId, topNAnomalies);
 
-            Map<String, SignalServiceSummary> servicesSummaryMap = redisUtilities.getSignalServiceSummary(accountId, signalId)
-                    .stream()
+            Map<String, SignalServiceSummary> servicesSummaryMap = redisUtilities.getSignalServiceSummary(signalId).stream()
                     .collect(Collectors.toMap(SignalServiceSummary::getServiceIdentifier, Function.identity()));
             log.trace("Signal summary, SignalId:{}, map: {}", signalId, servicesSummaryMap.keySet());
 
-            SignalServiceSummary serviceSummary = servicesSummaryMap.getOrDefault(serviceId, new SignalServiceSummary());
-            serviceSummary.setServiceIdentifier(serviceId);
-            if(anomalySummary.getSevereAnomalyId() != null) {
-                serviceSummary.getSevereAnomalies().add(anomalySummary.getAnomalyId());
-                signalSummary.setSevereAnomalies(signalSummary.getSevereAnomalies() +1);
-            } else {
-                serviceSummary.getDefaultAnomalies().add(anomalySummary.getAnomalyId());
-                signalSummary.setDefaultAnomalies(signalSummary.getDefaultAnomalies() + 1);
-            }
-            if(anomalySummary.isTxnAnomaly()) {
-                serviceSummary.getRequests().add(anomalySummary.getTxnId());
-            } else {
-                serviceSummary.getInstances().add(anomalySummary.getInstanceId());
-            }
-            serviceSummary.getCategories().put(anomalySummary.getKpiId(), anomalySummary.getCategoryId());
-            serviceSummary.setLatestAnomalyTimeInGMT(anomalySummary.getEventTime());
-            servicesSummaryMap.put(serviceId, serviceSummary);
+            Set<String> serviceIds = anomalySummary.getServiceId();
 
-            Set<SignalServiceSummary> serviceSummaries = new HashSet<>(servicesSummaryMap.values());
-            redisUtilities.updateServicesSummaryForSignal(accountId, signalId, serviceSummaries);
+            SignalSummary finalSignalSummary = signalSummary;
+            serviceIds.forEach(serviceId -> {
+                SignalServiceSummary serviceSummary = servicesSummaryMap.getOrDefault(serviceId, new SignalServiceSummary());
+                serviceSummary.setServiceIdentifier(serviceId);
 
-            if(anomalySummary.isTxnAnomaly() && anomalySummary.getTxnId() != null && !anomalySummary.getTxnId().trim().isEmpty()) {
+                if (anomalySummary.getSevereAnomalyId() != null) {
+                    serviceSummary.getSevereAnomalies().add(anomalySummary.getAnomalyId());
+                } else {
+                    serviceSummary.getDefaultAnomalies().add(anomalySummary.getAnomalyId());
+                }
 
-                Map<String, SignalTransactionSummary> txnsSummaryMap = redisUtilities.getSignalTransactionSummary(accountId, signalId)
-                        .stream()
+                if (anomalySummary.isTxnAnomaly()) {
+                    serviceSummary.getRequests().add(anomalySummary.getTxnId());
+                } else {
+                    serviceSummary.getInstances().add(anomalySummary.getInstanceId());
+                }
+
+                serviceSummary.getCategories().put(anomalySummary.getKpiId(), anomalySummary.getCategoryId());
+                serviceSummary.setLatestAnomalyTimeInGMT(anomalySummary.getEventTime());
+
+                servicesSummaryMap.put(serviceId, serviceSummary);
+            });
+
+            redisUtilities.updateServicesSummaryForSignal(signalId, new HashSet<>(servicesSummaryMap.values()));
+
+            if (anomalySummary.isTxnAnomaly() && anomalySummary.getTxnId() != null && !anomalySummary.getTxnId().trim().isEmpty()) {
+                Map<String, SignalTransactionSummary> txnsSummaryMap = redisUtilities.getSignalTransactionSummary(signalId).stream()
                         .collect(Collectors.toMap(SignalTransactionSummary::getTransactionIdentifier, Function.identity()));
                 log.trace("Transactions summary, SignalId:{}, map: {}", signalId, txnsSummaryMap);
 
-                SignalTransactionSummary txnSummary = txnsSummaryMap.getOrDefault(anomalySummary.getTxnId(), new SignalTransactionSummary());
-                txnSummary.setTransactionIdentifier(anomalySummary.getTxnId());
-                txnSummary.setServiceIdentifier(anomalySummary.getServiceId());
-                if(anomalySummary.getSevereAnomalyId() != null) {
-                    txnSummary.getSevereAnomalies().add(anomalySummary.getAnomalyId());
-                } else {
-                    txnSummary.getDefaultAnomalies().add(anomalySummary.getAnomalyId());
-                }
-                txnSummary.getKpiCategories().put(anomalySummary.getKpiId(), anomalySummary.getCategoryId());
-                txnSummary.getKpiLatestTime().put(anomalySummary.getKpiId(), anomalySummary.getEventTime());
-                txnSummary.getKpiSeverity().put(anomalySummary.getKpiId(), anomalySummary.getSeverityId()+"");
-                txnSummary.setLatestEventTimeInGMT(anomalySummary.getEventTime());
-                txnSummary.setServiceIdentifier(anomalySummary.getServiceId());
-                txnsSummaryMap.put(anomalySummary.getTxnId(), txnSummary);
-                redisUtilities.updateTransactionsSummaryForSignal(accountId, signalId, new HashSet<>(txnsSummaryMap.values()));
+                serviceIds.forEach(serviceId -> {
+                    SignalTransactionSummary txnSummary = txnsSummaryMap.getOrDefault(anomalySummary.getTxnId(), new SignalTransactionSummary());
+                    txnSummary.setTransactionIdentifier(anomalySummary.getTxnId());
+                    txnSummary.setServiceIdentifier(serviceId);
+
+                    if (anomalySummary.getSevereAnomalyId() != null) {
+                        txnSummary.getSevereAnomalies().add(anomalySummary.getAnomalyId());
+                    } else {
+                        txnSummary.getDefaultAnomalies().add(anomalySummary.getAnomalyId());
+                    }
+
+                    txnSummary.getKpiCategories().put(anomalySummary.getKpiId(), anomalySummary.getCategoryId());
+                    txnSummary.getKpiLatestTime().put(anomalySummary.getKpiId(), anomalySummary.getEventTime());
+                    txnSummary.getKpiSeverity().put(anomalySummary.getKpiId(), anomalySummary.getSeverityId() + "");
+                    txnSummary.setLatestEventTimeInGMT(anomalySummary.getEventTime());
+
+                    txnsSummaryMap.put(anomalySummary.getTxnId(), txnSummary);
+                });
+
+                redisUtilities.updateTransactionsSummaryForSignal(signalId, new HashSet<>(txnsSummaryMap.values()));
 
             } else {
 
-                Map<String, SignalInstanceSummary> instsSummaryMap = redisUtilities.getSignalInstanceSummary(accountId, signalId)
-                        .stream()
+                Map<String, SignalInstanceSummary> instsSummaryMap = redisUtilities.getSignalInstanceSummary(signalId).stream()
                         .collect(Collectors.toMap(SignalInstanceSummary::getInstanceIdentifier, Function.identity()));
                 log.trace("Instances summary, SignalId:{}, map: {}", signalId, instsSummaryMap.keySet());
 
                 SignalInstanceSummary instanceSummary = instsSummaryMap.getOrDefault(anomalySummary.getInstanceId(), new SignalInstanceSummary());
                 instanceSummary.setInstanceIdentifier(anomalySummary.getInstanceId());
-                if(anomalySummary.getSevereAnomalyId() != null) {
+
+                if (anomalySummary.getSevereAnomalyId() != null) {
                     instanceSummary.getSevereAnomalies().add(anomalySummary.getAnomalyId());
                 } else {
                     instanceSummary.getDefaultAnomalies().add(anomalySummary.getAnomalyId());
                 }
+
                 instanceSummary.getKpiCategories().put(anomalySummary.getKpiId(), anomalySummary.getCategoryId());
                 instanceSummary.getKpiLatestTime().put(anomalySummary.getKpiId(), anomalySummary.getEventTime());
-                instanceSummary.getKpiSeverity().put(anomalySummary.getKpiId(), anomalySummary.getSeverityId()+"");
+                instanceSummary.getKpiSeverity().put(anomalySummary.getKpiId(), anomalySummary.getSeverityId() + "");
                 instanceSummary.setLatestEventTimeInGMT(anomalySummary.getEventTime());
+
                 instsSummaryMap.put(anomalySummary.getInstanceId(), instanceSummary);
 
-                redisUtilities.updateInstancesSummaryForSignal(accountId, signalId, new HashSet<>(instsSummaryMap.values()));
+                redisUtilities.updateInstancesSummaryForSignal(signalId, new HashSet<>(instsSummaryMap.values()));
             }
 
             log.trace("Signal signalId:{}", signalId);
-            redisUtilities.updateSignalDetails(accountId, signalId, signalSummary);
+
+            redisUtilities.updateSignalSummary(signalId, signalSummary);
         } catch (Exception e) {
             metrics.updateErrors();
             log.error("Error occurred while updating signal summary details into redis. signalId:{}, anomalySummary:{}", signalId, anomalySummary, e);
         }
     }
 
-    public void updateServiceSignals(String accountId, String signalId, String serviceId, long signalStartTime, String signalStatus) {
+    public void updateServiceSignals(String signalId, Set<String> serviceId, long signalStartTime, String signalStatus, String appIdentifier) {
+        try {
+            redisUtilities.updateServiceOpenSignalsForApp(signalId, signalStartTime, serviceId, signalStatus, appIdentifier);
+        } catch (Exception e) {
+            metrics.updateErrors();
+            log.error("Error occurred while updating service signal into redis. signalId:{}, serviceId:{}, signalStatus:{}",
+                    signalId, serviceId, signalStatus, e);
+        }
+    }
+
+    public void updateSignalDetails(SignalDetails signalDetail, boolean isDelete) {
         try {
-            redisUtilities.updateServiceSignal(accountId, signalId, signalStartTime, serviceId, signalStatus);
+            if (!isDelete) {
+                redisUtilities.updateSignalDetails(signalDetail);
+            } else {
+                redisUtilities.deleteSignalDetails(signalDetail.getSignalId());
+            }
         } catch (Exception e) {
             metrics.updateErrors();
-            log.error("Error occurred while updating service signal into redis. accountId:{}, signalId:{}, serviceId:{}, signalStatus:{}",
-                    accountId, signalId, serviceId, signalStatus, e);
+            log.error("Error occurred while updating signal details into redis. signalId:{}", signalDetail.getSignalId(), e);
+        }
+    }
+
+    /**
+     * Removes an anomaly from Redis for the given signalId and anomalyId.
+     * Updates summaries and cleans up if needed.
+     * 
+     * @param signalId The ID of the signal from which the anomaly should be removed.
+     * @param anomalyId The ID of the anomaly to be removed.
+     */
+    public void removeAnomalyFromSignalInRedis(String signalId, String anomalyId) {
+        try {
+            // Remove anomaly from signal summary
+            SignalSummary signalSummary = redisUtilities.getSignalSummary(signalId);
+            if (signalSummary != null) {
+                signalSummary.getAnomalies().removeIf(a -> a.getAnomalyId().equals(anomalyId));
+                // Optionally update latestEventTime if needed
+                // ...additional logic if required...
+            }
+
+            // Remove anomaly from anomaly summaries
+            Set<AnomalySummary> anomalySummaries = redisUtilities.getSignalAnomalies(signalId);
+            anomalySummaries.removeIf(a -> a.getAnomalyId().equals(anomalyId));
+
+            // Update topNAnomalies
+            Set<AnomalySummary> topNAnomalies = anomalySummaries.stream()
+                    .sorted(Comparator.comparing(AnomalySummary::getEventTime, Comparator.reverseOrder()))
+                    .limit(lastNEvents)
+                    .collect(Collectors.toSet());
+            redisUtilities.updateAnomalyForSignal(signalId, topNAnomalies);
+
+            // Remove anomaly from service summaries
+            Map<String, SignalServiceSummary> servicesSummaryMap = redisUtilities.getSignalServiceSummary(signalId).stream()
+                    .collect(Collectors.toMap(SignalServiceSummary::getServiceIdentifier, Function.identity()));
+            servicesSummaryMap.values().forEach(serviceSummary -> {
+                serviceSummary.getSevereAnomalies().remove(anomalyId);
+                serviceSummary.getDefaultAnomalies().remove(anomalyId);
+                // Optionally update latestAnomalyTimeInGMT if needed
+            });
+            redisUtilities.updateServicesSummaryForSignal(signalId, new HashSet<>(servicesSummaryMap.values()));
+
+            //TODO: check anomaly type and then remove
+
+            // Remove anomaly from instance summaries
+            Map<String, SignalInstanceSummary> instsSummaryMap = redisUtilities.getSignalInstanceSummary(signalId).stream()
+                    .collect(Collectors.toMap(SignalInstanceSummary::getInstanceIdentifier, Function.identity()));
+            instsSummaryMap.values().forEach(instanceSummary -> {
+                instanceSummary.getSevereAnomalies().remove(anomalyId);
+                instanceSummary.getDefaultAnomalies().remove(anomalyId);
+            });
+            redisUtilities.updateInstancesSummaryForSignal(signalId, new HashSet<>(instsSummaryMap.values()));
+
+            // Remove anomaly from transaction summaries
+            Map<String, SignalTransactionSummary> txnsSummaryMap = redisUtilities.getSignalTransactionSummary(signalId).stream()
+                    .collect(Collectors.toMap(SignalTransactionSummary::getTransactionIdentifier, Function.identity()));
+            txnsSummaryMap.values().forEach(txnSummary -> {
+                txnSummary.getSevereAnomalies().remove(anomalyId);
+                txnSummary.getDefaultAnomalies().remove(anomalyId);
+            });
+            redisUtilities.updateTransactionsSummaryForSignal(signalId, new HashSet<>(txnsSummaryMap.values()));
+
+            log.info("Removed anomalyId:{} from signalId:{} in Redis.", anomalyId, signalId);
+        } catch (Exception e) {
+            log.error("Error while removing anomalyId:{} from signalId:{} in Redis.", anomalyId, signalId, e);
         }
     }
 }
diff --git a/src/main/java/com/heal/signal/detector/process/SignalHandler.java b/src/main/java/com/heal/signal/detector/process/SignalHandler.java
index cd70e8d..387c0ac 100644
--- a/src/main/java/com/heal/signal/detector/process/SignalHandler.java
+++ b/src/main/java/com/heal/signal/detector/process/SignalHandler.java
@@ -1,26 +1,29 @@
 package com.heal.signal.detector.process;
 
-
 import com.appnomic.appsone.common.protbuf.AnomalyEventProtos;
+import com.heal.configuration.enums.SignalType;
 import com.heal.configuration.pojos.*;
+import com.heal.configuration.pojos.opensearch.Anomalies;
+import com.heal.configuration.pojos.opensearch.SignalDetails;
 import com.heal.signal.detector.cache.CacheWrapper;
+import com.heal.signal.detector.opensearch.SignalRepo;
+import com.heal.signal.detector.pojos.SignalStatus;
+import com.heal.signal.detector.service.ForwarderToQueue;
 import com.heal.signal.detector.util.Commons;
+import com.heal.signal.detector.util.Constants;
 import com.heal.signal.detector.util.HealthMetrics;
 import com.heal.signal.detector.util.RedisUtilities;
 import lombok.extern.slf4j.Slf4j;
 import org.springframework.beans.factory.annotation.Autowired;
 import org.springframework.beans.factory.annotation.Value;
-import org.springframework.stereotype.Service;
 
 import java.util.*;
+import java.util.stream.Collectors;
 
-@Service
+@org.springframework.stereotype.Service
 @Slf4j
 public class SignalHandler {
 
-    @Autowired
-    RedisUtilities redisUtilities;
-
     @Autowired
     Commons commons;
 
@@ -33,6 +36,9 @@ public class SignalHandler {
     @Autowired
     ProblemSignalProcessor problemProcessor;
 
+    @Autowired
+    GenericSignalProcessor genericSignalProcessor;
+
     @Autowired
     InfoSignalProcessor infoProcessor;
 
@@ -42,34 +48,55 @@ public class SignalHandler {
     @Autowired
     CacheWrapper wrapper;
 
+    @Autowired
+    SignalRepo signalRepo;
+
+    @Autowired
+    RedisUtilities redisUtilities;
+
+    @Autowired
+    ForwarderToQueue forwarder;
+
     @Value("${offset.from.gmt:19800000}")
     private long offsetFromGMT;
 
+    @Value("${signal.severity.id.high:433}")
+    String highSeverityIdSignal;
+
+    @Value("${signal.severity.id.medium:432}")
+    String mediumSeverityIdSignal;
+
+    @Value("${signal.severity.id.low:431}")
+    String lowSeverityIdSignal;
+
+
     public void processAnomalyEvent(AnomalyEventProtos.AnomalyEvent anomalyEvent) {
         log.trace("Anomaly event:{}", anomalyEvent.getAnomalyId());
         long st = System.currentTimeMillis();
-        try {
-
-            Account account = wrapper.getAccounts().parallelStream().filter(f -> f.getIdentifier().equalsIgnoreCase(anomalyEvent.getAccountId())).findAny().orElse(null);
 
+        try {
+            Account account = wrapper.getAccounts().parallelStream()
+                    .filter(f -> f.getIdentifier().equals(anomalyEvent.getAccountId()))
+                    .findAny()
+                    .orElse(null);
             if (account == null) {
                 log.error("Invalid account identifier, Anomaly event details:{}", anomalyEvent);
                 return;
             }
 
-            boolean isTxnData = anomalyEvent.getKpis().getIsWorkload();
+            boolean isWorkload = anomalyEvent.getKpis().getIsWorkload();
 
             CompInstKpiEntity kpiEntity = null;
             if (!anomalyEvent.getKpis().getKpiId().trim().isEmpty()) {
                 int kpiId = Integer.parseInt(anomalyEvent.getKpis().getKpiId());
                 kpiEntity = wrapper.getInstanceKPIDetails(anomalyEvent.getAccountId(), anomalyEvent.getKpis().getInstanceId(), kpiId);
             }
-
             log.debug("KPI data:{}", kpiEntity);
 
             // Check instance wise maintenance window for component instance kpis
-            if (!isTxnData && kpiEntity != null && kpiEntity.getIsMaintenanceExcluded() == 0) {
-                List<MaintenanceDetails> maintenanceDetailsList = wrapper.getInstanceMaintenanceDetails(anomalyEvent.getAccountId(), anomalyEvent.getKpis().getInstanceId());
+            if (!isWorkload && kpiEntity != null && kpiEntity.getIsMaintenanceExcluded() == 0) {
+                List<MaintenanceDetails> maintenanceDetailsList =
+                        wrapper.getInstanceMaintenanceDetails(anomalyEvent.getAccountId(), anomalyEvent.getKpis().getInstanceId());
                 boolean isUnderMaintenance = commons.isUnderMaintenance(maintenanceDetailsList, anomalyEvent.getEndTimeGMT() + offsetFromGMT);
                 if (isUnderMaintenance) {
                     metrics.updateMaintenanceEvents();
@@ -77,14 +104,14 @@ public class SignalHandler {
                 }
             }
 
-            Set<String> svsIds = new HashSet<>();
+            Set<String> svcIds = new HashSet<>();
             if (anomalyEvent.getKpis().getSvcIdCount() > 0) {
-                svsIds = Collections.singleton(anomalyEvent.getKpis().getSvcId(0));
+                svcIds = new HashSet<>(anomalyEvent.getKpis().getSvcIdList());
             }
 
             // Check maintenance window for service wise for all kpis.
-            if (!svsIds.isEmpty() && (isTxnData || (kpiEntity != null && kpiEntity.getIsMaintenanceExcluded() == 0)) &&
-                    commons.isServicesUnderMaintenance(anomalyEvent.getAccountId(), svsIds, "")) {
+            if (!svcIds.isEmpty() && (isWorkload || (kpiEntity != null && kpiEntity.getIsMaintenanceExcluded() == 0)) &&
+                    commons.isServicesUnderMaintenance(Collections.singleton(anomalyEvent.getAccountId()), svcIds, "")) {
                 metrics.updateMaintenanceEvents();
                 return;
             }
@@ -95,7 +122,7 @@ public class SignalHandler {
                 return;
             }
 
-            if (!isTxnData && kpiEntity == null) {
+            if (!isWorkload && kpiEntity == null) {
                 log.error("Invalid kpi id received, Anomaly details:{}", anomalyEvent);
                 metrics.updateErrors();
                 return;
@@ -108,33 +135,70 @@ public class SignalHandler {
             }
 
             // Check the anomaly event for Txn KPI,entry point and problem event kpis list.
-            com.heal.configuration.pojos.Service service = wrapper.getServiceDetailsByIdentifier(anomalyEvent.getAccountId(), anomalyEvent.getKpis().getSvcId(0));
-            if(service == null){
+            Service service = wrapper.getServiceDetailsByIdentifier(anomalyEvent.getAccountId(), anomalyEvent.getKpis().getSvcId(0));
+            if (service == null) {
                 log.error("Could not find service details for serviceId:{} in redis for anomalyId:{}", anomalyEvent.getKpis().getSvcId(0), anomalyEvent.getAnomalyId());
                 return;
             }
-            List<BasicKpiEntity> txnKpis = wrapper.getComponentKpis(anomalyEvent.getAccountId(), "Transaction");
-            if(txnKpis.isEmpty()){
+
+            List<BasicKpiEntity> txnKpis = wrapper.getComponentKpis(anomalyEvent.getAccountId(), Constants.TRANSACTION_COMPONENT_IDENTIFIER);
+            if (txnKpis.isEmpty()) {
                 log.error("Could not find transaction kpis in redis for anomalyId:{}, accountId:{}.", anomalyEvent.getAccountId(), anomalyEvent.getAnomalyId());
                 return;
             }
 
-            BasicKpiEntity txnKpi = txnKpis.parallelStream().filter(e -> anomalyEvent.getKpis().getKpiId().equalsIgnoreCase(e.getId() + "")).findAny().orElse(null);
-            Tags entryPoint = service.getTags().stream().filter(t -> t.getType().equalsIgnoreCase("EntryPoint")).findAny().orElse(null);
+            BasicKpiEntity txnKpi = txnKpis.parallelStream()
+                    .filter(e -> anomalyEvent.getKpis().getKpiId().equalsIgnoreCase(e.getId() + ""))
+                    .findAny()
+                    .orElse(null);
 
-            if (entryPoint != null && isTxnData && txnKpi != null) {
-                log.debug("Creating Problem signal for txn kpi. Details:- Entry point:{}, isTxnData:{}, txnKpi:{}, account:{}, anomalyId:{}", entryPoint, isTxnData, txnKpi, account.getIdentifier(), anomalyEvent.getAnomalyId());
-                problemProcessor.processProblemEvent(anomalyEvent, txnKpi, account);
-                return;
-            } else if (isTxnData && txnKpi != null) {
-                log.debug("Creating Early Warning signal for txn kpi. Details:- Entry point: false, isTxnData:{}, txnKpi:{}, account:{}, anomalyId:{}", isTxnData, txnKpi, account.getIdentifier(), anomalyEvent.getAnomalyId());
-                warningProcessor.processEarlyWarningEvent(anomalyEvent, txnKpi, account);
-                return;
+            Anomalies anomaly = signalRepo.getAnomalyById(anomalyEvent.getAnomalyId(), anomalyEvent.getAccountId(), anomalyEvent.getEndTimeGMT());
+
+            Set<String> appIds = new HashSet<>(anomalyEvent.getAppIdList());
+
+            Set<String> signalIds = null;
+            Set<String> openSignalIds = new HashSet<>();
+            // Collect only signalIds for services referenced in the anomaly event across all apps
+            Set<String> anomalyServiceIds = new HashSet<>(anomalyEvent.getKpis().getSvcIdList());
+            appIds.forEach(appId -> {
+                try {
+                    Map<String, Map<String, Long>> openSignals = redisUtilities.getOpenSignalsForApp(appId);
+                    if (openSignals == null || openSignals.isEmpty()) return;
+                    anomalyServiceIds.forEach(svcId -> {
+                        Map<String, Long> signalTimeMap = openSignals.get(svcId);
+                        if (signalTimeMap != null && !signalTimeMap.isEmpty()) {
+                            openSignalIds.addAll(signalTimeMap.keySet());
+                        }
+                    });
+                } catch (Exception e) {
+                    log.error("Error fetching open signals for appId:{} while aggregating signalIds for anomalyId:{}", appId, anomalyEvent.getAnomalyId(), e);
+                }
+            });
+            if (!openSignalIds.isEmpty()) {
+                // Try to narrow down to signals already containing this anomaly (if summaries present)
+                Set<String> matchedByAnomaly = openSignalIds.stream()
+                        .map(redisUtilities::getSignalSummary)
+                        .filter(Objects::nonNull)
+                        .filter(ss -> ss.getAnomalies() != null && ss.getAnomalies().stream()
+                                .anyMatch(a -> anomalyEvent.getAnomalyId().equals(a.getAnomalyId())))
+                        .map(SignalSummary::getSignalId)
+                        .collect(Collectors.toSet());
+                signalIds = matchedByAnomaly.isEmpty() ? openSignalIds : matchedByAnomaly;
             }
 
-            log.debug("Creating Early Warning signal for kpi. Details:- anomalyId:{}, kpiEntity:{}, accountId:{}", anomalyEvent.getAnomalyId(), kpiEntity, account.getIdentifier());
-            // None of the above condition we will process anomaly event for early warning.
-            warningProcessor.processEarlyWarningEvent(anomalyEvent, kpiEntity, account);
+                handleAnomalyEvent(anomalyEvent,
+                        anomalyEvent.getKpis().getMetadataMap().getOrDefault("anomalyStatus", "OPEN"),
+                        isWorkload,
+                        signalIds,
+                        anomalyEvent.getAccountId(),
+                        anomalyEvent.getAnomalyId(),
+                        Integer.parseInt(anomalyEvent.getKpis().getThresholdSeverity()),
+                        anomalyEvent.getKpis().getMetadataMap(),
+                        txnKpi,
+                        kpiEntity,
+                        account,
+                        anomaly,
+                        appIds);
         } catch (Exception e) {
             metrics.updateErrors();
             log.error("Error while processing event, anomalyData:{}", anomalyEvent, e);
@@ -145,4 +209,201 @@ public class SignalHandler {
             metrics.updateProcessDetailsCounter("AnomalyProcessCounter", processTime);
         }
     }
+
+    /**
+     * Handles anomaly event processing for open, ongoing, and close anomaly types.
+     * Updates or creates signals as per the business rules described.
+     *
+     * @param anomalyEvent The anomaly event containing details about the anomaly.
+     * @param anomalyStatus The status of the anomaly event (OPEN, ONGOING, CLOSE).
+     * @param isWorkload Indicates if the anomaly is related to workload.
+     * @param signalIds The ID of the signal associated with the anomaly, if applicable.
+     * @param accountIdentifier The identifier of the account associated with the anomaly.
+     * @param anomalyId The identifier of the anomaly.
+     * @param severityId The severity ID of the anomaly.
+     * @param metaData Metadata associated with the anomaly event.
+     * @param txnKpi The transaction KPI entity, if applicable.
+     * @param kpiEntity The KPI entity associated with the anomaly.
+     * @param account The account details for the anomaly.
+     * @param anomaly The anomaly details.
+     * @param appIds Set of application IDs associated with the anomaly.
+     */
+    public void handleAnomalyEvent(
+            AnomalyEventProtos.AnomalyEvent anomalyEvent,
+            String anomalyStatus, // "OPEN", "ONGOING", "CLOSE"
+            boolean isWorkload,
+            Set<String> signalIds,
+            String accountIdentifier,
+            String anomalyId,
+            int severityId,
+            Map<String, String> metaData,
+            BasicKpiEntity txnKpi,
+            CompInstKpiEntity kpiEntity,
+            Account account,
+            Anomalies anomaly,
+            Set<String> appIds
+    ) {
+
+        List<String> severityLevels = Arrays.asList(lowSeverityIdSignal, mediumSeverityIdSignal, highSeverityIdSignal);
+        final BasicKpiEntity basicKpiEntity = txnKpi != null ? txnKpi : kpiEntity;
+        log.info("Processed anomaly event with {}: {}", txnKpi != null ? "txnKpi" : "kpiEntity", basicKpiEntity.getIdentifier());
+
+        if ("OPEN".equalsIgnoreCase(anomalyStatus)) {
+            SignalType signalType = (isWorkload && txnKpi != null) ? SignalType.PROBLEM : SignalType.EARLY_WARNING;
+            genericSignalProcessor.processSignalEvent(signalType, anomalyEvent, basicKpiEntity, account, appIds, signalIds);
+            log.info("Processed anomaly event with SignalType: {}", signalType);
+        } else if ("ONGOING".equalsIgnoreCase(anomalyStatus)) {
+            Set<String> anomalySignalIds = new HashSet<>(anomaly != null && anomaly.getSignalIds() != null ? anomaly.getSignalIds() : Collections.emptySet());
+            if (signalIds != null) {
+                anomalySignalIds.addAll(signalIds);
+            }
+            if (!anomalySignalIds.isEmpty()) {
+                List<SignalDetails> signalDetails = signalRepo.getSignalById(anomalySignalIds, accountIdentifier);
+                List<SignalDetails> openSignalDetails = (signalDetails == null ? Collections.<SignalDetails>emptyList() : signalDetails)
+                        .stream()
+                        .filter(s -> s.getCurrentStatus().equalsIgnoreCase(SignalStatus.OPEN.name()))
+                        .toList();
+                if (openSignalDetails.isEmpty()) {
+                    // Use same flow as Open Anomaly
+                    handleAnomalyEvent(anomalyEvent, "OPEN", isWorkload, null, accountIdentifier, anomalyId, severityId, metaData, txnKpi, kpiEntity, account, anomaly, appIds);
+                    return;
+                }
+                openSignalDetails.forEach(signal -> {
+                    if (severityLevels.indexOf(signal.getSeverityId().toString()) < severityLevels.indexOf(String.valueOf(severityId))) {
+                        // Update severity for last signal, update anomaly with signalId
+                        Map<String, String> updatedMetaData = signal.getMetadata();
+                        updatedMetaData.putAll(metaData);
+                        AnomalySummary anomalySummary = commons.getAnomalySummary(anomalyEvent.getKpis().getInstanceId(), basicKpiEntity.getIdentifier(),
+                                basicKpiEntity.getCategoryDetails().getIdentifier(), anomaly != null ? anomaly.getServiceId() : null, SignalType.valueOf(signal.getSignalType()),
+                                severityId, anomalyEvent.getEndTimeGMT(), anomalyEvent.getKpis().getIsWorkload(), anomalyEvent.getAnomalyId(),
+                                anomalyEvent.getKpis().getKpiAttribute(), basicKpiEntity.getGroupIdentifier(), anomalyEvent.getThresholdType(), anomalyEvent.getOperationType(),
+                                anomalyEvent.getKpis().getMetadataMap(), anomalyEvent.getKpis().getValue(), anomalyEvent.getKpis().getThresholdsMap(), anomalyEvent.getAccountId());
+
+                        boolean updateStatus = signalRepo.updateSignal(anomalyEvent.getEndTimeGMT(), signal.getCurrentStatus(), signal.getCurrentStatus(), updatedMetaData, anomalyEvent.getAnomalyId(), signal.getSignalId(), accountIdentifier,
+                                severityId, anomalySummary, anomalyEvent.getStartTimeGMT(), true, appIds, isWorkload, Integer.parseInt(anomalyEvent.getKpis().getThresholdSeverity()));
+                        if (updateStatus) {
+                            forwarder.sendSignalMessages(commons.getSignalProto(signal, false,
+                                    true, false, false, anomalySummary));
+                            log.info("Updated signal with higher severity for anomalyId:{}, signalId:{}, severityId:{}", anomalyEvent.getAnomalyId(), signal.getSignalId(), severityId);
+                        }
+                    } else {
+                        log.debug("Signal already has higher or equal severity for {} signal, signalId:{}, anomalyId:{}",
+                                signal.getSignalType(), signal.getSignalId(), anomalyEvent.getAnomalyId());
+                    }
+                });
+            } else {
+                // Use same flow as Open Anomaly
+                log.warn("No open signals found for anomalyId:{}, processing as OPEN anomaly.", anomalyEvent.getAnomalyId());
+                handleAnomalyEvent(anomalyEvent, "OPEN", isWorkload, null, accountIdentifier, anomalyId, severityId, metaData, txnKpi, kpiEntity, account, anomaly, appIds);
+                return;
+            }
+        } else if ("CLOSE".equalsIgnoreCase(anomalyStatus)) {
+            Set<String> anomalySignalIds = new HashSet<>(anomaly != null && anomaly.getSignalIds() != null ? anomaly.getSignalIds() : Collections.emptySet());
+            if (signalIds != null) {
+                anomalySignalIds.addAll(signalIds);
+            }
+            if (!anomalySignalIds.isEmpty()) {
+                boolean updateStatus = false;
+
+                List<SignalDetails> signalDetails = signalRepo.getSignalById(anomalySignalIds, accountIdentifier);
+                List<SignalDetails> openSignalDetails = (signalDetails == null ? Collections.<SignalDetails>emptyList() : signalDetails)
+                        .stream()
+                        .filter(s -> s.getCurrentStatus().equalsIgnoreCase(SignalStatus.OPEN.name()))
+                        .toList();
+                if (openSignalDetails.isEmpty()) {
+                    log.error("No open signal found for anomalyId:{} to close", anomalyEvent.getAnomalyId());
+                    return;
+                }
+
+                for (SignalDetails signal : openSignalDetails) {
+                    //                    signalRepo.removeAnomalyFromSignal(anomalyId, signal.getSignalId(), accountIdentifier, anomalyEvent.getStartTimeGMT(), signal.getCurrentStatus(), anomaly.getServiceId(), false);
+                    signal.getAnomalies().remove(anomalyEvent.getAnomalyId());
+                    // If no anomalies under signal, close signal
+                    if (signal.getAnomalies().isEmpty()) {
+                        String closingReason = "All anomalies closed under this signal.";
+                        log.info("Closing signal: {}, reason: {}", signal.getSignalId(), closingReason);
+                        closeSignal(signal, accountIdentifier, appIds, closingReason, null);
+                    } else {
+                        SignalSummary signalSummary = redisUtilities.getSignalSummary(signal.getSignalId());
+                        if (signalSummary != null) {
+                            signalSummary.getAnomalies().removeIf(anomalySummary -> anomalyId.equalsIgnoreCase(anomalySummary.getAnomalyId()));
+                            // Determine the highest remaining anomaly severity using defined ordering (low < medium < high)
+                            int updatedSeverityId = signalSummary.getAnomalies().stream()
+                                    .map(SignalSummary.AnomalySummary::getSeverityId)
+                                    .max(Comparator.comparingInt(s -> {
+                                        int idx = severityLevels.indexOf(String.valueOf(s));
+                                        return idx < 0 ? -1 : idx; // unknown severities rank lowest
+                                    }))
+                                    .orElse(signal.getSeverityId());
+                            signalSummary.setSeverityId(updatedSeverityId);
+                            signal.setSeverityId(updatedSeverityId);
+                            if (signalSummary.getSignalType().equalsIgnoreCase(SignalType.PROBLEM.name())) {
+                                // ...logic to check if anomalyId is workload...
+                                boolean hasWorkload = signalSummary.getAnomalies().stream().anyMatch(SignalSummary.AnomalySummary::isWorkload);
+                                if (!hasWorkload) {
+                                    // Downgrade signal and create EARLY_WARNING
+                                    signal.setCurrentStatus(SignalStatus.DOWNGRADED.name());
+                                    signalSummary.setSignalType(SignalType.EARLY_WARNING.name());
+                                    String newSignalId;
+                                    newSignalId = genericSignalProcessor.processSignalEvent(SignalType.EARLY_WARNING, anomalyEvent, basicKpiEntity, account, appIds, null);
+                                    signal.getRelatedSignals().add(newSignalId);
+                                    signalRepo.signalDetailsUpdateStatus(System.currentTimeMillis(), SignalStatus.DOWNGRADED.name(), signal.getMetadata(), signal.getSignalId(), signal.getRelatedSignals(), accountIdentifier, signal.getStartedTime(), true);
+                                    log.info("Downgraded signal: {}, created EARLY_WARNING signal: {}", signal.getSignalId(), newSignalId);
+                                    // TODO: Should I delete the old signal details?
+                                }
+                            }
+                            log.info("Signal summary updated for signalId: {}, remaining anomalies: {}", signal.getSignalId(), signalSummary.getAnomalies().size());
+                            redisUtilities.updateSignalSummary(signal.getSignalId(), signalSummary);
+                        }
+                    }
+                    updateStatus = signalRepo.removeAnomalyFromSignal(anomalyEvent.getAnomalyId(), signal.getSignalId(), accountIdentifier, anomalyEvent.getStartTimeGMT(), signal.getCurrentStatus(), anomaly != null ? anomaly.getServiceId() : null, true, appIds);
+
+                    if (updateStatus) {
+                        AnomalySummary anomalySummary = commons.getAnomalySummary(anomalyEvent.getKpis().getInstanceId(), basicKpiEntity.getIdentifier(),
+                                basicKpiEntity.getCategoryDetails().getIdentifier(), anomaly != null ? anomaly.getServiceId() : null, SignalType.valueOf(signal.getSignalType()),
+                                severityId, anomalyEvent.getEndTimeGMT(), anomalyEvent.getKpis().getIsWorkload(), anomalyEvent.getAnomalyId(),
+                                anomalyEvent.getKpis().getKpiAttribute(), basicKpiEntity.getGroupIdentifier(), anomalyEvent.getThresholdType(), anomalyEvent.getOperationType(),
+                                anomalyEvent.getKpis().getMetadataMap(), anomalyEvent.getKpis().getValue(), anomalyEvent.getKpis().getThresholdsMap(), anomalyEvent.getAccountId());
+                        forwarder.sendSignalMessages(commons.getSignalProto(signal, false,
+                                true, false, signal.getSignalType().equalsIgnoreCase(SignalStatus.DOWNGRADED.name()), anomalySummary));
+                    }
+                    log.info("Signal with anomalyId:{} is successfully removed from signalId:{}", anomalyEvent.getAnomalyId(), signal.getSignalId());
+                }
+            } else {
+                log.error("No signal IDs found for anomalyId:{} to close.", anomalyEvent.getAnomalyId());
+                return;
+            }
+        }
+    }
+
+    /**
+     * Closes the signal and updates the status in the database.
+     * Sends a message to the queue with the updated signal details.
+     *
+     * @param signalDetails The details of the signal to be closed.
+     * @param accountId The identifier of the account associated with the signal.
+     * @param appIds The set of application IDs associated with the signal.
+     * @param closingReason The reason for closing the signal.
+     * @param endTime The end time for the signal closure, if null current time will be used.
+     */
+    public void closeSignal(SignalDetails signalDetails, String accountId, Set<String> appIds, String closingReason, String endTime) {
+        signalDetails.setCurrentStatus(SignalStatus.CLOSED.name());
+        if (closingReason != null) {
+            signalDetails.getMetadata().put("closing_reason", closingReason);
+        }
+        signalDetails.getMetadata().put("end_time", endTime != null ? endTime : String.valueOf(System.currentTimeMillis()));
+
+        appIds.forEach(appId -> {
+            boolean isUpdated = signalRepo.closeSignal(signalDetails, accountId, appId);
+            log.info("Dead signal : {} is successfully closed.", signalDetails.getSignalId());
+            metrics.updateSignalCloseCount();
+            metrics.updateSnapshots(signalDetails.getSignalId() + "_CLOSED", 1);
+
+            if (isUpdated) {
+                forwarder.sendSignalMessages(commons.getSignalProto(signalDetails, false,
+                        false, false, true, null));
+            }
+        });
+        log.info("Signal with ID: {} is successfully closed for account: {} and apps: {}", signalDetails.getSignalId(), accountId, appIds);
+    }
 }
diff --git a/src/main/java/com/heal/signal/detector/process/WarningSignalProcessor.java b/src/main/java/com/heal/signal/detector/process/WarningSignalProcessor.java
index 2d03efa..448962d 100644
--- a/src/main/java/com/heal/signal/detector/process/WarningSignalProcessor.java
+++ b/src/main/java/com/heal/signal/detector/process/WarningSignalProcessor.java
@@ -2,10 +2,7 @@ package com.heal.signal.detector.process;
 
 import com.appnomic.appsone.common.protbuf.AnomalyEventProtos;
 import com.heal.configuration.enums.SignalType;
-import com.heal.configuration.pojos.Account;
-import com.heal.configuration.pojos.AnomalySummary;
-import com.heal.configuration.pojos.BasicEntity;
-import com.heal.configuration.pojos.BasicKpiEntity;
+import com.heal.configuration.pojos.*;
 import com.heal.configuration.pojos.opensearch.SignalDetails;
 import com.heal.signal.detector.cache.CacheWrapper;
 import com.heal.signal.detector.opensearch.SignalRepo;
@@ -14,6 +11,7 @@ import com.heal.signal.detector.service.ForwarderToQueue;
 import com.heal.signal.detector.util.Commons;
 import com.heal.signal.detector.util.HealthMetrics;
 import com.heal.signal.detector.util.LocalQueues;
+import com.heal.signal.detector.util.RedisUtilities;
 import lombok.extern.slf4j.Slf4j;
 import org.springframework.beans.factory.annotation.Autowired;
 import org.springframework.beans.factory.annotation.Value;
@@ -25,6 +23,9 @@ import java.util.stream.Collectors;
 @Component
 @Slf4j
 public class WarningSignalProcessor {
+    @Autowired
+    RedisUtilities redisUtilities;
+
     @Autowired
     Commons commons;
 
@@ -46,108 +47,149 @@ public class WarningSignalProcessor {
     @Autowired
     LocalQueues localQueues;
 
+    @Value("${signal.severity.id.high:433}")
+    String highSeverityIdSignal;
+
+    @Value("${signal.severity.id.medium:432}")
+    String mediumSeverityIdSignal;
+
+    @Value("${signal.severity.id.low:431}")
+    String lowSeverityIdSignal;
+
     public void processEarlyWarningEvent(AnomalyEventProtos.AnomalyEvent anomalyEvent, BasicKpiEntity kpiEntity, Account account) {
         long st = System.currentTimeMillis();
 
         Set<SignalDetails> openSignalsFromOS;
         try {
-            openSignalsFromOS = signalRepo.getOpenSignals(anomalyEvent.getAccountId(), true);
-        } catch (Exception e) {
-            log.error("Error occurred while fetching open signals so pushing in failed queue will be processing it later " +
-                            "for accountIdentifier: {}, anomalyId: {}",
-                    account.getIdentifier(), anomalyEvent.getAnomalyId(), e);
-            localQueues.addToFailedOpenSignalQueue(FailedOpenSignalPojo.builder().signalType(SignalType.EARLY_WARNING)
-                    .anomalyEvent(anomalyEvent)
-                    .account(account)
-                    .kpiEntity(kpiEntity)
-                    .build());
-
-            return;
-        }
 
-        try {
-            String serviceId = anomalyEvent.getKpis().getSvcId(0);
-            com.heal.configuration.pojos.Service service = wrapper.getServiceDetailsByIdentifier(anomalyEvent.getAccountId(), serviceId);
-            if (service == null) {
-                log.error("Invalid service identifier, Anomaly event details:{}", anomalyEvent);
-                metrics.updateErrors();
+            try {
+                openSignalsFromOS = signalRepo.getOpenSignals(anomalyEvent.getAccountId(), true, true);
+            } catch (Exception e) {
+                log.error("Error occurred while fetching open signals. So pushing in failed queue. Will be processing it later " +
+                        "for accountIdentifier: {}, anomalyId: {}", account.getIdentifier(), anomalyEvent.getAnomalyId(), e);
+
+                localQueues.addToFailedOpenSignalQueue(FailedOpenSignalPojo.builder()
+                        .signalType(SignalType.EARLY_WARNING)
+                        .anomalyEvent(anomalyEvent)
+                        .account(account)
+                        .kpiEntity(kpiEntity)
+                        .build());
+
                 return;
             }
 
+            Set<Service> services = anomalyEvent.getKpis().getSvcIdList()
+                    .stream()
+                    .map(serviceIdentifier -> {
+                        Service service = wrapper.getServiceDetailsByIdentifier(anomalyEvent.getAccountId(), serviceIdentifier);
+                        if (service == null) {
+                            log.error("Invalid service identifier, Anomaly event details:{}", anomalyEvent);
+                            metrics.updateErrors();
+                        }
+                        return service;
+                    }).filter(Objects::nonNull)
+                    .collect(Collectors.toSet());
+
             long start = System.currentTimeMillis();
-            Map<String, String> metaData = commons.getSignalMetaData(anomalyEvent, SignalType.EARLY_WARNING);
-            Set<SignalDetails> signals = commons.getOpenSignals(anomalyEvent.getAccountId(), openSignalsFromOS, serviceId);
-            metrics.updateProcessDetails("WarnOpenSignals", System.currentTimeMillis() - start);
 
-            log.debug("Open EarlyWarning signals for anomalyId:{}, accountId:{}, serviceId:{}, signal ids:{}", anomalyEvent.getAnomalyId(), account.getIdentifier(), serviceId, signals.stream().map(SignalDetails::getSignalId).collect(Collectors.joining()));
+            Set<String> serviceIdentifiers = services.stream().map(Service::getIdentifier).collect(Collectors.toSet());
 
-            Set<BasicEntity> destServicesMap = wrapper.getNeighbours(account.getIdentifier(), serviceId);
-            if (destServicesMap.isEmpty()) {
-                log.warn("Could not find service neighbours from redis for includeOutbounds:{}, serviceId:{}, serviceName:{}, accountId:{} for anomalyId:{}", includeOutbounds, service.getIdentifier(), service.getName(), account.getIdentifier(), anomalyEvent.getAnomalyId());
-                if(includeOutbounds == 1) {
-                    List<BasicEntity> serviceOutbounds = wrapper.getAccountOutbounds(account.getIdentifier()).getOrDefault(service.getId(), new ArrayList<>());
-                    log.debug("Neighbours are not exists for serviceId:{}, serviceName:{}, accountId:{}, looking for outbound connections, size:{} in anomalyId:{}", service.getIdentifier(), service.getName(), account.getIdentifier(), serviceOutbounds.size(), anomalyEvent.getAnomalyId());
-                    destServicesMap = new HashSet<>(serviceOutbounds);
-                }
+            Set<ControllerAlias> serviceAliasSet = redisUtilities.getServiceAliases();
+            Map<String, String> dcDrServiceMap = new HashMap<>();
+            Map<String, String> drDcServiceMap = new HashMap<>();
+            if (serviceAliasSet != null && !serviceAliasSet.isEmpty()) {
+                dcDrServiceMap.putAll(serviceAliasSet.stream().collect(Collectors.toMap(ControllerAlias::getDcControllerIdentifier, ControllerAlias::getDrControllerIdentifier)));
+                drDcServiceMap.putAll(serviceAliasSet.stream().collect(Collectors.toMap(ControllerAlias::getDrControllerIdentifier, ControllerAlias::getDcControllerIdentifier)));
             }
 
-            if(destServicesMap.isEmpty()) {
-                log.info("No neighbours/outbound connections for serviceId:{}, serviceName:{}, accountId:{} for anomalyId:{}.", service.getIdentifier(), service.getName(), account.getIdentifier(), anomalyEvent.getAnomalyId());
-            } else {
-                log.debug("{}:Neighbours/Outbound connections found for serviceId:{}, serviceName:{}, accountId:{} for anomalyId:{}.", destServicesMap.size(), service.getIdentifier(), service.getName(), account.getIdentifier(), anomalyEvent.getAnomalyId());
+            Set<SignalDetails> signals;
+            try {
+                signals = commons.processAndGetOpenSignals(services, account.getIdentifier(), anomalyEvent.getAnomalyId(),
+                        dcDrServiceMap, drDcServiceMap, openSignalsFromOS, true, new HashSet<>(anomalyEvent.getAppIdList()));
+            } catch (Exception e) {
+                log.error("Error occurred while fetching open signals from Redis. So pushing in failed queue. Will be processing it later " +
+                        "for accountIdentifier: {}, anomalyId: {}", account.getIdentifier(), anomalyEvent.getAnomalyId(), e);
+                localQueues.addToFailedOpenSignalQueue(FailedOpenSignalPojo.builder().signalType(SignalType.EARLY_WARNING)
+                        .anomalyEvent(anomalyEvent)
+                        .account(account)
+                        .kpiEntity(kpiEntity)
+                        .build());
+
+                return;
             }
 
-            destServicesMap.forEach(destService -> signals.addAll(commons.getOpenSignals(account.getIdentifier(), openSignalsFromOS, destService.getIdentifier())));
+            log.debug("Open signals in EW processor, anomaly id:{}, signal ids: {}",
+                    anomalyEvent.getAnomalyId(), signals.stream().map(SignalDetails::getSignalId).collect(Collectors.joining()));
+
+            metrics.updateProcessDetails("WarnOpenSignals", System.currentTimeMillis() - start);
+
+            log.debug("Open EarlyWarning signals for anomalyId:{}, accountId:{}, serviceIdentifiers:{}, signal ids:{}",
+                    anomalyEvent.getAnomalyId(), account.getIdentifier(), serviceIdentifiers,
+                    signals.stream().map(SignalDetails::getSignalId).collect(Collectors.joining(", ")));
+
+            Map<String, String> metaData = commons.getSignalMetaData(anomalyEvent, SignalType.EARLY_WARNING);
+            //NOTE:- Proto will give immutable map. Don't remove new HashMap<>() here, else java.lang.UnsupportedOperationException
+            // will come in case of map.put().
+            Map<String, String> anomalyEventMetadataMap = new HashMap<>(anomalyEvent.getKpis().getMetadataMap());
 
             if (signals.isEmpty()) {
                 start = System.currentTimeMillis();
                 log.info("No early warning signal is found. So, we will create a new early warning signal for anomalyId:{}, accountId:{}, serviceId:{}.",
-                        anomalyEvent.getAnomalyId(), anomalyEvent.getAccountId(), serviceId);
+                        anomalyEvent.getAnomalyId(), anomalyEvent.getAccountId(), String.join(",", serviceIdentifiers));
+
                 int severityId = wrapper.getSeverityId(anomalyEvent.getKpis().getThresholdSeverity());
-                String signalId = commons.createSignalId("E", account.getId(), Integer.parseInt(anomalyEvent.getKpis().getKpiId()), service.getId(), anomalyEvent.getEndTimeGMT() / 1000);
 
-                SignalDetails signalDetails = commons.createSignal(signalId, anomalyEvent.getEndTimeGMT(), anomalyEvent.getEndTimeGMT(), 0L, SignalStatus.OPEN.name(),
-                        Collections.singleton(anomalyEvent.getAnomalyId()), SignalType.EARLY_WARNING, Collections.singleton(serviceId), Collections.singleton(serviceId),
-                        Collections.singleton(anomalyEvent.getAnomalyId()), null, null, severityId, metaData);
+                int servicesHashCode = String.join("-", serviceIdentifiers).hashCode();
+
+                String signalId = commons.createSignalId("E", account.getId(), Integer.parseInt(anomalyEvent.getKpis().getKpiId()),
+                        servicesHashCode, anomalyEvent.getEndTimeGMT() / 1000);
+
+                SignalDetails signalDetails = commons.createSignal(signalId, anomalyEvent.getEndTimeGMT(), anomalyEvent.getEndTimeGMT(), 0L,
+                        SignalStatus.OPEN.name(), Collections.singleton(anomalyEvent.getAnomalyId()), SignalType.EARLY_WARNING, serviceIdentifiers,
+                        serviceIdentifiers, Collections.singleton(anomalyEvent.getAnomalyId()), null, null, severityId, metaData, anomalyEvent.getAccountId());
 
                 AnomalySummary anomalySummary = commons.getAnomalySummary(anomalyEvent.getKpis().getInstanceId(), kpiEntity.getIdentifier(),
-                        kpiEntity.getCategoryDetails().getIdentifier(), service.getIdentifier(), SignalType.EARLY_WARNING,
-                        severityId, anomalyEvent.getEndTimeGMT(), anomalyEvent.getKpis().getIsWorkload(), anomalyEvent.getAnomalyId(),
-                        anomalyEvent.getKpis().getKpiAttribute(), kpiEntity.getGroupIdentifier(), anomalyEvent.getThresholdType(), anomalyEvent.getOperationType(),
-                        anomalyEvent.getKpis().getMetadataMap(), anomalyEvent.getKpis().getValue(), anomalyEvent.getKpis().getThresholdsMap());
+                        kpiEntity.getCategoryDetails().getIdentifier(), serviceIdentifiers, SignalType.EARLY_WARNING, severityId, anomalyEvent.getEndTimeGMT(),
+                        anomalyEvent.getKpis().getIsWorkload(), anomalyEvent.getAnomalyId(), anomalyEvent.getKpis().getKpiAttribute(),
+                        kpiEntity.getGroupIdentifier(), anomalyEvent.getThresholdType(), anomalyEvent.getOperationType(),
+                        anomalyEventMetadataMap, anomalyEvent.getKpis().getValue(), anomalyEvent.getKpis().getThresholdsMap(), anomalyEvent.getAccountId());
 
-                boolean insertStatus = signalRepo.insertSignal(signalDetails, anomalyEvent.getAccountId(), anomalySummary);
+                boolean insertStatus = signalRepo.insertSignal(signalDetails, anomalyEvent.getAccountId(), anomalySummary, true, new HashSet<>(anomalyEvent.getAppIdList()), anomalyEvent.getKpis().getIsWorkload(), Integer.parseInt(anomalyEvent.getKpis().getThresholdSeverity()));
 
                 if (insertStatus) {
                     boolean queueUpdateStatus = signalRepo.updateAnomaly(anomalyEvent.getAnomalyId(), anomalyEvent.getEndTimeGMT(),
                             Collections.singleton(signalId), anomalyEvent.getAccountId());
                     if (queueUpdateStatus) {
-                        log.trace("Anomaly updated into scheduler queue for warning signal, signalId:{}, anomalyId:{}", signalDetails.getSignalId(), anomalyEvent.getAnomalyId());
+                        log.trace("Anomaly updated into scheduler queue for warning signal, signalId:{}, anomalyId:{}",
+                                signalDetails.getSignalId(), anomalyEvent.getAnomalyId());
                     } else {
-                        log.error("Anomaly not updated into scheduler queue for warning signal, signalId:{}, anomalyId:{}, because of this anomaly wont be update with signal id.", signalDetails.getSignalId(), anomalyEvent.getAnomalyId());
+                        log.error("Anomaly not updated into scheduler queue for warning signal, signalId:{}, anomalyId:{}," +
+                                " because of this anomaly wont be update with signal id.", signalDetails.getSignalId(), anomalyEvent.getAnomalyId());
                     }
 //                signalRepo.serviceSignalDetailsCreate(account.getIdentifier(), signalId, signalDetails.getServiceIds());
 
                     metrics.updateSignalOpenCount(1);
                     metrics.updateSnapshots(signalDetails.getSignalId() + "_CREATED", 1);
 
-                    forwarder.sendSignalMessages(commons.getSignalProto(account.getIdentifier(), signalDetails, false,
+                    forwarder.sendSignalMessages(commons.getSignalProto(signalDetails, false,
                             false, false, false, anomalySummary));
 
                     log.info("Early warning signal is created for anomalyId:{}, signal  id:{}, severity:{}, type:{}", anomalyEvent.getAnomalyId(),
                             signalDetails.getSignalId(), signalDetails.getSeverityId(), signalDetails.getSignalType());
                 } else {
-                    log.error("Early warning signal creation failed, anomaly event will dropped for anomaly:{}, signalId:{}", anomalyEvent.getAnomalyId(), signalDetails.getSignalId());
+                    log.error("Early warning signal creation failed, anomaly event will dropped for anomaly:{}, signalId:{}",
+                            anomalyEvent.getAnomalyId(), signalDetails.getSignalId());
                 }
                 metrics.updateProcessDetails("WarnCreateSignals", System.currentTimeMillis() - start);
 
             } else {
 
                 start = System.currentTimeMillis();
-                log.info("Open signals found for anomalyId:{}, accountId:{}, serviceId:{}, signals:{}.",
-                        anomalyEvent.getAnomalyId(), anomalyEvent.getAccountId(), serviceId, signals.stream().map(SignalDetails::getSignalId).collect(Collectors.joining(",")));
+                log.info("Open signals found for anomalyId:{}, accountId:{}, serviceIdentifiers:{}, signals:{}.",
+                        anomalyEvent.getAnomalyId(), anomalyEvent.getAccountId(), serviceIdentifiers,
+                        signals.stream().map(SignalDetails::getSignalId).collect(Collectors.joining(",")));
 
-                OpenSignals openSignals = populateOpenSignalDetails(anomalyEvent, signals, serviceId, metaData);
+                OpenSignals openSignals = populateOpenSignalDetails(anomalyEvent, signals, serviceIdentifiers, metaData);
                 int severityId = wrapper.getSeverityId(anomalyEvent.getKpis().getThresholdSeverity());
 
                 Set<String> signalIds = new HashSet<>();
@@ -161,15 +203,15 @@ public class WarningSignalProcessor {
                         SignalChanges signalChanges = openSignals.getEwSignalHelper().getSignalDetailChanges().get(ewSignalId);
 
                         AnomalySummary anomalySummary = commons.getAnomalySummary(anomalyEvent.getKpis().getInstanceId(), kpiEntity.getIdentifier(),
-                                kpiEntity.getCategoryDetails().getIdentifier(), service.getIdentifier(), SignalType.EARLY_WARNING,
+                                kpiEntity.getCategoryDetails().getIdentifier(), serviceIdentifiers, SignalType.EARLY_WARNING,
                                 severityId, anomalyEvent.getEndTimeGMT(), anomalyEvent.getKpis().getIsWorkload(), anomalyEvent.getAnomalyId(),
                                 anomalyEvent.getKpis().getKpiAttribute(), kpiEntity.getGroupIdentifier(), anomalyEvent.getThresholdType(), anomalyEvent.getOperationType(),
-                                anomalyEvent.getKpis().getMetadataMap(), anomalyEvent.getKpis().getValue(), anomalyEvent.getKpis().getThresholdsMap());
+                                anomalyEventMetadataMap, anomalyEvent.getKpis().getValue(), anomalyEvent.getKpis().getThresholdsMap(), anomalyEvent.getAccountId());
 
                         boolean updateSignal = signalRepo.updateSignal(anomalyEvent.getEndTimeGMT(), SignalStatus.OPEN.name(), SignalStatus.OPEN.name(),
                                 signalDetails.getMetadata(), anomalyEvent.getAnomalyId(), ewSignalId, account.getIdentifier(),
                                 signalChanges.isSeverityChanged() ? severityId : signalDetails.getSeverityId(),
-                                anomalySummary, signalDetails.getStartedTime());
+                                anomalySummary, signalDetails.getStartedTime(), true, new HashSet<>(anomalyEvent.getAppIdList()), anomalyEvent.getKpis().getIsWorkload(), Integer.parseInt(anomalyEvent.getKpis().getThresholdSeverity()));
 
                         if (updateSignal) {
                             signalIds.add(ewSignalId);
@@ -177,7 +219,7 @@ public class WarningSignalProcessor {
                             metrics.updateSignalUpdateCount(1);
                             metrics.updateSnapshots(signalDetails.getSignalId() + "_UPDATED", 1);
 
-                            forwarder.sendSignalMessages(commons.getSignalProto(account.getIdentifier(), signalDetails, false,
+                            forwarder.sendSignalMessages(commons.getSignalProto(signalDetails, false,
                                     signalChanges.isSeverityChanged(), signalChanges.isServiceAdded(), false, anomalySummary));
 
                             log.info("Early warning signal is updated for anomalyId:{}, signal id:{}, severity:{}, type:{}",
@@ -194,7 +236,8 @@ public class WarningSignalProcessor {
                     if (queueUpdateStatus) {
                         log.trace("Anomaly updated into scheduler queue for warning signal, signalIds:{}, anomalyId:{}", signalIds, anomalyEvent.getAnomalyId());
                     } else {
-                        log.error("Anomaly not updated into scheduler queue for warning signal, signalIds:{}, anomalyId:{}, because of this anomaly wont be update with signal id.", signalIds, anomalyEvent.getAnomalyId());
+                        log.error("Anomaly not updated into scheduler queue for warning signal, signalIds:{}, anomalyId:{}," +
+                                " because of this anomaly wont be update with signal id.", signalIds, anomalyEvent.getAnomalyId());
                     }
                 } else {
                     // Update the problem with current anomaly data.
@@ -204,25 +247,26 @@ public class WarningSignalProcessor {
                         SignalChanges signalChanges = openSignals.getPSignalHelper().getSignalDetailChanges().get(pSignalId);
 
                         AnomalySummary anomalySummary = commons.getAnomalySummary(anomalyEvent.getKpis().getInstanceId(), kpiEntity.getIdentifier(),
-                                kpiEntity.getCategoryDetails().getIdentifier(), service.getIdentifier(), SignalType.PROBLEM,
+                                kpiEntity.getCategoryDetails().getIdentifier(), serviceIdentifiers, SignalType.PROBLEM,
                                 severityId, anomalyEvent.getEndTimeGMT(), anomalyEvent.getKpis().getIsWorkload(), anomalyEvent.getAnomalyId(),
                                 anomalyEvent.getKpis().getKpiAttribute(), kpiEntity.getGroupIdentifier(), anomalyEvent.getThresholdType(),
-                                anomalyEvent.getOperationType(), anomalyEvent.getKpis().getMetadataMap(), anomalyEvent.getKpis().getValue(),
-                                anomalyEvent.getKpis().getThresholdsMap());
+                                anomalyEvent.getOperationType(), anomalyEventMetadataMap, anomalyEvent.getKpis().getValue(),
+                                anomalyEvent.getKpis().getThresholdsMap(), anomalyEvent.getAccountId());
 
                         boolean updateSignal = signalRepo.updateSignal(anomalyEvent.getEndTimeGMT(), SignalStatus.OPEN.name(), SignalStatus.OPEN.name(),
                                 signalDetails.getMetadata(), anomalyEvent.getAnomalyId(), pSignalId, account.getIdentifier(),
                                 signalChanges.isSeverityChanged() ? severityId : signalDetails.getSeverityId(),
-                                anomalySummary, signalDetails.getStartedTime());
+                                anomalySummary, signalDetails.getStartedTime(), true, new HashSet<>(anomalyEvent.getAppIdList()), anomalyEvent.getKpis().getIsWorkload(), Integer.parseInt(anomalyEvent.getKpis().getThresholdSeverity()));
 
                         boolean updateSignalStatus = signalRepo.signalDetailsUpdateStatus(anomalyEvent.getEndTimeGMT(), SignalStatus.OPEN.name(),
                                 signalDetails.getMetadata(), pSignalId, openSignals.getEwSignalHelper().getSignals(), anomalyEvent.getAccountId(),
                                 signalDetails.getStartedTime(), false);
 
                         if (updateSignalStatus && updateSignal) {
-                            forwarder.sendSignalMessages(commons.getSignalProto(account.getIdentifier(), signalDetails, false,
+                            forwarder.sendSignalMessages(commons.getSignalProto(signalDetails, false,
                                     signalChanges.isSeverityChanged(), signalChanges.isServiceAdded(), false, anomalySummary));
                             signalIds.add(pSignalId);
+
                             metrics.updateSignalUpdateCount(1);
                             metrics.updateSnapshots(signalDetails.getSignalId() + "_UPDATED", 1);
                             log.info("Problem signal is updated for anomalyId:{}, signal id:{}, severity:{}, type:{}",
@@ -240,7 +284,8 @@ public class WarningSignalProcessor {
                     if (queueUpdateStatus) {
                         log.trace("Anomaly updated into scheduler queue for warning signal, signalIds:{}, anomalyId:{}", signalIds, anomalyEvent.getAnomalyId());
                     } else {
-                        log.error("Anomaly not updated into scheduler queue for warning signal, signalIds:{}, anomalyId:{}, because of this anomaly wont be update with signal id.", signalIds, anomalyEvent.getAnomalyId());
+                        log.error("Anomaly not updated into scheduler queue for warning signal, signalIds:{}, anomalyId:{}," +
+                                " because of this anomaly wont be update with signal id.", signalIds, anomalyEvent.getAnomalyId());
                     }
 
                     // Update the early warnings with current anomaly data.(Upgrade early warning)
@@ -267,21 +312,23 @@ public class WarningSignalProcessor {
                             metrics.updateSnapshots(signalDetails.getSignalId() + "_UPDATED", 1);
 
                             AnomalySummary anomalySummary = commons.getAnomalySummary(anomalyEvent.getKpis().getInstanceId(), kpiEntity.getIdentifier(),
-                                    kpiEntity.getCategoryDetails().getIdentifier(), service.getIdentifier(), SignalType.PROBLEM,
+                                    kpiEntity.getCategoryDetails().getIdentifier(), serviceIdentifiers, SignalType.PROBLEM,
                                     severityId, anomalyEvent.getEndTimeGMT(), anomalyEvent.getKpis().getIsWorkload(), anomalyEvent.getAnomalyId(),
                                     anomalyEvent.getKpis().getKpiAttribute(), kpiEntity.getGroupIdentifier(), anomalyEvent.getThresholdType(),
-                                    anomalyEvent.getOperationType(), anomalyEvent.getKpis().getMetadataMap(), anomalyEvent.getKpis().getValue(),
-                                    anomalyEvent.getKpis().getThresholdsMap());
+                                    anomalyEvent.getOperationType(), anomalyEventMetadataMap, anomalyEvent.getKpis().getValue(),
+                                    anomalyEvent.getKpis().getThresholdsMap(), anomalyEvent.getAccountId());
 
-                            forwarder.sendSignalMessages(commons.getSignalProto(account.getIdentifier(), signalDetails, false,
+                            forwarder.sendSignalMessages(commons.getSignalProto(signalDetails, false,
                                     signalChanges.isSeverityChanged(), signalChanges.isServiceAdded(), true, anomalySummary));
 
                             log.info("WSP:Early warning id:{} is upgraded to problem id:{}, anomalyId:{}, signal  id:{}, severity:{}, type:{}",
-                                    ewSignalId, openSignals.getPSignalHelper(), anomalyEvent.getAnomalyId(), signalDetails.getSignalId(), signalDetails.getSeverityId(), signalDetails.getSignalType());
+                                    ewSignalId, openSignals.getPSignalHelper(), anomalyEvent.getAnomalyId(), signalDetails.getSignalId(),
+                                    signalDetails.getSeverityId(), signalDetails.getSignalType());
 
                         } else {
                             log.error("WSP:Early warning id:{} upgrade is failed for problem id:{}, anomalyId:{}, signal  id:{}, severity:{}, type:{}",
-                                    ewSignalId, openSignals.getPSignalHelper(), anomalyEvent.getAnomalyId(), signalDetails.getSignalId(), signalDetails.getSeverityId(), signalDetails.getSignalType());
+                                    ewSignalId, openSignals.getPSignalHelper(), anomalyEvent.getAnomalyId(), signalDetails.getSignalId(),
+                                    signalDetails.getSeverityId(), signalDetails.getSignalType());
                         }
                     });
                 }
@@ -299,22 +346,22 @@ public class WarningSignalProcessor {
         }
     }
 
-
     private OpenSignals populateOpenSignalDetails(AnomalyEventProtos.AnomalyEvent anomalyEvent, Set<SignalDetails> openSignals,
-                                                  String serviceId, Map<String, String> metaData) {
+                                                  Set<String> serviceIdentifiers, Map<String, String> metaData) {
 
-        int higherSeverityId = wrapper.getSeverityId("Severe");
+//        int higherSeverityId = wrapper.getSeverityId("Severe");
+        List<String> severityLevels = Arrays.asList(lowSeverityIdSignal, mediumSeverityIdSignal, highSeverityIdSignal);
 
         SignalHelper ewSignalHelper = new SignalHelper();
         ewSignalHelper.getAnomalies().add(anomalyEvent.getAnomalyId());
         ewSignalHelper.getRcaAnomalies().add(anomalyEvent.getAnomalyId());
-        ewSignalHelper.getRcaServices().add(serviceId);
+        ewSignalHelper.getRcaServices().addAll(serviceIdentifiers);
 
         SignalHelper pSignalHelper = new SignalHelper();
-        int severityId = wrapper.getSeverityId(anomalyEvent.getKpis().getThresholdSeverity());
+        String severityId = anomalyEvent.getKpis().getThresholdSeverity();
         pSignalHelper.getAnomalies().add(anomalyEvent.getAnomalyId());
         pSignalHelper.getRcaAnomalies().add(anomalyEvent.getAnomalyId());
-        pSignalHelper.getRcaServices().add(serviceId);
+        pSignalHelper.getRcaServices().addAll(serviceIdentifiers);
 
         Set<String> serviceIds = new HashSet<>();
 
@@ -322,8 +369,8 @@ public class WarningSignalProcessor {
         for (SignalDetails signalDetails : openSignals) {
 
             if (anomalyEvent.getEndTimeGMT() < signalDetails.getStartedTime()) {
-                log.error("We will drop this anomaly because anomaly time:{} is less than the signal id:{}, " +
-                        "start time:{} for anomaly:{}", anomalyEvent.getEndTimeGMT(), signalDetails.getSignalId(), signalDetails.getStartedTime(), anomalyEvent);
+                log.error("We will drop this anomaly because anomaly time:{} is less than the signal id:{}, start time:{} for anomaly:{}",
+                        anomalyEvent.getEndTimeGMT(), signalDetails.getSignalId(), signalDetails.getStartedTime(), anomalyEvent);
                 isValidAnomaly = false;
                 break;
             }
@@ -332,7 +379,15 @@ public class WarningSignalProcessor {
                     .isServiceAdded(false)
                     .build();
 
+            signalDetails.setAccountIdentifiers(new HashSet<>() {{
+                add(anomalyEvent.getAccountId());
+                if (signalDetails.getAccountIdentifiers() != null) {
+                    addAll(signalDetails.getAccountIdentifiers());
+                }
+            }});
+
             signalDetails.getMetadata().putAll(metaData);
+
             if (signalDetails.getSignalType().equalsIgnoreCase(SignalType.EARLY_WARNING.name())) {
                 ewSignalHelper.getSignals().add(signalDetails.getSignalId());
                 ewSignalHelper.getAnomalies().addAll(signalDetails.getAnomalies());
@@ -345,25 +400,33 @@ public class WarningSignalProcessor {
                 }
 
                 // Check the signal is updating from default to severe
-                if (higherSeverityId != signalDetails.getSeverityId() && higherSeverityId == severityId) {
-                    signalDetails.setSeverityId(severityId);
+//                if (higherSeverityId != signalDetails.getSeverityId() && higherSeverityId == severityId) {
+//                    signalDetails.setSeverityId(severityId);
+//                    signalChanges.setSeverityChanged(true);
+//                }
+                if (severityLevels.indexOf(signalDetails.getSeverityId().toString()) < severityLevels.indexOf(severityId)) {
+                    signalDetails.setSeverityId(Integer.valueOf(severityId));
                     signalChanges.setSeverityChanged(true);
                 }
 
-                signalChanges.setServiceAdded(!signalDetails.getServiceIds().contains(serviceId));
+                signalChanges.setServiceAdded(!signalDetails.getServiceIds().containsAll(serviceIdentifiers));
                 ewSignalHelper.getSignalDetailChanges().put(signalDetails.getSignalId(), signalChanges);
                 ewSignalHelper.getSignalDetailsMap().put(signalDetails.getSignalId(), signalDetails);
 
-            } else if (signalDetails.getServiceIds().contains(serviceId)) {
+            } else if (signalDetails.getServiceIds().containsAll(serviceIdentifiers)) {
                 pSignalHelper.getSignals().add(signalDetails.getSignalId());
 
                 // Check the signal is updating from default to severe
-                if (higherSeverityId != signalDetails.getSeverityId() && higherSeverityId == severityId) {
-                    signalDetails.setSeverityId(severityId);
+//                if (higherSeverityId != signalDetails.getSeverityId() && higherSeverityId == severityId) {
+//                    signalDetails.setSeverityId(severityId);
+//                    signalChanges.setSeverityChanged(true);
+//                }
+                if (severityLevels.indexOf(signalDetails.getSeverityId().toString()) < severityLevels.indexOf(severityId)) {
+                    signalDetails.setSeverityId(Integer.valueOf(severityId));
                     signalChanges.setSeverityChanged(true);
                 }
 
-                signalChanges.setServiceAdded(!signalDetails.getServiceIds().contains(serviceId));
+                signalChanges.setServiceAdded(!signalDetails.getServiceIds().containsAll(serviceIdentifiers));
 
                 pSignalHelper.getSignalDetailChanges().put(signalDetails.getSignalId(), signalChanges);
                 pSignalHelper.getSignalDetailsMap().put(signalDetails.getSignalId(), signalDetails);
@@ -379,19 +442,23 @@ public class WarningSignalProcessor {
                 }
 
                 // Check the signal is updating from default to severe
-                if (higherSeverityId != signalDetails.getSeverityId() && higherSeverityId == severityId) {
-                    signalDetails.setSeverityId(severityId);
+//                if (higherSeverityId != signalDetails.getSeverityId() && higherSeverityId == severityId) {
+//                    signalDetails.setSeverityId(severityId);
+//                    signalChanges.setSeverityChanged(true);
+//                }
+                if (severityLevels.indexOf(signalDetails.getSeverityId().toString()) < severityLevels.indexOf(severityId)) {
+                    signalDetails.setSeverityId(Integer.valueOf(severityId));
                     signalChanges.setSeverityChanged(true);
                 }
 
-                signalChanges.setServiceAdded(!signalDetails.getServiceIds().contains(serviceId));
+                signalChanges.setServiceAdded(!signalDetails.getServiceIds().containsAll(serviceIdentifiers));
                 pSignalHelper.getSignalDetailChanges().put(signalDetails.getSignalId(), signalChanges);
                 pSignalHelper.getSignalDetailsMap().put(signalDetails.getSignalId(), signalDetails);
             }
 
             serviceIds.addAll(signalDetails.getServiceIds());
         }
-        serviceIds.add(serviceId);
+        serviceIds.addAll(serviceIdentifiers);
 
         return OpenSignals.builder()
                 .ewSignalHelper(ewSignalHelper)
diff --git a/src/main/java/com/heal/signal/detector/scheduler/DroppedAnomalyChecker.java b/src/main/java/com/heal/signal/detector/scheduler/DroppedAnomalyChecker.java
index 0e3b952..2c7b194 100644
--- a/src/main/java/com/heal/signal/detector/scheduler/DroppedAnomalyChecker.java
+++ b/src/main/java/com/heal/signal/detector/scheduler/DroppedAnomalyChecker.java
@@ -3,9 +3,8 @@ package com.heal.signal.detector.scheduler;
 import com.heal.configuration.enums.SignalType;
 import com.heal.signal.detector.pojos.FailedOpenSignalPojo;
 import com.heal.signal.detector.process.BatchSignalProcessor;
+import com.heal.signal.detector.process.GenericSignalProcessor;
 import com.heal.signal.detector.process.InfoSignalProcessor;
-import com.heal.signal.detector.process.ProblemSignalProcessor;
-import com.heal.signal.detector.process.WarningSignalProcessor;
 import com.heal.signal.detector.util.LocalQueues;
 import lombok.extern.slf4j.Slf4j;
 import org.springframework.beans.factory.annotation.Autowired;
@@ -18,8 +17,6 @@ import java.util.concurrent.TimeUnit;
 @Slf4j
 @Component
 public class DroppedAnomalyChecker {
-    @Autowired
-    private WarningSignalProcessor warningSignalProcessor;
 
     @Autowired
     private BatchSignalProcessor batchSignalProcessor;
@@ -28,7 +25,7 @@ public class DroppedAnomalyChecker {
     private InfoSignalProcessor infoSignalProcessor;
 
     @Autowired
-    private ProblemSignalProcessor problemSignalProcessor;
+    GenericSignalProcessor genericSignalProcessor;
 
     @Autowired
     private LocalQueues localQueues;
@@ -48,14 +45,12 @@ public class DroppedAnomalyChecker {
             log.debug("Received anomaly data from failed queue anomalyId: {}, signalType: {}, accountIdentifier: {}",
                     failedSignals.getAnomalyEvent().getAnomalyId(), failedSignals.getSignalType(), failedSignals.getAccount().getIdentifier());
 
-            if (SignalType.EARLY_WARNING == failedSignals.getSignalType()) {
-                warningSignalProcessor.processEarlyWarningEvent(failedSignals.getAnomalyEvent(), failedSignals.getKpiEntity(), failedSignals.getAccount());
+            if (SignalType.EARLY_WARNING == failedSignals.getSignalType() || SignalType.PROBLEM == failedSignals.getSignalType()) {
+                genericSignalProcessor.processSignalEvent(failedSignals.getSignalType(), failedSignals.getAnomalyEvent(), failedSignals.getKpiEntity(), failedSignals.getAccount(), failedSignals.getAppIds(), failedSignals.getSignalIds());
             } else if (SignalType.BATCH_JOB == failedSignals.getSignalType()) {
                 batchSignalProcessor.processBatchJobEvent(failedSignals.getAnomalyEvent(), failedSignals.getAccount());
             } else if (SignalType.INFO == failedSignals.getSignalType()) {
                 infoSignalProcessor.processInfoEvent(failedSignals.getAnomalyEvent(), failedSignals.getCompInstKpiEntity(), failedSignals.getAccount());
-            } else if (SignalType.PROBLEM == failedSignals.getSignalType()) {
-                problemSignalProcessor.processProblemEvent(failedSignals.getAnomalyEvent(), failedSignals.getKpiEntity(), failedSignals.getAccount());
             }
 
         }
diff --git a/src/main/java/com/heal/signal/detector/scheduler/OSDataPushScheduler.java b/src/main/java/com/heal/signal/detector/scheduler/OSDataPushScheduler.java
index 41766bb..b30d2a5 100644
--- a/src/main/java/com/heal/signal/detector/scheduler/OSDataPushScheduler.java
+++ b/src/main/java/com/heal/signal/detector/scheduler/OSDataPushScheduler.java
@@ -1,5 +1,6 @@
 package com.heal.signal.detector.scheduler;
 
+import com.heal.configuration.pojos.opensearch.Anomalies;
 import com.heal.signal.detector.config.OpenSearchConfig;
 import com.heal.signal.detector.opensearch.SignalRepo;
 import com.heal.signal.detector.pojos.AnomalyHelper;
@@ -20,9 +21,9 @@ import org.springframework.scheduling.annotation.Scheduled;
 import org.springframework.stereotype.Service;
 
 import java.util.*;
-import java.util.concurrent.CompletableFuture;
 import java.util.concurrent.ConcurrentLinkedQueue;
 import java.util.concurrent.TimeUnit;
+import java.util.stream.Collectors;
 
 /**
  * <AUTHOR> Prasad - 05-04-2022
@@ -162,7 +163,9 @@ public class OSDataPushScheduler {
 
             try {
                 accIndexRestClientMap.values().forEach(c ->
-                        CompletableFuture.runAsync(() -> {
+                        // TODO: Temp fix as anomaly was not getting updated
+//                        CompletableFuture.runAsync(() ->
+                        {
                             try {
                                 BulkResponse response = c.getOpenSearchClient().bulk(c.getBulkRequest());
                                 if (response.errors()) {
@@ -180,7 +183,7 @@ public class OSDataPushScheduler {
                                 log.error("Exception during bulk indexing operation: ", e);
                                 metrics.updateErrors();
                             }
-                        }));
+                        });
             } catch (Exception e) {
                 log.error("Exception while bulk operation in OpenSearch.", e);
                 metrics.updateErrors();
@@ -201,20 +204,71 @@ public class OSDataPushScheduler {
         long st = System.currentTimeMillis();
         int size = anomalyHelperQueue.size();
         try {
-            int dataCounter = -1;
             List<AnomalyHelper> notUpdatedAnomalies = new ArrayList<>();
+            List<AnomalyHelper> anomalyHelperList = new ArrayList<>();
+            int dataCounter = -1;
             while (anomalyHelperQueue.peek() != null && ++dataCounter < osBatchSize) {
-                AnomalyHelper helper = anomalyHelperQueue.poll();
-
-                boolean isUpdated = signalRepo.updateAnomalyIndex(helper.getAnomalyId(), helper.getAnomalyTime(), helper.getSignalIds(), helper.getAccountId());
-                if (!isUpdated && helper.getRetry() > 0) {
-                    helper.setRetry(helper.getRetry() - 1);
-                    notUpdatedAnomalies.add(helper);
-                } else if (helper.getRetry() <= 0) {
-                    metrics.updateErrors();
-                    log.error("Tried to update the signal id:{} for anomaly id:{}, attempted {} times, could not update.", helper.getSignalIds(), helper.getAnomalyId(), maxRetry);
+                anomalyHelperList.add(anomalyHelperQueue.poll());
+            }
+
+            if (!anomalyHelperList.isEmpty()) {
+                Set<String> anomalyIds = new HashSet<>();
+                Set<String> accountIds = new HashSet<>();
+                final long[] initialAnomalyTime = {System.currentTimeMillis()};
+                anomalyHelperList.forEach(anomalyHelper -> {
+                    anomalyIds.add(anomalyHelper.getAnomalyId());
+                    accountIds.add(anomalyHelper.getAccountId());
+                    initialAnomalyTime[0] = Math.min(initialAnomalyTime[0], anomalyHelper.getAnomalyTime());
+                });
+
+                Set<Anomalies> anomaliesSet = signalRepo.getAnomalyById(anomalyIds, accountIds, initialAnomalyTime[0]);
+                if (anomaliesSet == null || anomaliesSet.isEmpty()) {
+                    log.error("No anomaly details found for anomalies {}", anomaliesSet);
+
+                    anomalyHelperList.forEach(anomalyHelper -> {
+                        if (anomalyHelper.getRetry() > 0) {
+                            anomalyHelper.setRetry(anomalyHelper.getRetry() - 1);
+                            notUpdatedAnomalies.add(anomalyHelper);
+                        } else {
+                            metrics.updateErrors();
+                            log.error("Tried to get anomaly details for the for anomaly id:{}, signal id:{}, attempted {} times, could not get.",
+                                    anomalyHelper.getAnomalyId(), anomalyHelper.getSignalIds(), maxRetry);
+                        }
+                    });
+                } else {
+
+                    Map<String, Anomalies> anomalyMap = anomaliesSet.stream().collect(Collectors.toMap(Anomalies::getAnomalyId, c -> c, (a, b) -> a));
+
+                    for (AnomalyHelper helper : anomalyHelperList) {
+                        Anomalies anomalyDetail = anomalyMap.get(helper.getAnomalyId());
+                        if (anomalyDetail == null) {
+                            log.error("Could not get the anomaly details for accountId:{}, anomalyId:{}, signalIds:{}, anomalyTime:{}",
+                                    helper.getAccountId(), helper.getAnomalyId(), helper.getSignalIds(), helper.getAnomalyTime());
+                            metrics.updateErrors();
+
+                            if (helper.getRetry() > 0) {
+                                helper.setRetry(helper.getRetry() - 1);
+                                notUpdatedAnomalies.add(helper);
+                            } else {
+                                metrics.updateErrors();
+                                log.error("Tried to get anomaly details for the for anomaly id:{}, signal id:{}, attempted {} times, could not get.",
+                                        helper.getAnomalyId(), helper.getSignalIds(), maxRetry);
+                            }
+                        } else {
+                            //Add multi anomaly get query here
+                            boolean isUpdated = signalRepo.updateAnomalyIndex(helper.getAnomalyId(), helper.getAnomalyTime(), helper.getSignalIds(), helper.getAccountId(), anomalyDetail);
+                            if (!isUpdated && helper.getRetry() > 0) {
+                                helper.setRetry(helper.getRetry() - 1);
+                                notUpdatedAnomalies.add(helper);
+                            } else if (helper.getRetry() <= 0) {
+                                metrics.updateErrors();
+                                log.error("Tried to update the signal id:{} for anomaly id:{}, attempted {} times, could not update.", helper.getSignalIds(), helper.getAnomalyId(), maxRetry);
+                            }
+                        }
+                    }
                 }
             }
+
             notUpdatedAnomalies.forEach(this::addToAnomalyHelper);
             log.debug("No. of not updated anomalies:{}, anomaly helper queue size:{}, osBatchSize:{}", notUpdatedAnomalies.size(), size, osBatchSize);
         } catch (Exception e) {
diff --git a/src/main/java/com/heal/signal/detector/scheduler/SignalChecker.java b/src/main/java/com/heal/signal/detector/scheduler/SignalChecker.java
index df9d2b3..bd93fb3 100644
--- a/src/main/java/com/heal/signal/detector/scheduler/SignalChecker.java
+++ b/src/main/java/com/heal/signal/detector/scheduler/SignalChecker.java
@@ -1,8 +1,7 @@
 package com.heal.signal.detector.scheduler;
 
-import com.heal.configuration.pojos.Account;
 import com.heal.configuration.pojos.opensearch.SignalDetails;
-import com.heal.signal.detector.cache.CacheWrapper;
+import com.heal.configuration.util.DateHelper;
 import com.heal.signal.detector.opensearch.SignalRepo;
 import com.heal.signal.detector.pojos.SignalStatus;
 import com.heal.signal.detector.service.ForwarderToQueue;
@@ -12,19 +11,21 @@ import com.heal.signal.detector.util.RedisUtilities;
 import lombok.extern.slf4j.Slf4j;
 import org.springframework.beans.factory.annotation.Autowired;
 import org.springframework.beans.factory.annotation.Value;
-import org.springframework.scheduling.annotation.Async;
-import org.springframework.scheduling.annotation.Scheduled;
 import org.springframework.stereotype.Component;
 
 import java.util.*;
+import java.util.concurrent.ConcurrentHashMap;
+import java.util.concurrent.atomic.AtomicLong;
 import java.util.stream.Collectors;
 
 @Slf4j
 @Component
 public class SignalChecker {
 
+    private final ConcurrentHashMap<String, Integer> signalRetryCountMap = new ConcurrentHashMap<>();
+
     @Autowired
-    private HealthMetrics healthMetrics;
+    HealthMetrics healthMetrics;
 
     @Autowired
     RedisUtilities redisUtilities;
@@ -38,8 +39,8 @@ public class SignalChecker {
     @Autowired
     ForwarderToQueue forwarder;
 
-    @Autowired
-    CacheWrapper wrapper;
+    @Value("${opensearch.signals.index:heal_signals}")
+    public String signalsIndex;
 
     @Value("${info.signal.close.window.time.minutes:5}")
     private int infoSignalIdleTimeInMin;
@@ -50,83 +51,129 @@ public class SignalChecker {
     @Value("${signal.close.window.time.minutes:15}")
     private int signalIdleTimeInMin;
 
-    @Async(value = "ThreadPoolTaskExecutorSignalChecker")
-    @Scheduled(initialDelay = 1000, fixedRateString = "${signal.cleaner.scheduler.milliseconds:30000}")
+    @Value(("${heal.global.account.identifier:e573f852-5057-11e9-8fd2-b37b61e52317}"))
+    private String globalAccountIdentifier;
+
+    @Value("${signal.checker.retry.signal.details.from.os:3}")
+    public int retryCountLimit;
+
+//    @Async(value = "ThreadPoolTaskExecutorSignalChecker")
+//    @Scheduled(initialDelay = 1000, fixedRateString = "${signal.cleaner.scheduler.milliseconds:60000}")
     public void checkSignals() {
         log.trace("Signal checker scheduler method called.");
+        long stepStartTime = System.currentTimeMillis();
+        long startTime = stepStartTime;
 
-        Set<SignalDetails> signalDetailList = new HashSet<>();
         try {
-            // Getting all the account details.
-            List<Account> accounts = wrapper.getAccounts();
-            if (accounts.isEmpty()) {
-                log.error("Could not find account details from redis");
-                return;
-            }
+            // ServiceId, <SignalId, SignalTime>
+            Map<String, Map<String, Long>> serviceSignals = redisUtilities.getOpenSignalsInRedis();
+            log.debug("Total number of service signals from redis cache is {}", serviceSignals.size());
+            log.debug("Time taken to get signals from Redis: {} ms", (System.currentTimeMillis() - stepStartTime));
+            stepStartTime = System.currentTimeMillis();
+
+            if (!serviceSignals.isEmpty()) {
+                List<String> osIndexNames = signalRepo.getExistingSignalsIndexNames(globalAccountIdentifier);
+                log.debug("Total number of signals index found from OpenSearch is {}", osIndexNames.size());
+                log.debug("Time taken to get existing index names from OpenSearch: {} ms", (System.currentTimeMillis() - stepStartTime));
+                stepStartTime = System.currentTimeMillis();
+
+                Set<String> signalIds = new HashSet<>();
+                AtomicLong minSignalTime = new AtomicLong(System.currentTimeMillis());
+                serviceSignals.values().forEach(c -> {
+                    c.values().forEach(signalTime -> minSignalTime.set(Math.min(minSignalTime.get(), signalTime)));
+                    signalIds.addAll(c.keySet());
+                });
+                log.debug("Time taken to collect signal IDs from Redis data: {} ms", (System.currentTimeMillis() - stepStartTime));
+                stepStartTime = System.currentTimeMillis();
+
+                long et = System.currentTimeMillis();
+                List<SignalDetails> signalDetailsList = signalRepo.getSignalById(signalIds, minSignalTime.get(), et, globalAccountIdentifier);
+                log.debug("Time taken to get signal details by ID from OpenSearch: {} ms", (System.currentTimeMillis() - stepStartTime));
+                if (signalDetailsList == null || signalDetailsList.isEmpty()) {
+                    log.warn("No signal details found from OpenSearch for signalTime from {}, to {},  Signals {}.", minSignalTime.get(), et, signalIds);
+                    signalDetailsList = new ArrayList<>();
+                }
+                Map<String, SignalDetails> signalDetailsMap = signalDetailsList.stream()
+                        .collect(Collectors.toMap(SignalDetails::getSignalId, c -> c, (a, b) -> b));
+
+                stepStartTime = System.currentTimeMillis();
+                //Removing Closed/Upgraded/NoOpenSearchIndex signal details from redis in this loop
+                serviceSignals.forEach((serviceId, signalIdToTimeMap) ->
 
-            log.debug("Total number of accounts from redis cache is:{}", accounts.size());
-            //Skipping global account
-            signalDetailList = accounts.stream()
-                    .filter(a -> !a.getIdentifier().equalsIgnoreCase("e573f852-5057-11e9-8fd2-b37b61e52317"))
-                    .map(a -> {
-                        // ServiceId, <SignalId, SignalTime>
-                        Map<String, Map<String, Long>> serviceSignals = redisUtilities.getOpenSignalsInRedis(a.getIdentifier());
+                        signalIdToTimeMap.forEach((signalId, signalTime) -> {
 
-                        log.debug("Total number of service signals from redis cache is {}, account:{}", serviceSignals.size(), a.getIdentifier());
+                            boolean isSignalsIndexPresent = isSignalsIndexPresent(osIndexNames, signalTime);
 
-                        //Loop all the signals for account and remove the closed signals from redis cache.
-                        serviceSignals.forEach((serviceId, value) -> value.forEach((signalId, signalTime) -> {
-                            boolean isSignalsIndexPresent = signalRepo.isSignalsIndexPresent(a.getIdentifier(), signalTime, signalTime);
                             if (isSignalsIndexPresent) {
-                                SignalDetails signalDetails = signalRepo.getSignalById(signalId, signalTime, a.getIdentifier());
-                                log.trace("Check the SignalId:{}, startTime:{}, AccountId:{}, signal details:{}", signalId, signalTime, a.getIdentifier(), signalDetails);
-                                if (signalDetails != null && !signalDetails.getCurrentStatus().equalsIgnoreCase("OPEN")) {
-                                    log.debug("Removing the closed signal from the redis cache. AccountId:{}, SignalId:{}, SignalTime:{}, ServiceId:{}",
-                                            a.getIdentifier(), signalId, signalTime, serviceId);
-                                    redisUtilities.updateServiceSignal(a.getIdentifier(), signalId, signalTime, serviceId, signalDetails.getCurrentStatus());
+                                SignalDetails signalDetails = signalDetailsMap.get(signalId);
+                                log.trace("Checking SignalId:{}, startTime:{}, signal details:{}", signalId, signalTime, signalDetails);
+                                if (signalDetails == null) {
+                                    //If no signal details found from OS
+                                    int currentRetryCount = signalRetryCountMap.getOrDefault(signalId, 0);
+                                    if (currentRetryCount < retryCountLimit) {
+                                        log.warn("No signal details found from OpenSearch for signal {}. Retrying (attempt {}/{}).", signalId, currentRetryCount + 1, retryCountLimit);
+                                        signalRetryCountMap.put(signalId, currentRetryCount + 1);
+                                    } else {
+                                        log.warn("No signal details found from OpenSearch for signal {} after {} retries. Marking as closed in redis cache.", signalId, retryCountLimit);
+                                        redisUtilities.updateServiceSignal(signalId, signalTime, Collections.singleton(serviceId), SignalStatus.CLOSED.name());
+                                        redisUtilities.deleteSignalDetails(signalId);
+                                        signalRetryCountMap.remove(signalId); // Remove from retry map once closed
+                                    }
+                                } else {
+                                    // If signal detail is found, remove from retry map
+                                    signalRetryCountMap.remove(signalId);
+                                    if (!signalDetails.getCurrentStatus().equalsIgnoreCase(SignalStatus.OPEN.name())) {
+
+                                        log.debug("Removing the closed signal from the redis cache. AccountId:{}, SignalId:{}, SignalTime:{}, ServiceId:{}",
+                                                signalDetails.getAccountIdentifiers(), signalId, signalTime, serviceId);
+                                        redisUtilities.updateServiceSignal(signalId, signalTime, Collections.singleton(serviceId), signalDetails.getCurrentStatus());
+                                        redisUtilities.deleteSignalDetails(signalId);
+                                    }
                                 }
                             } else {
-                                log.debug("Removing the signal from the redis cache because it's corresponding OS Index is not present." +
-                                        " AccountId:{}, SignalId:{}, SignalTime:{}, ServiceId:{}", a.getIdentifier(), signalId, signalTime, serviceId);
-                                redisUtilities.updateServiceSignal(a.getIdentifier(), signalId, signalTime, serviceId, SignalStatus.CLOSED.name());
+                                //If Signals Index already deleted from OS
+                                log.debug("Removing the signal from the redis cache because it's corresponding OpenSearch Index is not present." +
+                                        " SignalId:{}, SignalTime:{}, ServiceId:{}", signalId, signalTime, serviceId);
+                                redisUtilities.updateServiceSignal(signalId, signalTime, Collections.singleton(serviceId), SignalStatus.CLOSED.name());
+                                redisUtilities.deleteSignalDetails(signalId);
                             }
                         }));
-                        Set<SignalDetails> signalDetails;
-                        try {
-                             signalDetails = signalRepo.getOpenSignals(a.getIdentifier(), false);
-                        } catch (Exception e) {
-                            log.error("Error occurred while fetching open signals for accountIdentifier: {}", a.getIdentifier(), e);
-                            return null;
-                        }
-                        if (signalDetails.isEmpty()) {
-                            log.debug("No open signals found.");
-                            return null;
-                        }
-
-                        return signalDetails;
-                    })
-                    .filter(Objects::nonNull)
-                    .flatMap(Collection::stream)
-                    .collect(Collectors.toSet());
+                log.debug("Time taken for Redis reconciliation: {} ms", (System.currentTimeMillis() - stepStartTime));
+                stepStartTime = System.currentTimeMillis();
+            }
         } catch (Exception e) {
             healthMetrics.updateErrors();
             log.error("Error while checking the signal close details and remove the signals from redis cache.", e);
+            log.info("Total time taken for checkSignals (ended in error): {} ms", (System.currentTimeMillis() - startTime));
+            return;
         }
 
-        // Get the open signals for all the accounts
-        if (signalDetailList.isEmpty()) {
-            log.debug("No open signals not found.");
-            return;
+        Set<SignalDetails> openSignalDetailsSet = new HashSet<>();
+        try {
+            openSignalDetailsSet = new HashSet<>(signalRepo.getOpenSignals(globalAccountIdentifier, false, false));
+            log.info("Time taken to get all open signals of size {} from OpenSearch: {} ms", openSignalDetailsSet.size(), (System.currentTimeMillis() - stepStartTime));
+            if (openSignalDetailsSet.isEmpty()) {
+                log.debug("No open signals found from OpenSearch.");
+                return;
+            }
+            log.debug("No. of open signals:{}", openSignalDetailsSet.size());
+        } catch (Exception e) {
+            healthMetrics.updateErrors();
+            log.error("Error while getting open signals from OpenSearch.", e);
+            log.info("Total time taken for checkSignals (ended in error): {} ms", (System.currentTimeMillis() - startTime));
         }
-        log.debug("No of open signals:{}", signalDetailList.size());
 
-        signalDetailList.forEach(signalDetails -> {
+        stepStartTime = System.currentTimeMillis();
+        //Marking Under Maintenance/No Anomaly Update Signals' status to Closed in OpenSearch in this loop
+        openSignalDetailsSet.forEach(signalDetails -> {
             try {
                 if (signalDetails.getStartedTime() == null || signalDetails.getUpdatedTime() == null) {
-                    log.error("Start time or update time is null for signalId:{}, startTime:{}, updateTime:{}.", signalDetails.getSignalId(), signalDetails.getStartedTime(), signalDetails.getUpdatedTime());
+                    log.error("Start time or update time is null for signalId:{}, startTime:{}, updateTime:{}.",
+                            signalDetails.getSignalId(), signalDetails.getStartedTime(), signalDetails.getUpdatedTime());
                     healthMetrics.updateErrors();
                     return;
                 }
+
                 long idleTime = commons.getSignalIdealTime(signalDetails.getSignalType(), infoSignalIdleTimeInMin, batchSignalIdleTimeInMin, signalIdleTimeInMin);
                 long latestTime = Math.max(signalDetails.getUpdatedTime(), signalDetails.getTxnAnomalyTime() == null ? 0 : signalDetails.getTxnAnomalyTime());
                 long currentTime = Calendar.getInstance(TimeZone.getTimeZone("UTC")).getTimeInMillis();
@@ -138,11 +185,9 @@ public class SignalChecker {
                     newMetaData.put("end_time", String.valueOf(latestTime + idleTime));
                 }
 
-                String accountId = (signalDetails.getMetadata() != null && signalDetails.getMetadata().containsKey("account_id")) ?
-                        signalDetails.getMetadata().get("account_id") : null;
-
-                if (accountId == null) {
-                    log.error("Account id does not exists for signalId:{}.", signalDetails.getSignalId());
+                Set<String> accountIdentifierSet = signalDetails.getAccountIdentifiers();
+                if (accountIdentifierSet == null) {
+                    log.error("Account identifiers does not exist for signalId:{}.", signalDetails.getSignalId());
                     healthMetrics.updateErrors();
                     return;
                 }
@@ -151,38 +196,43 @@ public class SignalChecker {
                 if (signalDetails.getMetadata().containsKey("lastMaintenanceExcluded")) {
                     long lastMaintenanceExcluded = Long.parseLong(signalDetails.getMetadata().getOrDefault("lastMaintenanceExcluded", "0"));
                     if (lastMaintenanceExcluded != 0L) {
-                        log.info("curTime : {}, last maintenance exclude time :{}, SIGNAL_IDLE_TIME : {}", currentTime, lastMaintenanceExcluded, idleTime);
+                        log.info("currTime : {}, last maintenance exclude time :{}, SIGNAL_IDLE_TIME : {}", currentTime, lastMaintenanceExcluded, idleTime);
                         isMaintenanceShouldExclude = !((currentTime - idleTime) > lastMaintenanceExcluded);
                     }
                 }
 
                 boolean isMaintenance = false;
                 if (!isMaintenanceShouldExclude) {
-                    isMaintenance = commons.isServicesUnderMaintenance(accountId, signalDetails.getServiceIds(), signalDetails.getSignalId());
+                    isMaintenance = commons.isServicesUnderMaintenance(accountIdentifierSet, signalDetails.getServiceIds(), signalDetails.getSignalId());
                 }
 
                 if (isMaintenance) {
                     newMetaData.put("closing_reason", "Signal closed as all impacted services are put under maintenance.");
                     newMetaData.put("end_time", String.valueOf(latestTime));
                 } else {
-                    newMetaData.put("closing_reason", "Signal closed as no new event is received on any of the impacted services in last " + idleTime / 60000 + " minutes.");
+                    newMetaData.put("closing_reason",
+                            "Signal closed as no new event is received on any of the impacted services in last " + idleTime / 60000 + " minutes.");
                     newMetaData.put("end_time", String.valueOf(latestTime + idleTime));
                 }
-                if (((currentTime - latestTime) > idleTime) || (signalDetails.getMetadata() != null &&
-                        signalDetails.getMetadata().containsKey("end_time")) || isMaintenance) {
+
+                if (((currentTime - latestTime) > idleTime)
+                        || (signalDetails.getMetadata() != null && signalDetails.getMetadata().containsKey("end_time"))
+                        || isMaintenance) {
                     signalDetails.setCurrentStatus(SignalStatus.CLOSED.name());
                     signalDetails.getMetadata().putAll(newMetaData);
-                    boolean isUpdated = signalRepo.closeSignal(signalDetails, accountId);
+
+                    //TODO:Can we introduce Async close here by CompletableFuture?
+                    boolean isUpdated = signalRepo.closeSignal(signalDetails, accountIdentifierSet.stream().toList().get(0), null);
                     log.info("Dead signal : {} is successfully closed.", signalDetails.getSignalId());
                     healthMetrics.updateSignalCloseCount();
                     healthMetrics.updateSnapshots(signalDetails.getSignalId() + "_CLOSED", 1);
 
                     if (isUpdated) {
-                        forwarder.sendSignalMessages(commons.getSignalProto(accountId, signalDetails, true,
+                        forwarder.sendSignalMessages(commons.getSignalProto(signalDetails, true,
                                 false, false, true, null));
                     }
                 } else {
-                    forwarder.sendSignalMessages(commons.getSignalProto(accountId, signalDetails, true,
+                    forwarder.sendSignalMessages(commons.getSignalProto(signalDetails, true,
                             false, false, false, null));
                 }
             } catch (Exception e) {
@@ -190,6 +240,16 @@ public class SignalChecker {
                 log.error("Error while checking the signal close details. signal id:{}", signalDetails.getSignalId(), e);
             }
         });
+        log.debug("Time taken for processing and closing idle signals: {} ms", (System.currentTimeMillis() - stepStartTime));
+        log.info("Total time taken for checkSignals: {} ms", (System.currentTimeMillis() - startTime));
+    }
+
+    private boolean isSignalsIndexPresent(List<String> indexNamesFromOS, Long signalTime) {
+        List<String> indexNames = new ArrayList<>();
+        DateHelper.getWeeksAsString(signalTime, signalTime).forEach(date ->
+                indexNames.add(signalsIndex + "_" + date));
+
+        return indexNamesFromOS.contains(indexNames.get(0));
     }
 
 }
diff --git a/src/main/java/com/heal/signal/detector/util/Commons.java b/src/main/java/com/heal/signal/detector/util/Commons.java
index 666a77e..96e99ff 100644
--- a/src/main/java/com/heal/signal/detector/util/Commons.java
+++ b/src/main/java/com/heal/signal/detector/util/Commons.java
@@ -9,29 +9,30 @@ import com.heal.configuration.enums.SignalType;
 import com.heal.configuration.pojos.*;
 import com.heal.configuration.pojos.opensearch.SignalDetails;
 import com.heal.signal.detector.cache.CacheWrapper;
-import com.heal.signal.detector.opensearch.SignalRepo;
 import com.heal.signal.detector.pojos.SignalStatus;
 import lombok.extern.slf4j.Slf4j;
 import org.springframework.beans.factory.annotation.Autowired;
 import org.springframework.beans.factory.annotation.Value;
-import org.springframework.stereotype.Repository;
+import org.springframework.stereotype.Component;
 
 import java.sql.Timestamp;
 import java.util.*;
+import java.util.concurrent.atomic.AtomicInteger;
+import java.util.concurrent.atomic.AtomicLong;
 import java.util.stream.Collectors;
 
 @Slf4j
-@Repository
+@Component
 public class Commons {
 
     @Autowired
     RedisUtilities redisUtilities;
 
-    @Autowired
-    SignalRepo signalRepo;
-
     @Value("${signal.latest.events.count:10}")
-    public int lastNEvents;
+    protected int lastNEvents;
+
+    @Value("${early.warning.processing.include.outbounds:1}")
+    protected int includeOutbounds;
 
     @Autowired
     HealthMetrics metrics;
@@ -40,7 +41,7 @@ public class Commons {
     CacheWrapper wrapper;
 
     @Value("${offset.from.gmt:19800000}")
-    private long offsetFromGMT;
+    protected long offsetFromGMT;
 
     public boolean isUnderMaintenance(List<MaintenanceDetails> maintenanceDetailsList, long timeInMilliSecs) {
         Timestamp timestamp;
@@ -59,7 +60,7 @@ public class Commons {
             if (startTime == null) return false;
             log.debug("isUnderMaintenance() : data time : {}, maintenance start time : {}, maintenance end time : {}.", timestamp.getTime(), startTime.getTime(), endTime != null ? endTime.getTime() : null);
             if (endTime == null) {
-                isUnderMaintenance =  (startTime.before(timestamp) || startTime.equals(timestamp));
+                isUnderMaintenance = (startTime.before(timestamp) || startTime.equals(timestamp));
             } else {
                 isUnderMaintenance =
                         ((startTime.before(timestamp) || startTime.equals(timestamp)) && (endTime.after(timestamp) || endTime.equals(timestamp)));
@@ -70,41 +71,65 @@ public class Commons {
     }
 
 
-    public boolean isServicesUnderMaintenance(String accountId, Set<String> services, String signalId) {
-        log.debug("Checking the maintenance window for account identifier : {}, and services : {}, signalId:{}", accountId, services, signalId);
+    public boolean isServicesUnderMaintenance(Set<String> accountIdentifierSet, Set<String> services, String signalId) {
+        log.debug("Checking the maintenance window for account identifier : {}, and services : {}, signalId:{}", accountIdentifierSet, services, signalId);
         if (services == null || services.isEmpty()) return false;
+        if (accountIdentifierSet == null || accountIdentifierSet.isEmpty()) return false;
 
-        int underMaintenanceCount = 0;
-        for (String serviceId : services) {
-            List<MaintenanceDetails> maintenanceDetailsList = wrapper.getServiceMaintenanceDetails(accountId, serviceId);
-            if (maintenanceDetailsList == null || maintenanceDetailsList.isEmpty()) {
-                log.info("Maintenance details is not found in DS config data for service :{} and account :{}, signalId:{}", serviceId, accountId, signalId);
-                continue;
-            }
+        Map<String, Set<String>> accSvcMap = accountIdentifierSet.stream()
+                .collect(Collectors.toMap(a -> a, a -> {
+                    List<BasicEntity> serviceList = wrapper.getAccountServices(a);
 
-            boolean isMaintenance = isUnderMaintenance(maintenanceDetailsList, Calendar.getInstance().getTimeInMillis() + offsetFromGMT);
-            log.debug("Is service :{} of account :{} is under maintenance :{}, signalId:{}.", serviceId, accountId, isMaintenance, signalId);
-            if (isMaintenance) {
-                underMaintenanceCount += 1;
-            }
+                    return serviceList.stream()
+                            .filter(c -> c.getStatus() == 1)
+                            .map(BasicEntity::getIdentifier)
+                            .filter(services::contains)
+                            .collect(Collectors.toSet());
+                }));
+
+        int validSvcListSize = accSvcMap.values().stream().flatMap(Collection::stream).collect(Collectors.toSet()).size();
+
+        if (services.size() != validSvcListSize) {
+            log.error("Invalid services present in the signal service list. Marking services as not under maintenance for signalId:{}.", signalId);
+            return false;
         }
-        log.info("services : {} of account : {} are under maintenance:{}, signalId:{}", services, accountId, underMaintenanceCount, signalId);
-        return services.size() == underMaintenanceCount;
+
+        AtomicInteger underMaintenanceCount = new AtomicInteger();
+        accSvcMap.forEach((accIdentifier, svcList) -> {
+            for (String serviceId : svcList) {
+                List<MaintenanceDetails> maintenanceDetailsList = wrapper.getServiceMaintenanceDetails(accIdentifier, serviceId);
+                if (maintenanceDetailsList == null || maintenanceDetailsList.isEmpty()) {
+                    log.info("Maintenance details is not found in DS config data for service :{} and account :{}, signalId:{}", serviceId, accIdentifier, signalId);
+                    continue;
+                }
+
+                boolean isMaintenance = isUnderMaintenance(maintenanceDetailsList, Calendar.getInstance().getTimeInMillis() + offsetFromGMT);
+                log.debug("Is service :{} of account :{} is under maintenance :{}, signalId:{}.", serviceId, accIdentifier, isMaintenance, signalId);
+                if (isMaintenance) {
+                    underMaintenanceCount.addAndGet(1);
+                }
+            }
+
+            log.info("services : {} of account : {} are under maintenance:{}, signalId:{}", services, accIdentifier, underMaintenanceCount, signalId);
+        });
+
+        // All the services in signal are under maintenance then true, otherwise false.
+        return validSvcListSize == underMaintenanceCount.get();
     }
 
     public long getSignalIdealTime(String signalTypeStr, int infoSignalIdleTimeInMin, int batchSignalIdleTimeInMin,
                                    int signalIdleTimeInMin) {
-        if(signalTypeStr == null || signalTypeStr.trim().length() == 0) {
+        if (signalTypeStr == null || signalTypeStr.trim().isEmpty()) {
             return -1;
         }
 
         try {
             SignalType signalType = SignalType.valueOf(signalTypeStr);
-            if(SignalType.INFO.compareTo(signalType) == 0) {
+            if (SignalType.INFO.compareTo(signalType) == 0) {
                 return infoSignalIdleTimeInMin * 60000L;
             }
 
-            if(SignalType.BATCH_JOB.compareTo(signalType) == 0) {
+            if (SignalType.BATCH_JOB.compareTo(signalType) == 0) {
                 return batchSignalIdleTimeInMin * 60000L;
             }
 
@@ -118,16 +143,15 @@ public class Commons {
     public Map<String, String> getSignalMetaData(AnomalyEventProtos.AnomalyEvent anomalyEvent, SignalType signalType) {
         Map<String, String> metaData = new HashMap<>();
 
-        metaData.put("account_id", anomalyEvent.getAccountId());
-        if(SignalType.BATCH_JOB.compareTo(signalType) == 0 ) {
+        if (SignalType.BATCH_JOB.compareTo(signalType) == 0) {
             metaData.put("batch_job_id", anomalyEvent.getBatchInfo().getBatchJob());
             metaData.put("kpi_id", anomalyEvent.getBatchInfo().getKpiId());
-            if(anomalyEvent.getBatchInfo().getMetadataMap().getOrDefault("isMaintenanceExcluded", "0").equalsIgnoreCase("1")) {
+            if (anomalyEvent.getBatchInfo().getMetadataMap().getOrDefault("isMaintenanceExcluded", "0").equalsIgnoreCase("1")) {
                 metaData.put("lastMaintenanceExcluded", String.valueOf(anomalyEvent.getEndTimeGMT()));
             }
             metaData.putAll(anomalyEvent.getBatchInfo().getMetadataMap());
         } else {
-            if(anomalyEvent.getKpis().getMetadataMap().getOrDefault("isMaintenanceExcluded", "0").equalsIgnoreCase("1")) {
+            if (anomalyEvent.getKpis().getMetadataMap().getOrDefault("isMaintenanceExcluded", "0").equalsIgnoreCase("1")) {
                 metaData.put("lastMaintenanceExcluded", String.valueOf(anomalyEvent.getEndTimeGMT()));
             }
             metaData.putAll(anomalyEvent.getKpis().getMetadataMap());
@@ -149,79 +173,194 @@ public class Commons {
     }
 
 
-    public SignalDetails createSignal(String signalId, long signalStartTime, long violationTime, long txnViolationTime, String signalStatus, Set<String> rootCauseAnomaliesIds,
-                                      SignalType signalType, Set<String> serviceIds, Set<String> rootCauseServiceIds,
-                                      Set<String> anomalyIds, String entryServiceId, Set<String> relatedSignals, int severityId,
-                                      Map<String, String> metaData) {
-
+    public SignalDetails createSignal(String signalId, long signalStartTime, long violationTime, long txnViolationTime, String signalStatus,
+                                      Set<String> rootCauseAnomaliesIds, SignalType signalType, Set<String> serviceIds, Set<String> rootCauseServiceIds,
+                                      Set<String> anomalyIds, Set<String> entryServiceIds, Set<String> relatedSignals, int severityId,
+                                      Map<String, String> metaData, String accountIdentifier) {
 
-        Set<String> statusDetails = new HashSet<>();
-        statusDetails.add(signalStatus);
-        SignalDetails.SignalDetailsBuilder builder = SignalDetails.builder();
-        builder
+        SignalDetails.SignalDetailsBuilder builder = SignalDetails.builder()
                 .signalId(signalId)
                 .startedTime(signalStartTime)
                 .updatedTime(violationTime)
                 .txnAnomalyTime(txnViolationTime)
                 .currentStatus(signalStatus)
-                .statusDetails(statusDetails)
+                .statusDetails(new HashSet<>() {{
+                    add(signalStatus);
+                }})
                 .signalType(signalType.name())
                 .severityId(severityId)
-                .entryServiceId(entryServiceId)
+                .entryServiceId(entryServiceIds)
                 .relatedSignals(relatedSignals)
                 .serviceIds(serviceIds)
                 .rootCauseAnomalyIds(rootCauseAnomaliesIds)
                 .rootCauseServiceIds(rootCauseServiceIds)
                 .anomalies(anomalyIds)
-                .metadata(metaData);
-        if(signalType.compareTo(SignalType.PROBLEM) == 0) {
+                .metadata(metaData)
+                .accountIdentifiers(new HashSet<>() {{
+                    add(accountIdentifier);
+                }});
+
+        if (signalType.compareTo(SignalType.PROBLEM) == 0) {
             builder.txnAnomalyTime(txnViolationTime);
         }
 
         return builder.build();
     }
 
-    public Set<SignalDetails> getOpenSignals(String accountIdentifier, Set<SignalDetails> openSignals, String serviceIdentifier) {
-        Set<SignalDetails> signals = openSignals
-                .parallelStream()
+    public Set<SignalDetails> processAndGetOpenSignals(Set<Service> services, String accountIdentifier, String anomalyId,
+                                                       Map<String, String> dcDrServiceMap, Map<String, String> drDcServiceMap,
+                                                       Set<SignalDetails> openSignalsFromOS, boolean isEarlyWarning, Set<String> appIds) throws Exception {
+        try {
+            Set<String> sourceAndDestinationServicesSet = services.stream()
+                    .map(service -> {
+                        try {
+                            Set<String> sourceAndDestinationServices = new HashSet<>() {{
+                                add(service.getIdentifier());
+                            }};
+
+                        Set<String> destinationServices = new HashSet<>();
+                        Map<String, Map<String, Long>> openSignalsForApp = new HashMap<>();
+                        appIds.forEach(appId -> {
+                            openSignalsForApp.putAll(redisUtilities.getOpenSignalsForApp(appId));
+                        });
+                        if (!openSignalsForApp.isEmpty()) {
+                            destinationServices = openSignalsForApp.keySet();
+                        }
+
+                        if (destinationServices.isEmpty()) {
+                            log.info("No other services found for appId:{}, serviceId:{}, serviceName:{}, accountId:{} for anomalyId:{}.",
+                                    appIds, service.getIdentifier(), service.getName(), accountIdentifier, anomalyId);
+                        } else {
+                            log.debug("{}:Services found for appId:{}, serviceId:{}, serviceName:{}, accountId:{} for anomalyId:{}.",
+                                    destinationServices.size(), appIds, service.getIdentifier(), service.getName(), accountIdentifier, anomalyId);
+                            sourceAndDestinationServices.addAll(destinationServices);
+                        }
+
+                            sourceAndDestinationServices.addAll(sourceAndDestinationServices.stream().map(svc -> {
+                                        String linkedServiceIdentifierFromOtherAccount = dcDrServiceMap.getOrDefault(svc, drDcServiceMap.get(svc));
+
+                                        if (linkedServiceIdentifierFromOtherAccount != null) {
+                                            log.debug("Linked service {} found in service alias list for service {}", linkedServiceIdentifierFromOtherAccount, svc);
+                                            return linkedServiceIdentifierFromOtherAccount;
+                                        } else {
+                                            log.debug("No linked service found in service alias list for service {}", svc);
+                                            return null;
+                                        }
+                                    })
+                                    .filter(Objects::nonNull)
+                                    .collect(Collectors.toSet()));
+
+                            log.debug("{}:Neighbours/Outbound/Cross Account Linked connections found for serviceId:{}, serviceName:{}, accountId:{} for anomalyId:{}.",
+                                    sourceAndDestinationServices.size(), service.getIdentifier(), service.getName(), accountIdentifier, anomalyId);
+
+                            return sourceAndDestinationServices;
+                        } catch (Exception e) {
+                            log.error("Exception while getting Open Signals related to service {}.", service.getIdentifier());
+                            return null;
+                        }
+                    })
+                    .filter(Objects::nonNull)
+                    .flatMap(Collection::parallelStream)
+                    .collect(Collectors.toSet());
+
+            return getOpenSignals(accountIdentifier, openSignalsFromOS, sourceAndDestinationServicesSet, anomalyId, appIds);
+        } catch (Exception e) {
+            log.error("Exception while getting filtered open signal details from OS and Redis. ", e);
+            throw new Exception("Exception while getting filtered open signal details from OS and Redis. " + e.getMessage());
+        }
+    }
+
+    public Set<SignalDetails> getOpenSignals(String accountIdentifier, Set<SignalDetails> openSignalDetailsFromOS, Set<String> serviceIdentifiers, String anomalyId, Set<String> appIds) {
+        Set<SignalDetails> filteredOpenSignalDetailsFromOS = openSignalDetailsFromOS.parallelStream()
                 .filter(s -> s.getSignalType().equalsIgnoreCase(SignalType.PROBLEM.name())
                         || s.getSignalType().equalsIgnoreCase(SignalType.EARLY_WARNING.name()))
-                .filter(s -> s.getServiceIds().contains(serviceIdentifier))
+                .filter(s -> serviceIdentifiers.stream().anyMatch(serviceIdentifier -> s.getServiceIds().contains(serviceIdentifier)))
                 .collect(Collectors.toSet());
 
-        Set<String> signalIds = signals.stream().map(SignalDetails::getSignalId).collect(Collectors.toSet());
-        log.debug("Number of open signals from open search for accountId:{}, serviceId:{} is {} signals. SignalIds:{}",
-                accountIdentifier, serviceIdentifier, signals.size(), signalIds);
+        Set<String> signalIdsFromOS = filteredOpenSignalDetailsFromOS.stream().map(SignalDetails::getSignalId).collect(Collectors.toSet());
+        log.debug("Number of open signals from open search for anomalyId:{}, accountId:{}, serviceIds:{} is {} signals. SignalIds:{}",
+                anomalyId, accountIdentifier, serviceIdentifiers, filteredOpenSignalDetailsFromOS.size(), signalIdsFromOS);
+
+        Map<String, Map<String, Long>> svcSignalsTimeMapFromRedis = new HashMap<>();
+        appIds.forEach(appId -> {
+            try {
+                Map<String, Map<String, Long>> openSignalsByService = redisUtilities.getOpenSignalsForApp(appId);
+                if (openSignalsByService == null || openSignalsByService.isEmpty()) return;
+
+                // Deep-merge: serviceId -> (signalId -> time) across all appIds
+                openSignalsByService.forEach((serviceId, signalTimeMap) -> {
+                    if (signalTimeMap == null || signalTimeMap.isEmpty()) return;
+                    svcSignalsTimeMapFromRedis
+                            .computeIfAbsent(serviceId, k -> new HashMap<>())
+                            .putAll(signalTimeMap);
+                });
+            } catch (Exception e) {
+                log.error("Error while fetching open signals for appId:{}", appId, e);
+                metrics.updateErrors();
+            }
+        });
+        if (svcSignalsTimeMapFromRedis == null || svcSignalsTimeMapFromRedis.isEmpty()) {
+            return filteredOpenSignalDetailsFromOS;
+        }
+
+        Set<String> signalIdsFromRedis = new HashSet<>();
+        AtomicLong startTime = new AtomicLong(System.currentTimeMillis());
+        serviceIdentifiers.forEach(s -> {
+            Map<String, Long> signalTimeMapFromRedis = svcSignalsTimeMapFromRedis.getOrDefault(s, new HashMap<>());
+
+            log.debug("Signals from redis cache for service id:{} is signalIds:{}", s, signalTimeMapFromRedis.keySet());
+
+            if (!signalTimeMapFromRedis.isEmpty()) {
+                signalIdsFromRedis.addAll(signalTimeMapFromRedis.entrySet()
+                        .stream()
+                        .filter(entry -> !signalIdsFromOS.contains(entry.getKey()))
+                        .map(entry -> {
+                            startTime.set(Math.min(startTime.get(), entry.getValue()));
+                            return entry.getKey();
+                        })
+                        .collect(Collectors.toSet()));
+            }
+        });
+
+        if (signalIdsFromRedis.isEmpty()) {
+            return filteredOpenSignalDetailsFromOS;
+        }
 
-        Map<String, Long> serviceMap = redisUtilities.getOpenSignalsInRedis(accountIdentifier).getOrDefault(serviceIdentifier, new HashMap<>());
-        if(serviceMap.isEmpty()) {
-            return signals;
+        List<SignalDetails> signalDetailsFromRedis = new ArrayList<>();
+        signalIdsFromRedis.forEach(signalId -> {
+            if (signalId.startsWith("E") || signalId.startsWith("P")) {
+                SignalDetails signalDetails = redisUtilities.getSignalDetails(signalId);
+                if (signalDetails == null) {
+                    log.warn("Signal details not found in redis for signalId:{}.", signalId);
+                } else {
+                    signalDetailsFromRedis.add(signalDetails);
+                }
+            }
+        });
+        if (signalDetailsFromRedis.isEmpty()) {
+            return filteredOpenSignalDetailsFromOS;
         }
-        log.debug("Signals from redis cache for service id:{} is signalIds:{}", serviceIdentifier, serviceMap.keySet());
 
-        // TODO::Given signalId is not getting updated in Open search if events are in same service at same second.
-        Set<SignalDetails> redisSignals = serviceMap.entrySet()
-                .stream()
-                .filter(entry -> !signalIds.contains(entry.getKey()))
-                .map(entry -> signalRepo.getSignalById(entry.getKey(), entry.getValue(), accountIdentifier))
+        filteredOpenSignalDetailsFromOS.addAll(signalDetailsFromRedis.stream()
                 .filter(Objects::nonNull)
                 .filter(entry -> !"MLE".equalsIgnoreCase(entry.getMetadata().getOrDefault("Source", "HEAL")))
                 .filter(s -> s.getCurrentStatus().equalsIgnoreCase(SignalStatus.OPEN.name()))
                 .filter(s -> s.getSignalType().equalsIgnoreCase(SignalType.PROBLEM.name())
                         || s.getSignalType().equalsIgnoreCase(SignalType.EARLY_WARNING.name()))
-                .collect(Collectors.toSet());
-        signals.addAll(redisSignals);
-        log.debug("Signals from redis cache and open search:{}, serviceIdentifier:{}", signals.size(), serviceIdentifier);
-        return signals;
+                .collect(Collectors.toSet()));
+
+        log.debug("Signals of size from redis cache and open search is:{}, serviceIdentifier:{}", filteredOpenSignalDetailsFromOS.size(), serviceIdentifiers);
+
+        return filteredOpenSignalDetailsFromOS;
     }
 
-    public AnomalySummary getAnomalySummary(String instanceId, String kpiId, String categoryId, String serviceId,
+    public AnomalySummary getAnomalySummary(String instanceId, String kpiId, String categoryId, Set<String> serviceIdentifiers,
                                             SignalType signalType, int severityId, long anomalyTime, boolean isTxnAnomaly,
                                             String anomalyId, String kpiAttribute, String kpiGroupId, String thresholdType,
                                             String operationType, Map<String, String> metaData, String kpiValue,
-                                            Map<String, Double> thresholds) {
-        String dAnomalyId = severityId == 296 ? anomalyId: null;
-        String sAnomalyId = severityId == 296 ? null:anomalyId;
+                                            Map<String, Double> thresholds, String accountIdentifier) {
+        String dAnomalyId = severityId == 296 ? anomalyId : null;
+        String sAnomalyId = severityId == 296 ? null : anomalyId;
 
         return AnomalySummary.builder()
                 .signalType(signalType)
@@ -230,7 +369,7 @@ public class Commons {
                 .kpiAttribute(kpiAttribute)
                 .kpiGroupId(kpiGroupId)
                 .categoryId(categoryId)
-                .serviceId(serviceId)
+                .serviceId(serviceIdentifiers)
                 .eventTime(anomalyTime)
                 .defaultAnomalyId(dAnomalyId)
                 .severeAnomalyId(sAnomalyId)
@@ -242,40 +381,39 @@ public class Commons {
                 .kpiValue(kpiValue)
                 .thresholds(thresholds)
                 .metadata(metaData)
-                .txnId(isTxnAnomaly ? instanceId: null)
+                .txnId(isTxnAnomaly ? instanceId : null)
+                .accountIdentifier(accountIdentifier)
                 .build();
     }
 
-    public SignalProtos.SignalDetails getSignalProto(String accountId, SignalDetails signalDetails, boolean isRemainder,
+    public SignalProtos.SignalDetails getSignalProto(SignalDetails signalDetails, boolean isRemainder,
                                                      boolean isSeverityChanged, boolean isServiceAdded, boolean isStatusChanged,
                                                      AnomalySummary anomalySummary) {
 
         SignalProtos.SignalDetails.Builder signalProtoBuilder = SignalProtos.SignalDetails.newBuilder();
 
         try {
-
-            Set<AnomalySummary> anomalySummaries = redisUtilities.getSignalAnomalies(accountId, signalDetails.getSignalId());
+            Set<AnomalySummary> anomalySummaries = redisUtilities.getSignalAnomalies(signalDetails.getSignalId());
 
             boolean isMLE = signalDetails.getMetadata().getOrDefault("Source", "").equalsIgnoreCase("MLE");
-            log.debug("signal is MLE {} for signalId {}: ", isMLE, signalDetails.getSignalId());
+            log.debug("SignalId: {} is ML generated: {}: ", signalDetails.getSignalId(), isMLE);
 
-
-            Set<AnomalySummary> topNAnomalies = anomalySummaries
-                    .stream()
+            Set<AnomalySummary> topNAnomalies = anomalySummaries.stream()
                     .sorted(Comparator.comparing(AnomalySummary::getEventTime, Comparator.reverseOrder()))
-                    .limit( isMLE ? 1 : lastNEvents-(anomalySummary == null ? 0:1))
+                    .limit(isMLE ? 1 : lastNEvents - (anomalySummary == null ? 0 : 1))
                     .collect(Collectors.toSet());
 
-            String serviceId = (isServiceAdded && anomalySummary != null) ? anomalySummary.getServiceId(): null;
+            String serviceId = (isServiceAdded && anomalySummary != null) ? String.join(",", anomalySummary.getServiceId()) : null;
             if (anomalySummary != null) {
                 anomalySummaries.add(anomalySummary);
                 signalDetails.getMetadata().put("AnomalyId", anomalySummary.getAnomalyId());
                 topNAnomalies.add(anomalySummary);
             }
 
-            if(isServiceAdded && anomalySummary != null) {
+            if (isServiceAdded && anomalySummary != null) {
                 signalDetails.getMetadata().put("ServiceAdded", serviceId);
             }
+
             List<SignalProtos.AnomalyDetail> anomalyDetailList = topNAnomalies.parallelStream()
                     .map(as -> SignalProtos.AnomalyDetail.newBuilder()
                             .setAnomalyId(as.getAnomalyId())
@@ -287,9 +425,12 @@ public class Commons {
                             .setCategoryId(as.getCategoryId())
                             .setKpiAttribute(as.getKpiAttribute())
                             .setAnomalyTime(as.getEventTime())
-                            .setServiceId(as.getServiceId())
+                            .addAllServiceIds(as.getServiceId())
                             .setKpiValue(as.getKpiValue())
-                            .putAllMetadata(as.getMetadata())
+                            .putAllMetadata(new HashMap<>() {{
+                                putAll(as.getMetadata());
+                                put("accountIdentifier", as.getAccountIdentifier());
+                            }})
                             .putAllThresholds(as.getThresholds())
                             .setOperationType(as.getOperationType())
                             .setThresholdType(as.getThresholdType())
@@ -298,36 +439,39 @@ public class Commons {
 
 
             signalProtoBuilder
-                    .setAccountId(accountId)
+                    .setAccountId(String.join(",", signalDetails.getAccountIdentifiers()))
                     .setSignalType(signalDetails.getSignalType())
                     .setSignalId(signalDetails.getSignalId())
                     .setSignalStatus(signalDetails.getCurrentStatus())
                     .setIsRemainder(isRemainder)
                     .setIsSeverityChanged(isSeverityChanged)
                     .setIsServiceAdded(isServiceAdded)
-                    .putAllMetadata(signalDetails.getMetadata())
+                    .putAllMetadata(new HashMap<>() {{
+                        putAll(signalDetails.getMetadata());
+                        put("accountIdentifiers", String.join(",", signalDetails.getAccountIdentifiers()));
+                    }})
                     .setStartTime(signalDetails.getStartedTime())
                     .setUpdateTime(signalDetails.getUpdatedTime())
                     .addAllServiceIds(signalDetails.getServiceIds())
                     .addAllRootCauseServiceIds(signalDetails.getRootCauseServiceIds())
-                    .setEntryServiceId(signalDetails.getEntryServiceId() == null ? "" : signalDetails.getEntryServiceId())
+                    .addAllEntryServiceIds(signalDetails.getEntryServiceId() == null ? new ArrayList<>() : signalDetails.getEntryServiceId())
                     .setSignalSeverityId(anomalySummary != null ? anomalySummary.getSeverityId() : signalDetails.getSeverityId()) // checking the latest anomaly as signal severity
                     .build();
 
             signalProtoBuilder.addAllAnomalyDetails(anomalyDetailList);
+
             if (signalDetails.getRelatedSignals() != null && !signalDetails.getRelatedSignals().isEmpty()) {
                 signalProtoBuilder.addAllRelatedSignals(signalDetails.getRelatedSignals());
             }
 
-            SignalSummary summary = redisUtilities.getSignalDetails(accountId, signalDetails.getSignalId());
+            SignalSummary summary = redisUtilities.getSignalSummary(signalDetails.getSignalId());
             if (summary == null) {
-                log.error("Signal summary not found for accountId:{}, signalId:{}, notification will have issues.", accountId, signalDetails.getSignalId());
+                log.error("Signal summary not found for signalId:{}, notification will have issues.", signalDetails.getSignalId());
             } else {
-                signalProtoBuilder.setAnomalies(summary.getAnomalies());
+                signalProtoBuilder.setAnomalies(summary.getAnomalies() != null ? summary.getAnomalies().size() : 0);
             }
 
-            List<SignalProtos.ServiceSummary> signalServiceSummariesProto = redisUtilities.getSignalServiceSummary(accountId, signalDetails.getSignalId())
-                    .parallelStream()
+            List<SignalProtos.ServiceSummary> signalServiceSummariesProto = redisUtilities.getSignalServiceSummary(signalDetails.getSignalId()).parallelStream()
                     .map(serviceSummary -> SignalProtos.ServiceSummary.newBuilder()
                             .setServiceId(serviceSummary.getServiceIdentifier())
                             .addAllSevereAnomalies(serviceSummary.getSevereAnomalies())
@@ -340,7 +484,7 @@ public class Commons {
                     .collect(Collectors.toList());
             signalProtoBuilder.addAllServiceSummary(signalServiceSummariesProto);
 
-            List<SignalProtos.InstanceSummary> signalInstanceSummariesProto = redisUtilities.getSignalInstanceSummary(accountId, signalDetails.getSignalId())
+            List<SignalProtos.InstanceSummary> signalInstanceSummariesProto = redisUtilities.getSignalInstanceSummary(signalDetails.getSignalId())
                     .parallelStream()
                     .map(instanceSummary -> SignalProtos.InstanceSummary.newBuilder()
                             .setInstanceId(instanceSummary.getInstanceIdentifier())
@@ -353,7 +497,7 @@ public class Commons {
                             .build()).collect(Collectors.toList());
             signalProtoBuilder.addAllInstanceSummary(signalInstanceSummariesProto);
 
-            List<SignalProtos.RequestSummary> signalTxnSummariesProto = redisUtilities.getSignalTransactionSummary(accountId, signalDetails.getSignalId())
+            List<SignalProtos.RequestSummary> signalTxnSummariesProto = redisUtilities.getSignalTransactionSummary(signalDetails.getSignalId())
                     .parallelStream()
                     .map(instanceSummary -> SignalProtos.RequestSummary.newBuilder()
                             .setTransactionId(instanceSummary.getTransactionIdentifier())
@@ -368,7 +512,7 @@ public class Commons {
                     .collect(Collectors.toList());
             signalProtoBuilder.addAllRequestSummary(signalTxnSummariesProto);
         } catch (Exception e) {
-            log.error("Error while populating signal proto for accountId:{}, signalId:{}", accountId, signalDetails.getSignalId(), e);
+            log.error("Error while populating signal proto for signalId:{}", signalDetails.getSignalId(), e);
             metrics.updateErrors();
         }
         return signalProtoBuilder.build();
diff --git a/src/main/java/com/heal/signal/detector/util/Constants.java b/src/main/java/com/heal/signal/detector/util/Constants.java
index 3480d64..340e631 100644
--- a/src/main/java/com/heal/signal/detector/util/Constants.java
+++ b/src/main/java/com/heal/signal/detector/util/Constants.java
@@ -32,4 +32,8 @@ public class Constants {
     public static final String SERVICE_NEIGHBOURS_BY_IDENTIFIER = "serviceNeighboursCache";
     public static final String HEAL_TYPES = "healTypesCache";
     public static final String TENANTS = "tenantsCache";
+    public static final String ACCOUNT_SERVICES = "accountServices";
+    public static final String APPLICATIONS = "applicationsCache";
+
+    public static final String TRANSACTION_COMPONENT_IDENTIFIER = "Transaction";
 }
diff --git a/src/main/java/com/heal/signal/detector/util/CustomThreadExecutionService.java b/src/main/java/com/heal/signal/detector/util/CustomThreadExecutionService.java
index e8d99d1..2f61df7 100644
--- a/src/main/java/com/heal/signal/detector/util/CustomThreadExecutionService.java
+++ b/src/main/java/com/heal/signal/detector/util/CustomThreadExecutionService.java
@@ -42,11 +42,11 @@ public class CustomThreadExecutionService {
             ThreadPoolExecutor threadPoolExecutor = appExecutorMap.get(mapKey);
             threadPoolExecutor.execute(task);
         } else {
-            String threadPoolNamePrefix = "SD-Worker-" + mapKey;
-            ThreadFactory namedThreadFactory = new ThreadFactoryBuilder().setNameFormat(threadPoolNamePrefix).build();
+            String threadPoolNameFormat = "SD-Worker-" + mapKey + "-%d";
+            ThreadFactory namedThreadFactory = new ThreadFactoryBuilder().setNameFormat(threadPoolNameFormat).build();
             LinkedBlockingQueue<Runnable> workQueue = new LinkedBlockingQueue<>(maxQueueSize);
             ThreadPoolExecutor executor = new ThreadPoolExecutor(corePoolSize, maxPoolSize, 0L, TimeUnit.MILLISECONDS, workQueue, namedThreadFactory);
-            executor.setRejectedExecutionHandler((runnable, threadPoolExecutor) -> healthMetrics.updateThreadRejectedTaskCount(threadPoolNamePrefix));
+            executor.setRejectedExecutionHandler((runnable, threadPoolExecutor) -> healthMetrics.updateThreadRejectedTaskCount(threadPoolNameFormat));
             appExecutorMap.put(mapKey, executor);
             log.trace("Creating new thread for [{}]", mapKey);
             executor.execute(task);
diff --git a/src/main/java/com/heal/signal/detector/util/HealthMetrics.java b/src/main/java/com/heal/signal/detector/util/HealthMetrics.java
index 5d948dc..7dbc96f 100644
--- a/src/main/java/com/heal/signal/detector/util/HealthMetrics.java
+++ b/src/main/java/com/heal/signal/detector/util/HealthMetrics.java
@@ -16,6 +16,9 @@ public class HealthMetrics {
     private int signalInputQueueReadData = 0;
     private int signalOutputQueueWriteData = 0;
     private int errors = 0;
+    private int openSignalDetailsKeyErrorsGET = 0;
+    private int openSignalDetailsKeyErrorsPUT = 0;
+    private int openSignalDetailsKeyErrorsDELETE = 0;
     private int maintenanceEvents = 0;
     @Setter
     private String readSignalQueueName;
@@ -68,6 +71,33 @@ public class HealthMetrics {
         errors++;
     }
 
+    @ManagedAttribute
+    public int getSignalDetailsKeyErrorsGET() {
+        return openSignalDetailsKeyErrorsGET;
+    }
+
+    public void updateSignalDetailsKeyErrorsGET() {
+        openSignalDetailsKeyErrorsGET++;
+    }
+
+    @ManagedAttribute
+    public int getSignalDetailsKeyErrorsPUT() {
+        return openSignalDetailsKeyErrorsPUT;
+    }
+
+    public void updateSignalDetailsKeyErrorsPUT() {
+        openSignalDetailsKeyErrorsPUT++;
+    }
+
+    @ManagedAttribute
+    public int getSignalDetailsKeyErrorsDELETE() {
+        return openSignalDetailsKeyErrorsDELETE;
+    }
+
+    public void updateSignalDetailsKeyErrorsDELETE() {
+        openSignalDetailsKeyErrorsDELETE++;
+    }
+
     @ManagedAttribute
     public int getMaintenanceEvents() {
         return maintenanceEvents;
@@ -209,6 +239,9 @@ public class HealthMetrics {
         sb.append("Read Count Signal Input Queue : ").append(getSignalInputQueueReadData()).append(", ");
         sb.append("Write Count Signal Output Queue : ").append(getSignalOutputQueueWriteData()).append(", ");
         sb.append("Errors Count : ").append(getErrors()).append(", ");
+        sb.append("GET Signal Details Key Errors Count : ").append(getSignalDetailsKeyErrorsGET()).append(", ");
+        sb.append("PUT Signal Details Key Errors Count : ").append(getSignalDetailsKeyErrorsPUT()).append(", ");
+        sb.append("DELETE Signal Details Key Errors Count : ").append(getSignalDetailsKeyErrorsDELETE()).append(", ");
         sb.append("Read Signal Queue name : ").append(getReadSignalQueueName()).append(", ");
         sb.append("Write Signal Queue name : ").append(getWriteSignalQueueName()).append(", ");
         sb.append("Events under Maintenance Count : ").append(getMaintenanceEvents()).append(", ");
diff --git a/src/main/java/com/heal/signal/detector/util/RedisUtilities.java b/src/main/java/com/heal/signal/detector/util/RedisUtilities.java
index a51bd6c..a9ec07c 100644
--- a/src/main/java/com/heal/signal/detector/util/RedisUtilities.java
+++ b/src/main/java/com/heal/signal/detector/util/RedisUtilities.java
@@ -4,6 +4,7 @@ import com.fasterxml.jackson.core.JsonProcessingException;
 import com.fasterxml.jackson.core.type.TypeReference;
 import com.fasterxml.jackson.databind.ObjectMapper;
 import com.heal.configuration.pojos.*;
+import com.heal.configuration.pojos.opensearch.SignalDetails;
 import com.heal.signal.detector.pojos.SignalStatus;
 import lombok.extern.slf4j.Slf4j;
 import org.springframework.beans.factory.annotation.Autowired;
@@ -11,6 +12,7 @@ import org.springframework.data.redis.core.RedisTemplate;
 import org.springframework.stereotype.Repository;
 
 import java.util.*;
+import java.util.concurrent.atomic.AtomicReference;
 import java.util.stream.Collectors;
 
 @Slf4j
@@ -38,7 +40,8 @@ public class RedisUtilities {
                 return null;
             }
 
-            List<Account> accounts = objectMapper.readValue(obj.toString(), new TypeReference<List<Account>>() {});
+            List<Account> accounts = objectMapper.readValue(obj.toString(), new TypeReference<>() {
+            });
             return accounts.stream().filter(a -> a.getStatus() == 1).collect(Collectors.toList());
         } catch (JsonProcessingException e) {
             log.error("Error while converting JSON string to accounts, Redis data:{}.", obj, e);
@@ -63,8 +66,9 @@ public class RedisUtilities {
                 return null;
             }
 
-            Account account = objectMapper.readValue(obj.toString(), new TypeReference<Account>() {});
-            if(account.getStatus() == 0) {
+            Account account = objectMapper.readValue(obj.toString(), new TypeReference<>() {
+            });
+            if (account.getStatus() == 0) {
                 log.error("Account is disabled for name:{}, identifier:{}.", account.getName(), accIdentifier);
                 return null;
             }
@@ -94,8 +98,9 @@ public class RedisUtilities {
                 return null;
             }
 
-            Application application = objectMapper.readValue(obj.toString(), new TypeReference<Application>() {});
-            if(application.getStatus() == 0) {
+            Application application = objectMapper.readValue(obj.toString(), new TypeReference<>() {
+            });
+            if (application.getStatus() == 0) {
                 log.error("Application is disabled for name:{}, identifier:{}.", application.getName(), appIdentifier);
                 return null;
             }
@@ -116,8 +121,8 @@ public class RedisUtilities {
     public BasicKpiEntity getKPIDetails(String accIdentifier, String componentIdentifier, String kpiIdentifier) {
         Object obj = null;
         try {
-            obj = redisTemplate.opsForHash().entries("/accounts/" + accIdentifier + "/components/" + componentIdentifier+"/kpis")
-                    .get("ACCOUNTS_" + accIdentifier + "_COMPONENTS_" + componentIdentifier+"_KPIS");
+            obj = redisTemplate.opsForHash().entries("/accounts/" + accIdentifier + "/components/" + componentIdentifier + "/kpis")
+                    .get("ACCOUNTS_" + accIdentifier + "_COMPONENTS_" + componentIdentifier + "_KPIS");
             if (obj == null) {
                 log.error("Component details unavailable in redis cache for account identifier {}, component Identifier identifier {}.",
                         accIdentifier, componentIdentifier);
@@ -126,12 +131,13 @@ public class RedisUtilities {
                 return null;
             }
 
-            List<BasicKpiEntity> componentKpisList = objectMapper.readValue(obj.toString(), new TypeReference<List<BasicKpiEntity>>() {});
+            List<BasicKpiEntity> componentKpisList = objectMapper.readValue(obj.toString(), new TypeReference<>() {
+            });
             BasicKpiEntity kpiEntity = componentKpisList.parallelStream()
-                    .filter(k -> k.getIdentifier().equalsIgnoreCase(kpiIdentifier) || kpiIdentifier.equalsIgnoreCase(k.getId()+""))
+                    .filter(k -> k.getIdentifier().equalsIgnoreCase(kpiIdentifier) || kpiIdentifier.equalsIgnoreCase(k.getId() + ""))
                     .findAny()
                     .orElse(null);
-            if(kpiEntity == null || kpiEntity.getStatus() == 0) {
+            if (kpiEntity == null || kpiEntity.getStatus() == 0) {
                 log.error("KPI is disabled or does not exists for identifier:{}, component:{}, kpiEntity:{}.", kpiIdentifier, componentIdentifier, kpiEntity);
                 return null;
             }
@@ -152,8 +158,8 @@ public class RedisUtilities {
     public List<BasicKpiEntity> getComponentKPIs(String accIdentifier, String componentIdentifier) {
         Object obj = null;
         try {
-            obj = redisTemplate.opsForHash().entries("/accounts/" + accIdentifier + "/components/" + componentIdentifier+"/kpis")
-                    .get("ACCOUNTS_" + accIdentifier + "_COMPONENTS_" + componentIdentifier+"_KPIS");
+            obj = redisTemplate.opsForHash().entries("/accounts/" + accIdentifier + "/components/" + componentIdentifier + "/kpis")
+                    .get("ACCOUNTS_" + accIdentifier + "_COMPONENTS_" + componentIdentifier + "_KPIS");
             if (obj == null) {
                 log.error("Component details unavailable in redis cache for account identifier {}, component Identifier identifier {}.",
                         accIdentifier, componentIdentifier);
@@ -162,7 +168,8 @@ public class RedisUtilities {
                 return null;
             }
 
-            List<BasicKpiEntity> kpiEntities = objectMapper.readValue(obj.toString(), new TypeReference<List<BasicKpiEntity>>() {});
+            List<BasicKpiEntity> kpiEntities = objectMapper.readValue(obj.toString(), new TypeReference<>() {
+            });
             return kpiEntities.parallelStream().filter(k -> k.getStatus() == 1).collect(Collectors.toList());
         } catch (JsonProcessingException e) {
             log.error("Error while converting JSON string to component details for account identifier {}, " +
@@ -189,8 +196,9 @@ public class RedisUtilities {
                 return null;
             }
 
-            Service service = objectMapper.readValue(obj.toString(), new TypeReference<Service>() {});
-            if(service.getStatus() == 0) {
+            Service service = objectMapper.readValue(obj.toString(), new TypeReference<>() {
+            });
+            if (service.getStatus() == 0) {
                 log.error("Service is disabled for name:{}, identifier:{}, account identifier:{}", service.getName(), serviceIdentifier, accIdentifier);
                 return null;
             }
@@ -207,13 +215,36 @@ public class RedisUtilities {
         }
     }
 
+    public List<BasicEntity> getAccountServices(String accIdentifier) {
+        Object obj = null;
+        try {
+            obj = redisTemplate.opsForHash().entries("/accounts/" + accIdentifier + "/services").get("ACCOUNTS_" + accIdentifier + "_SERVICES");
+            if (obj == null) {
+                log.error("Service list unavailable in redis cache for account identifier {}.", accIdentifier);
+                metrics.updateRedisKeysNotFound();
+                metrics.updateSnapshots("/accounts/" + accIdentifier + "/services", 1);
+                return new ArrayList<>();
+            }
+
+            return objectMapper.readValue(obj.toString(), new TypeReference<>() {
+            });
+        } catch (JsonProcessingException e) {
+            log.error("Error while converting JSON string to service list for account identifier {}, Redis data:{}.",
+                    accIdentifier, obj, e);
+            metrics.updateErrors();
+            return null;
+        } catch (Exception e) {
+            metrics.updateErrors();
+            log.error("Error occurred while getting service details, account identifier:{}.", accIdentifier, e);
+            return null;
+        }
+    }
 
     public List<MaintenanceDetails> getServiceMaintenanceDetails(String accountIdentifier, String serviceIdentifier) {
 
         Object obj = null;
         try {
-            obj = redisTemplate.opsForHash().get("/accounts/" + accountIdentifier +
-                            "/services/" + serviceIdentifier + "/maintenanceDetails",
+            obj = redisTemplate.opsForHash().get("/accounts/" + accountIdentifier + "/services/" + serviceIdentifier + "/maintenanceDetails",
                     "ACCOUNTS_" + accountIdentifier + "_SERVICES_" + serviceIdentifier + "_MAINTENANCE_DETAILS");
 
             if (obj == null) {
@@ -225,7 +256,7 @@ public class RedisUtilities {
                 return Collections.emptyList();
             }
 
-            return objectMapper.readValue(obj.toString(), new TypeReference<List<MaintenanceDetails>>() {
+            return objectMapper.readValue(obj.toString(), new TypeReference<>() {
             });
         } catch (JsonProcessingException e) {
             log.error("Error while converting JSON string to maintenance details for account identifier:{}, " +
@@ -249,18 +280,18 @@ public class RedisUtilities {
 
             if (obj == null) {
                 log.debug("Maintenance details information unavailable for account identifier:{}, instance identifier:{}",
-                         accountIdentifier, instanceIdentifier);
+                        accountIdentifier, instanceIdentifier);
                 metrics.updateRedisKeysNotFound();
                 metrics.updateSnapshots("/accounts/" + accountIdentifier +
                         "/instances/" + instanceIdentifier + "/maintenanceDetails", 1);
                 return Collections.emptyList();
             }
 
-            return objectMapper.readValue(obj.toString(), new TypeReference<List<MaintenanceDetails>>() {
+            return objectMapper.readValue(obj.toString(), new TypeReference<>() {
             });
         } catch (JsonProcessingException e) {
             log.error("Error while converting JSON string to maintenance details for account identifier:{}, " +
-                    "instance identifier:{}, Redis data:{}", accountIdentifier,  instanceIdentifier, obj, e);
+                    "instance identifier:{}, Redis data:{}", accountIdentifier, instanceIdentifier, obj, e);
             metrics.updateErrors();
             return Collections.emptyList();
         } catch (Exception e) {
@@ -287,9 +318,9 @@ public class RedisUtilities {
                         + "/instances/" + instanceIdentifier + "/kpis/" + kpiId, 1);
                 return null;
             }
-            CompInstKpiEntity compInstKpiEntity = objectMapper.readValue(obj.toString(), new TypeReference<CompInstKpiEntity>() {
+            CompInstKpiEntity compInstKpiEntity = objectMapper.readValue(obj.toString(), new TypeReference<>() {
             });
-            if(compInstKpiEntity.getStatus() == 0) {
+            if (compInstKpiEntity.getStatus() == 0) {
                 log.error("KPI is disabled for instance:{}, kpi:{}, account:{}", instanceIdentifier, kpiId, accountIdentifier);
                 return null;
             }
@@ -315,11 +346,11 @@ public class RedisUtilities {
             obj = redisTemplate.opsForHash().get("/heal/types", "HEAL_TYPES");
 
             if (obj == null) {
-                log.debug("Severity Id are unavailable for name:{}.",name);
+                log.debug("Severity Id are unavailable for name:{}.", name);
                 metrics.updateRedisKeysNotFound();
                 metrics.updateSnapshots("/heal/types", 1);
             } else {
-                viewTypesList = objectMapper.readValue(obj.toString(), new TypeReference<List<ViewTypes>>() {
+                viewTypesList = objectMapper.readValue(obj.toString(), new TypeReference<>() {
                 });
             }
         } catch (JsonProcessingException e) {
@@ -333,7 +364,7 @@ public class RedisUtilities {
         ViewTypes signalSeverity = viewTypesList.parallelStream().filter(v -> v.getTypeName().equalsIgnoreCase("SignalSeverity"))
                 .filter(v -> v.getSubTypeName().equalsIgnoreCase(name)).findAny().orElse(null);
 
-        if(signalSeverity != null) {
+        if (signalSeverity != null) {
             return signalSeverity.getSubTypeId();
         }
 
@@ -347,18 +378,17 @@ public class RedisUtilities {
     public Set<BasicEntity> getNeighbours(String accountIdentifier, String serviceIdentifier) {
         Object obj = null;
         try {
-            obj = redisTemplate.opsForHash().get("/accounts/" + accountIdentifier
-                            + "/services/" + serviceIdentifier+"/neighbours",
-                    "ACCOUNTS_" + accountIdentifier + "_SERVICES_" + serviceIdentifier+"_NEIGHBOURS");
+            obj = redisTemplate.opsForHash().get("/accounts/" + accountIdentifier + "/services/" + serviceIdentifier + "/neighbours",
+                    "ACCOUNTS_" + accountIdentifier + "_SERVICES_" + serviceIdentifier + "_NEIGHBOURS");
 
             if (obj == null) {
-                log.debug("Neighbours are unavailable for service [{}] and account [{}].",serviceIdentifier, accountIdentifier);
+                log.debug("Neighbours are unavailable for service [{}] and account [{}].", serviceIdentifier, accountIdentifier);
                 metrics.updateRedisKeysNotFound();
                 metrics.updateSnapshots("/accounts/" + accountIdentifier
-                        + "/services/" + serviceIdentifier+"/neighbour", 1);
+                        + "/services/" + serviceIdentifier + "/neighbour", 1);
                 return Collections.emptySet();
             }
-            return objectMapper.readValue(obj.toString(), new TypeReference<Set<BasicEntity>>() {
+            return objectMapper.readValue(obj.toString(), new TypeReference<>() {
             });
         } catch (JsonProcessingException e) {
             log.error("Error while converting JSON string for Account identifier:{}, service identifier:{}. " +
@@ -373,157 +403,248 @@ public class RedisUtilities {
         }
     }
 
-    public void updateServiceSignal(String accountIdentifier, String signalId, long signalStartTime, String serviceId, String signalStatus) {
-        String key = "/accounts/" + accountIdentifier+"/signals/open";
-        String hashKey = "ACCOUNTS_"+accountIdentifier+"_SIGNALS_OPEN";
+    public Set<ControllerAlias> getServiceAliases() {
+        Object obj = null;
         try {
+            obj = redisTemplate.opsForHash().get("/accounts/services", "ACCOUNTS_SERVICES");
+            if (obj == null) {
+                log.debug("Service aliases list unavailable in redis.");
+                metrics.updateRedisKeysNotFound();
+                metrics.updateSnapshots("/accounts/services", 1);
+                return Collections.emptySet();
+            }
 
-            Map<String, Map<String, Long>> serviceSignals = getOpenSignalsInRedis(accountIdentifier);
-            Map<String, Long> signalMap = serviceSignals.getOrDefault(serviceId, new HashMap<>());
-            log.trace("Before update serviceId:{}, signalMap:{}", serviceId, signalMap);
+            return objectMapper.readValue(obj.toString(), new TypeReference<>() {
+            });
+        } catch (JsonProcessingException e) {
+            log.error("Error while converting JSON string for service aliases list. Redis data:{}.", obj, e);
+            metrics.updateErrors();
+            return Collections.emptySet();
+        } catch (Exception e) {
+            metrics.updateErrors();
+            log.error("Error occurred while getting service aliases list.", e);
+            return Collections.emptySet();
+        }
+    }
 
-            if(signalMap.containsKey(signalId) && (signalStatus.equalsIgnoreCase(SignalStatus.UPGRADED.name()) || signalStatus.equalsIgnoreCase(SignalStatus.CLOSED.name()))) {
-                signalMap.remove(signalId);
-                log.debug("Service signal will be removed from redis cache. AccountId:{}, serviceId:{}, signalId:{}, signalStatus:{}", accountIdentifier, serviceId, signalId, signalStatus);
-            } else if(!signalMap.containsKey(signalId)){
-                signalMap.put(signalId, signalStartTime);
-                log.debug("Service signal will be added to redis cache. AccountId:{}, serviceId:{}, signalId:{}, signalStatus:{}", accountIdentifier, serviceId, signalId, signalStatus);
-            }
+    public void updateServiceSignal(String signalId, long signalStartTime, Set<String> serviceIds, String signalStatus) {
+        String key = "/signals/open";
+        String hashKey = "SIGNALS_OPEN";
+        try {
+            AtomicReference<Map<String, Long>> signalMap = new AtomicReference<>(new HashMap<>());
+            Map<String, Map<String, Long>> serviceSignals = getOpenSignalsInRedis();
+
+            serviceIds.forEach(serviceId -> {
+                signalMap.set(serviceSignals.getOrDefault(serviceId, new HashMap<>()));
+                log.trace("Before update serviceId:{}, signalMap:{}", serviceId, signalMap);
+
+                if (signalMap.get().containsKey(signalId) && (signalStatus.equalsIgnoreCase(SignalStatus.UPGRADED.name()) || signalStatus.equalsIgnoreCase(SignalStatus.DOWNGRADED.name()) || signalStatus.equalsIgnoreCase(SignalStatus.CLOSED.name()))) {
+                    signalMap.get().remove(signalId);
+                    log.debug("Service signal will be removed from redis cache. serviceId:{}, signalId:{}, signalStatus:{}", serviceId, signalId, signalStatus);
+                } else if (!signalMap.get().containsKey(signalId)) {
+                    signalMap.get().put(signalId, signalStartTime);
+                    log.debug("Service signal will be added to redis cache. serviceId:{}, signalId:{}, signalStatus:{}", serviceId, signalId, signalStatus);
+                }
+                log.trace("After update serviceId:{}, signalMap:{}", serviceIds, signalMap);
+                serviceSignals.put(serviceId, signalMap.get());
+            });
 
-            log.trace("After update serviceId:{}, signalMap:{}", serviceId, signalMap);
-            serviceSignals.put(serviceId, signalMap);
             String dataFromDBInJson = objectMapper.writeValueAsString(serviceSignals);
             redisTemplate.opsForHash().put(key, hashKey, dataFromDBInJson);
         } catch (Exception e) {
             metrics.updateErrors();
-            log.error("Error occurred while updating service, signalId:{}, accountId:{}.",
-                    signalId, accountIdentifier, e);
+            log.error("Error occurred while updating service signals for service:{}, signalId:{}.", serviceIds, signalId, e);
         }
     }
 
 
-    public Map<String, Map<String, Long>> getOpenSignalsInRedis(String accountIdentifier) {
-        String key = "/accounts/" + accountIdentifier+"/signals/open";
-        String hashKey = "ACCOUNTS_"+accountIdentifier+"_SIGNALS_OPEN";
+    public Map<String, Map<String, Long>> getOpenSignalsInRedis() {
+        String key = "/signals/open";
+        String hashKey = "SIGNALS_OPEN";
         try {
 
             Object obj = redisTemplate.opsForHash().get(key, hashKey);
             if (obj == null) {
-                log.debug("Open signals for account is unavailable for accountId:{}.", accountIdentifier);
+                log.debug("Open signals is unavailable in redis.");
                 metrics.updateRedisKeysNotFound();
                 metrics.updateSnapshots(key, 1);
                 return new HashMap<>();
             }
 
-            return objectMapper.readValue(obj.toString(), new TypeReference<Map<String, Map<String, Long>>>() {});
+            return objectMapper.readValue(obj.toString(), new TypeReference<>() {
+            });
         } catch (Exception e) {
             metrics.updateErrors();
-            log.error("Error occurred while getting open signals for accountId:{}.", accountIdentifier, e);
+            log.error("Error occurred while getting open signals from redis.", e);
             return new HashMap<>();
         }
     }
 
-    public void updateAnomalyForSignal(String accountIdentifier, String signalId, Set<AnomalySummary> anomalySummaries) {
-        String key = "/accounts/" + accountIdentifier+"/signals/"+signalId+"/anomalies";
-        String hashKey = "ACCOUNTS_"+accountIdentifier+"_SIGNALS_"+signalId+"_ANOMALIES";
+    /**
+     * Updates the open signals for a specific application in Redis.
+     *
+     * @param signalId        The ID of the signal to update.
+     * @param signalStartTime The start time of the signal.
+     * @param serviceIds      The set of service IDs associated with the signal.
+     * @param signalStatus    The status of the signal (e.g., UPGRADED, CLOSED).
+     * @param appIdentifier   The identifier of the application.
+     */
+    public void updateServiceOpenSignalsForApp(String signalId, long signalStartTime, Set<String> serviceIds, String signalStatus, String appIdentifier) {
+        String key = "/applications/" + appIdentifier + "/signals/open";
+        String hashKey = "APPLICATIONS_" + appIdentifier + "_SIGNALS_OPEN";
         try {
+            AtomicReference<Map<String, Long>> signalMap = new AtomicReference<>(new HashMap<>());
+            Map<String, Map<String, Long>> serviceSignals = getOpenSignalsForApp(appIdentifier);
+
+            serviceIds.forEach(serviceId -> {
+                signalMap.set(serviceSignals.getOrDefault(serviceId, new HashMap<>()));
+                log.trace("Before update serviceId:{}, signalMap:{}", serviceId, signalMap);
+
+                if (signalMap.get().containsKey(signalId) && (signalStatus.equalsIgnoreCase(SignalStatus.UPGRADED.name()) || signalStatus.equalsIgnoreCase(SignalStatus.CLOSED.name()))) {
+                    signalMap.get().remove(signalId);
+                    log.debug("Service signal will be removed from redis cache. serviceId:{}, signalId:{}, signalStatus:{}", serviceId, signalId, signalStatus);
+                } else if (!signalMap.get().containsKey(signalId)) {
+                    signalMap.get().put(signalId, signalStartTime);
+                    log.debug("Service signal will be added to redis cache. serviceId:{}, signalId:{}, signalStatus:{}", serviceId, signalId, signalStatus);
+                }
+                log.trace("After update serviceId:{}, signalMap:{}", serviceIds, signalMap);
+                serviceSignals.put(serviceId, signalMap.get());
+            });
+
+            String dataFromDBInJson = objectMapper.writeValueAsString(serviceSignals);
+            redisTemplate.opsForHash().put(key, hashKey, dataFromDBInJson);
+        } catch (Exception e) {
+            metrics.updateErrors();
+            log.error("Error occurred while updating service signals for service:{}, signalId:{}.", serviceIds, signalId, e);
+        }
+    }
+
+    /**
+     * Retrieves the open signals for a specific application from Redis.
+     *
+     * @param appIdentifier The identifier of the application.
+     * @return A map where keys are service IDs and values are maps of signal IDs and their start times.
+     */
+    public Map<String, Map<String, Long>> getOpenSignalsForApp(String appIdentifier) {
+        String key = "/applications/" + appIdentifier + "/signals/open";
+        String hashKey = "APPLICATIONS_" + appIdentifier + "_SIGNALS_OPEN";
+        try {
+
+            Object obj = redisTemplate.opsForHash().get(key, hashKey);
+            if (obj == null) {
+                log.debug("Open signals is unavailable in redis.");
+                metrics.updateRedisKeysNotFound();
+                metrics.updateSnapshots(key, 1);
+                return new HashMap<>();
+            }
+
+            return objectMapper.readValue(obj.toString(), new TypeReference<>() {
+            });
+        } catch (Exception e) {
+            metrics.updateErrors();
+            log.error("Error occurred while getting open signals from redis.", e);
+            return new HashMap<>();
+        }
+    }
 
+    public void updateAnomalyForSignal(String signalId, Set<AnomalySummary> anomalySummaries) {
+        String key = "/signals/" + signalId + "/anomalies";
+        String hashKey = "SIGNALS_" + signalId + "_ANOMALIES";
+        try {
             String dataFromDBInJson = objectMapper.writeValueAsString(anomalySummaries);
             redisTemplate.opsForHash().put(key, hashKey, dataFromDBInJson);
         } catch (Exception e) {
             metrics.updateErrors();
-            log.error("Error occurred while updating latest anomaly event for signalId:{}, accountId:{}.",
-                    signalId, accountIdentifier, e);
+            log.error("Error occurred while updating latest anomaly event for signalId:{}.", signalId, e);
         }
     }
 
-    public Set<AnomalySummary> getSignalAnomalies(String accountIdentifier, String signalId) {
-        String key = "/accounts/" + accountIdentifier+"/signals/"+signalId+"/anomalies";
-        String hashKey = "ACCOUNTS_"+accountIdentifier+"_SIGNALS_"+signalId+"_ANOMALIES";
+    public Set<AnomalySummary> getSignalAnomalies(String signalId) {
+        String key = "/signals/" + signalId + "/anomalies";
+        String hashKey = "SIGNALS_" + signalId + "_ANOMALIES";
         try {
             Object obj = redisTemplate.opsForHash().get(key, hashKey);
             if (obj == null) {
-                log.debug("Signal anomalies are unavailable for accountId:{}, signalId:{}.",accountIdentifier, signalId);
+                log.debug("Signal anomalies are unavailable for signalId:{}.", signalId);
                 metrics.updateRedisKeysNotFound();
                 metrics.updateSnapshots(key, 1);
                 return new HashSet<>();
             }
 
-            return objectMapper.readValue(obj.toString(), new TypeReference<Set<AnomalySummary>>() {});
+            return objectMapper.readValue(obj.toString(), new TypeReference<>() {
+            });
         } catch (Exception e) {
             metrics.updateErrors();
-            log.error("Error occurred while getting latest anomalies for signalId:{}, accountId:{}.",
-                    signalId, accountIdentifier, e);
+            log.error("Error occurred while getting latest anomalies for signalId:{}.", signalId, e);
             return new HashSet<>();
         }
     }
 
-    public SignalSummary getSignalDetails(String accountIdentifier, String signalId) {
-        String key = "/accounts/" + accountIdentifier+"/signals/"+signalId+"/summary";
-        String hashKey = "ACCOUNTS_"+accountIdentifier+"_SIGNALS_"+signalId+"_SUMMARY";
+    public SignalSummary getSignalSummary(String signalId) {
+        String key = "/signals/" + signalId + "/summary";
+        String hashKey = "SIGNALS_" + signalId + "_SUMMARY";
         try {
             Object obj = redisTemplate.opsForHash().get(key, hashKey);
             if (obj == null) {
-                log.debug("Signal detail id unavailable for accountId:{}, signalId:{}.",accountIdentifier, signalId);
+                log.debug("Signal summary detail unavailable for signalId:{}.", signalId);
                 metrics.updateRedisKeysNotFound();
                 metrics.updateSnapshots(key, 1);
                 return null;
             }
 
-            return objectMapper.readValue(obj.toString(), new TypeReference<SignalSummary>() {});
+            return objectMapper.readValue(obj.toString(), new TypeReference<>() {
+            });
         } catch (Exception e) {
             metrics.updateErrors();
-            log.error("Error occurred while getting signal detail for signalId:{}, accountId:{}.",
-                    signalId, accountIdentifier, e);
+            log.error("Error occurred while getting signal summary detail for signalId:{}.",
+                    signalId, e);
             return null;
         }
     }
 
-    public void updateSignalDetails(String accountIdentifier, String signalId, SignalSummary signalSummary) {
-        String key = "/accounts/" + accountIdentifier+"/signals/"+signalId+"/summary";
-        String hashKey = "ACCOUNTS_"+accountIdentifier+"_SIGNALS_"+signalId+"_SUMMARY";
+    public void updateSignalSummary(String signalId, SignalSummary signalSummary) {
+        String key = "/signals/" + signalId + "/summary";
+        String hashKey = "SIGNALS_" + signalId + "_SUMMARY";
         try {
             String dataFromDBInJson = objectMapper.writeValueAsString(signalSummary);
             redisTemplate.opsForHash().put(key, hashKey, dataFromDBInJson);
         } catch (Exception e) {
             metrics.updateErrors();
-            log.error("Error occurred while getting signal detail for signalId:{}, accountId:{}.",
-                    signalId, accountIdentifier, e);
+            log.error("Error occurred while updating signal detail for signalId:{}", signalId, e);
         }
     }
 
-    public Set<SignalServiceSummary> getSignalServiceSummary(String accountIdentifier, String signalId) {
+    public Set<SignalServiceSummary> getSignalServiceSummary(String signalId) {
         Object obj = null;
         try {
-            obj = redisTemplate.opsForHash().get("/accounts/" + accountIdentifier+"/signals/" + signalId+"/services/summary",
-                    "ACCOUNTS_" + accountIdentifier + "_SIGNALS_" + signalId+"_SERVICES_SUMMARY");
+            obj = redisTemplate.opsForHash().get("/signals/" + signalId + "/services/summary",
+                    "SIGNALS_" + signalId + "_SERVICES_SUMMARY");
 
             if (obj == null) {
-                log.debug("Signal summary is unavailable for accountId:{}, signalId:{}.",accountIdentifier, signalId);
+                log.debug("Signal services summary is unavailable for signalId:{}.", signalId);
                 metrics.updateRedisKeysNotFound();
-                metrics.updateSnapshots("/accounts/" + accountIdentifier+"/signals/" + signalId+"/summary", 1);
+                metrics.updateSnapshots("/signals/" + signalId + "/summary", 1);
                 return Collections.emptySet();
             }
-            return objectMapper.readValue(obj.toString(), new TypeReference<Set<SignalServiceSummary>>() {
+            return objectMapper.readValue(obj.toString(), new TypeReference<>() {
             });
         } catch (JsonProcessingException e) {
-            log.error("Error while converting signal summary JSON string for accountId:{}, signalId:{}. " +
-                    "Redis data:{}.", accountIdentifier, signalId, obj, e);
+            log.error("Error while converting signal service summary JSON string for signalId:{}. " +
+                    "Redis data:{}.", signalId, obj, e);
             metrics.updateErrors();
             return Collections.emptySet();
         } catch (Exception e) {
             metrics.updateErrors();
-            log.error("Error occurred while getting signal summary for accountId:{}, signalId:{}.",
-                    accountIdentifier, signalId, e);
+            log.error("Error occurred while getting signal service summary for signalId:{}.", signalId, e);
             return Collections.emptySet();
         }
     }
 
-    public void updateServicesSummaryForSignal(String accountIdentifier, String signalId, Set<SignalServiceSummary> serviceSummary) {
+    public void updateServicesSummaryForSignal(String signalId, Set<SignalServiceSummary> serviceSummary) {
         try {
             String dataFromDBInJson = objectMapper.writeValueAsString(serviceSummary);
-            redisTemplate.opsForHash().put("/accounts/" + accountIdentifier+"/signals/" + signalId+"/services/summary",
-                    "ACCOUNTS_" + accountIdentifier + "_SIGNALS_" + signalId+"_SERVICES_SUMMARY", dataFromDBInJson);
+            redisTemplate.opsForHash().put("/signals/" + signalId + "/services/summary",
+                    "SIGNALS_" + signalId + "_SERVICES_SUMMARY", dataFromDBInJson);
         } catch (Exception e) {
             metrics.updateErrors();
             log.error("Error occurred while updating services summary for signalId:{}, anomaly:{}.",
@@ -531,38 +652,37 @@ public class RedisUtilities {
         }
     }
 
-    public Set<SignalInstanceSummary> getSignalInstanceSummary(String accountIdentifier, String signalId) {
+    public Set<SignalInstanceSummary> getSignalInstanceSummary(String signalId) {
         Object obj = null;
         try {
-            obj = redisTemplate.opsForHash().get("/accounts/" + accountIdentifier+"/signals/" + signalId+"/instances/summary",
-                    "ACCOUNTS_" + accountIdentifier + "_SIGNALS_" + signalId+"_INSTANCES_SUMMARY");
+            obj = redisTemplate.opsForHash().get("/signals/" + signalId + "/instances/summary",
+                    "SIGNALS_" + signalId + "_INSTANCES_SUMMARY");
 
             if (obj == null) {
-                log.debug("Signal instances summary is unavailable for accountId:{}, signalId:{}.",accountIdentifier, signalId);
+                log.debug("Signal instances summary is unavailable for signalId:{}.", signalId);
                 metrics.updateRedisKeysNotFound();
-                metrics.updateSnapshots("/accounts/" + accountIdentifier+"/signals/" + signalId+"/instances/summary", 1);
+                metrics.updateSnapshots("/signals/" + signalId + "/instances/summary", 1);
                 return Collections.emptySet();
             }
-            return objectMapper.readValue(obj.toString(), new TypeReference<Set<SignalInstanceSummary>>() {
+            return objectMapper.readValue(obj.toString(), new TypeReference<>() {
             });
         } catch (JsonProcessingException e) {
-            log.error("Error while converting signal instances summary JSON string for accountId:{}, signalId:{}. " +
-                    "Redis data:{}.", accountIdentifier, signalId, obj, e);
+            log.error("Error while converting signal instances summary JSON string for signalId:{}. " +
+                    "Redis data:{}.", signalId, obj, e);
             metrics.updateErrors();
             return Collections.emptySet();
         } catch (Exception e) {
             metrics.updateErrors();
-            log.error("Error occurred while getting signal instances summary for accountId:{}, signalId:{}.",
-                    accountIdentifier, signalId, e);
+            log.error("Error occurred while getting signal instances summary for signalId:{}.", signalId, e);
             return Collections.emptySet();
         }
     }
 
-    public void updateInstancesSummaryForSignal(String accountIdentifier, String signalId, Set<SignalInstanceSummary> instancesSummary) {
+    public void updateInstancesSummaryForSignal(String signalId, Set<SignalInstanceSummary> instancesSummary) {
         try {
             String dataFromDBInJson = objectMapper.writeValueAsString(instancesSummary);
-            redisTemplate.opsForHash().put("/accounts/" + accountIdentifier+"/signals/" + signalId+"/instances/summary",
-                    "ACCOUNTS_" + accountIdentifier + "_SIGNALS_" + signalId+"_INSTANCES_SUMMARY", dataFromDBInJson);
+            redisTemplate.opsForHash().put("/signals/" + signalId + "/instances/summary",
+                    "SIGNALS_" + signalId + "_INSTANCES_SUMMARY", dataFromDBInJson);
         } catch (Exception e) {
             metrics.updateErrors();
             log.error("Error occurred while updating instances summary for signalId:{}, anomaly:{}.",
@@ -570,38 +690,37 @@ public class RedisUtilities {
         }
     }
 
-    public Set<SignalTransactionSummary> getSignalTransactionSummary(String accountIdentifier, String signalId) {
+    public Set<SignalTransactionSummary> getSignalTransactionSummary(String signalId) {
         Object obj = null;
         try {
-            obj = redisTemplate.opsForHash().get("/accounts/" + accountIdentifier+"/signals/" + signalId+"/transactions/summary",
-                    "ACCOUNTS_" + accountIdentifier + "_SIGNALS_" + signalId+"_TRANSACTIONS_SUMMARY");
+            obj = redisTemplate.opsForHash().get("/signals/" + signalId + "/transactions/summary",
+                    "SIGNALS_" + signalId + "_TRANSACTIONS_SUMMARY");
 
             if (obj == null) {
-                log.debug("Signal transaction summary is unavailable for accountId:{}, signalId:{}.",accountIdentifier, signalId);
+                log.debug("Signal transaction summary is unavailable for signalId:{}.", signalId);
                 metrics.updateRedisKeysNotFound();
-                metrics.updateSnapshots("/accounts/" + accountIdentifier+"/signals/" + signalId+"/transactions/summary", 1);
+                metrics.updateSnapshots("/signals/" + signalId + "/transactions/summary", 1);
                 return Collections.emptySet();
             }
-            return objectMapper.readValue(obj.toString(), new TypeReference<Set<SignalTransactionSummary>>() {
+            return objectMapper.readValue(obj.toString(), new TypeReference<>() {
             });
         } catch (JsonProcessingException e) {
-            log.error("Error while converting signal transactions summary JSON string for accountId:{}, signalId:{}. " +
-                    "Redis data:{}.", accountIdentifier, signalId, obj, e);
+            log.error("Error while converting signal transactions summary JSON string for signalId:{}. " +
+                    "Redis data:{}.", signalId, obj, e);
             metrics.updateErrors();
             return Collections.emptySet();
         } catch (Exception e) {
             metrics.updateErrors();
-            log.error("Error occurred while getting signal transactions summary for accountId:{}, signalId:{}.",
-                    accountIdentifier, signalId, e);
+            log.error("Error occurred while getting signal transactions summary for signalId:{}.", signalId, e);
             return Collections.emptySet();
         }
     }
 
-    public void updateTransactionsSummaryForSignal(String accountIdentifier, String signalId, Set<SignalTransactionSummary> txnsSummary) {
+    public void updateTransactionsSummaryForSignal(String signalId, Set<SignalTransactionSummary> txnsSummary) {
         try {
             String dataFromDBInJson = objectMapper.writeValueAsString(txnsSummary);
-            redisTemplate.opsForHash().put("/accounts/" + accountIdentifier+"/signals/" + signalId+"/transactions/summary",
-                    "ACCOUNTS_" + accountIdentifier + "_SIGNALS_" + signalId+"_TRANSACTIONS_SUMMARY", dataFromDBInJson);
+            redisTemplate.opsForHash().put("/signals/" + signalId + "/transactions/summary",
+                    "SIGNALS_" + signalId + "_TRANSACTIONS_SUMMARY", dataFromDBInJson);
         } catch (Exception e) {
             metrics.updateErrors();
             log.error("Error occurred while updating transactions summary for signalId:{}, anomaly:{}.",
@@ -610,8 +729,8 @@ public class RedisUtilities {
     }
 
     public Map<Integer, List<BasicEntity>> getAccountOutbounds(String accountIdentifier) {
-        String key = "/accounts/" + accountIdentifier+"/outbounds";
-        String hashKey = "ACCOUNTS_"+accountIdentifier+"_OUTBOUNDS";
+        String key = "/accounts/" + accountIdentifier + "/outbounds";
+        String hashKey = "ACCOUNTS_" + accountIdentifier + "_OUTBOUNDS";
         try {
             Object obj = redisTemplate.opsForHash().get(key, hashKey);
             if (obj == null) {
@@ -643,7 +762,7 @@ public class RedisUtilities {
                 return new ArrayList<>();
             }
 
-            return objectMapper.readValue(obj.toString(), new TypeReference<List<TenantOpenSearchDetails>>() {
+            return objectMapper.readValue(obj.toString(), new TypeReference<>() {
             });
         } catch (Exception e) {
             log.error("Error occurred while getting to tenant opensearch mapping details for tenant identifier [{}]. ", tenantIdentifier, e);
@@ -664,11 +783,82 @@ public class RedisUtilities {
                 return Collections.emptyList();
             }
 
-            return objectMapper.readValue(obj.toString(), new TypeReference<List<OSIndexZoneDetails>>() {
+            return objectMapper.readValue(obj.toString(), new TypeReference<>() {
             });
         } catch (Exception e) {
             log.error("Error occurred while getting Heal opensearch index to zone mapping.", e);
             return Collections.emptyList();
         }
     }
+
+    public SignalDetails getSignalDetails(String signalId) {
+        try {
+            String key = "/signals/" + signalId + "/details";
+            String hashKey = "SIGNALS_" + signalId + "_DETAILS";
+
+            Object obj = redisTemplate.opsForHash().entries(key).get(hashKey);
+            if (obj == null) {
+                log.error("No signal details for signal identifier [{}] available", signalId);
+                metrics.updateRedisKeysNotFound();
+                metrics.updateSnapshots(key, 1);
+                return null;
+            }
+
+            return objectMapper.readValue(obj.toString(), new TypeReference<>() {
+            });
+        } catch (Exception e) {
+            metrics.updateSignalDetailsKeyErrorsGET();
+            log.error("Error occurred while getting signal details for signal identifier [{}].", signalId, e);
+            return null;
+        }
+    }
+
+    public void updateSignalDetails(SignalDetails signalDetail) {
+        try {
+            String dataFromDBInJson = objectMapper.writeValueAsString(signalDetail);
+            redisTemplate.opsForHash().put("/signals/" + signalDetail.getSignalId() + "/details",
+                    "SIGNALS_" + signalDetail.getSignalId() + "_DETAILS", dataFromDBInJson);
+        } catch (Exception e) {
+            metrics.updateSignalDetailsKeyErrorsPUT();
+            metrics.updateErrors();
+            log.error("Error occurred while updating signal details for signalId:{}.", signalDetail.getSignalId(), e);
+        }
+    }
+
+    public void deleteSignalDetails(String signalId) {
+        try {
+            redisTemplate.opsForHash().delete("/signals/" + signalId + "/details", "SIGNALS_" + signalId + "_DETAILS");
+        } catch (Exception e) {
+            metrics.updateSignalDetailsKeyErrorsDELETE();
+            metrics.updateErrors();
+            log.error("Error occurred while deleting signal details key for signalId:{}.", signalId, e);
+        }
+    }
+
+    /**
+     * Retrieves a list of applications mapped to a specific service in a given account.
+     *
+     * @param accIdentifier      The account identifier.
+     * @param serviceIdentifier  The service identifier.
+     * @return A list of BasicEntity representing the applications mapped to the service.
+     */
+    public List<BasicEntity> getApplicationsMappedToService(String accIdentifier, String serviceIdentifier) {
+        try {
+            String key = "/accounts/" + accIdentifier + "/services/" + serviceIdentifier + "/applications";
+            String hashKey = "ACCOUNTS_" + accIdentifier + "_SERVICES_" + serviceIdentifier + "_APPLICATIONS";
+
+            Object obj = redisTemplate.opsForHash().entries(key).get(hashKey);
+            if (obj == null) {
+                log.error("Mapped Application details unavailable for service identifier [{}] mapped to account [{}]", serviceIdentifier, accIdentifier);
+                metrics.updateRedisKeysNotFound();
+                metrics.updateSnapshots("/accounts/" + accIdentifier + "/services/" + serviceIdentifier + "/applications", 1);
+                return new ArrayList<>();
+            }
+            return objectMapper.readValue(obj.toString(), new TypeReference<>() {
+            });
+        } catch (Exception e) {
+            log.error("Error while getting mapped application details for service identifier [{}] mapped to account [{}]. Details: ", serviceIdentifier, accIdentifier, e);
+            return new ArrayList<>();
+        }
+    }
 }
diff --git a/src/test/java/com/heal/signal/detector/process/BatchSignalProcessorTest.java b/src/test/java/com/heal/signal/detector/process/BatchSignalProcessorTest.java
index b35040f..b414737 100644
--- a/src/test/java/com/heal/signal/detector/process/BatchSignalProcessorTest.java
+++ b/src/test/java/com/heal/signal/detector/process/BatchSignalProcessorTest.java
@@ -5,6 +5,7 @@ import com.heal.configuration.enums.SignalType;
 import com.heal.configuration.pojos.Account;
 import com.heal.signal.detector.TestMain;
 import com.heal.signal.detector.opensearch.SignalRepo;
+import com.heal.signal.detector.util.HealthMetrics;
 import com.heal.signal.detector.util.LocalQueues;
 import com.heal.signal.detector.util.TestUtil;
 import org.junit.jupiter.api.Test;
@@ -27,10 +28,13 @@ class BatchSignalProcessorTest extends TestMain {
     @Mock
    LocalQueues localQueues;
 
+    @Mock
+    HealthMetrics healthMetrics;
+
     @Test
     void processBatchJobEvent() throws Exception {
         String accountId = "sample_account_identifier";
-        Mockito.when(signalRepo.getOpenSignals(accountId, true)).thenThrow(new Exception());
+        Mockito.when(signalRepo.getOpenSignals(accountId, true, true)).thenThrow(new Exception());
 
         Map<String, String> metaDataMap = new HashMap<>();
         String groupName = "sample_group_name";
diff --git a/src/test/java/com/heal/signal/detector/process/GenericSignalProcessorTest.java b/src/test/java/com/heal/signal/detector/process/GenericSignalProcessorTest.java
new file mode 100644
index 0000000..4e44b62
--- /dev/null
+++ b/src/test/java/com/heal/signal/detector/process/GenericSignalProcessorTest.java
@@ -0,0 +1,134 @@
+package com.heal.signal.detector.process;
+
+import com.appnomic.appsone.common.protbuf.AnomalyEventProtos;
+import com.heal.configuration.enums.SignalType;
+import com.heal.configuration.pojos.Account;
+import com.heal.configuration.pojos.AnomalySummary;
+import com.heal.configuration.pojos.BasicKpiEntity;
+import com.heal.configuration.pojos.KpiCategoryDetails;
+import com.heal.configuration.pojos.opensearch.SignalDetails;
+import com.heal.signal.detector.TestMain;
+import com.heal.signal.detector.cache.CacheWrapper;
+import com.heal.signal.detector.opensearch.SignalRepo;
+import com.heal.signal.detector.pojos.FailedOpenSignalPojo;
+import com.heal.signal.detector.service.ForwarderToQueue;
+import com.heal.signal.detector.util.*;
+import org.junit.jupiter.api.BeforeEach;
+import org.junit.jupiter.api.Test;
+import org.mockito.InjectMocks;
+import org.mockito.Mock;
+import org.mockito.MockitoAnnotations;
+
+import java.util.Collections;
+import java.util.HashMap;
+import java.util.HashSet;
+import java.util.Map;
+
+import static org.junit.jupiter.api.Assertions.assertEquals;
+import static org.junit.jupiter.api.Assertions.assertNull;
+import static org.mockito.ArgumentMatchers.*;
+import static org.mockito.Mockito.*;
+
+class GenericSignalProcessorTest extends TestMain {
+
+    @InjectMocks
+    GenericSignalProcessor genericSignalProcessor;
+
+    @Mock
+    RedisUtilities redisUtilities;
+
+    @Mock
+    Commons commons;
+
+    @Mock
+    HealthMetrics metrics;
+
+    @Mock
+    SignalRepo signalRepo;
+
+    @Mock
+    ForwarderToQueue forwarder;
+
+    @Mock
+    CacheWrapper wrapper;
+
+    @Mock
+    LocalQueues localQueues;
+
+    @BeforeEach
+    void setUp() {
+        MockitoAnnotations.openMocks(this);
+    }
+
+    @Test
+    void processSignalEvent_when_getOpenSignals_throws_should_enqueue_failedOpenSignalAndReturnNull() throws Exception {
+        String accountId = "acct1";
+        String appId = "app1";
+        String kpiId = "101";
+        Map<String, String> meta = new HashMap<>();
+        AnomalyEventProtos.AnomalyEvent anomalyEvent = TestUtil.buildAnomalyData(System.currentTimeMillis(), System.currentTimeMillis(), meta,
+                new HashMap<>(), "AE-G-1", accountId, appId, "T", "OP", kpiId, "10", false, "Low", false);
+
+        Account account = Account.builder().identifier(accountId).id(10).build();
+
+        when(signalRepo.getOpenSignals(eq(accountId), eq(true), eq(true))).thenThrow(new RuntimeException("OS down"));
+
+        String result = genericSignalProcessor.processSignalEvent(SignalType.EARLY_WARNING, anomalyEvent, BasicKpiEntity.builder().identifier(kpiId).build(), account, new HashSet<>(anomalyEvent.getAppIdList()), null);
+
+        assertNull(result);
+        verify(localQueues, times(1)).addToFailedOpenSignalQueue(argThat((FailedOpenSignalPojo pojo) ->
+                pojo.getSignalType() == SignalType.EARLY_WARNING && pojo.getAnomalyEvent() == anomalyEvent && pojo.getAccount() == account
+        ));
+    }
+
+    @Test
+    void processSignalEvent_create_new_signal_success_path_returnsSignalId_and_sendsMessage() throws Exception {
+        String accountId = "acct2";
+        String appId = "app2";
+        String kpiId = "202";
+        Map<String, String> meta = new HashMap<>();
+        AnomalyEventProtos.AnomalyEvent anomalyEvent = TestUtil.buildAnomalyData(System.currentTimeMillis(), System.currentTimeMillis(), meta,
+                new HashMap<>(), "AE-G-2", accountId, appId, "T", "OP", kpiId, "55", false, "431", false);
+
+        Account account = Account.builder().identifier(accountId).id(20).build();
+
+        // Mock kpiEntity with non-null categoryDetails and groupIdentifier to avoid NPE inside processSignalEvent
+        BasicKpiEntity kpiEntity = mock(BasicKpiEntity.class);
+        when(kpiEntity.getIdentifier()).thenReturn(kpiId);
+        KpiCategoryDetails categoryDetails = mock(KpiCategoryDetails.class);
+        when(categoryDetails.getIdentifier()).thenReturn("cat-1");
+        when(kpiEntity.getCategoryDetails()).thenReturn(categoryDetails);
+        when(kpiEntity.getGroupIdentifier()).thenReturn("grp-1");
+
+        when(signalRepo.getOpenSignals(eq(accountId), eq(true), eq(true))).thenReturn(new HashSet<>());
+        when(commons.getSignalMetaData(eq(anomalyEvent), eq(SignalType.EARLY_WARNING))).thenReturn(new HashMap<>());
+        // Ensure processAndGetOpenSignals returns empty set (mock default would be null and cause NPE)
+        when(commons.processAndGetOpenSignals(anySet(), anyString(), anyString(), anyMap(), anyMap(), anySet(), anyBoolean(), anySet())).thenReturn(new HashSet<>());
+        // Avoid null from redis utilities
+        when(redisUtilities.getServiceAliases()).thenReturn(new HashSet<>());
+
+        String expectedSignalId = "E-20-202-999-" + (anomalyEvent.getEndTimeGMT() / 1000);
+        when(commons.createSignalId(anyString(), anyInt(), anyInt(), anyInt(), anyLong())).thenReturn(expectedSignalId);
+
+        SignalDetails createdSignal = SignalDetails.builder().signalId(expectedSignalId).currentStatus("OPEN").serviceIds(new HashSet<>()).anomalies(new HashSet<>(Collections.singleton(anomalyEvent.getAnomalyId()))).metadata(new HashMap<>()).build();
+        when(commons.createSignal(eq(expectedSignalId), anyLong(), anyLong(), anyLong(), anyString(), anySet(), eq(SignalType.EARLY_WARNING), anySet(), anySet(), anySet(), any(), any(), anyInt(), anyMap(), eq(anomalyEvent.getAccountId()))).thenReturn(createdSignal);
+
+        AnomalySummary mockAnomalySummary = mock(AnomalySummary.class);
+        when(commons.getAnomalySummary(
+                anyString(), anyString(), anyString(), anySet(), eq(SignalType.EARLY_WARNING), anyInt(), anyLong(), anyBoolean(),
+                anyString(), anyString(), anyString(), anyString(), anyString(), anyMap(), anyString(), anyMap(), anyString()
+        )).thenReturn(mockAnomalySummary);
+
+        when(signalRepo.insertSignal(eq(createdSignal), eq(anomalyEvent.getAccountId()), any(AnomalySummary.class), eq(true), anySet(), eq(false), anyInt())).thenReturn(true);
+        when(signalRepo.updateAnomaly(eq(anomalyEvent.getAnomalyId()), eq(anomalyEvent.getEndTimeGMT()), anySet(), eq(anomalyEvent.getAccountId()))).thenReturn(true);
+
+        when(commons.getSignalProto(eq(createdSignal), anyBoolean(), anyBoolean(), anyBoolean(), anyBoolean(), eq(mockAnomalySummary))).thenReturn(com.appnomic.appsone.common.protbuf.SignalProtos.SignalDetails.newBuilder().setSignalId(expectedSignalId).build());
+
+        String result = genericSignalProcessor.processSignalEvent(SignalType.EARLY_WARNING, anomalyEvent, kpiEntity, account, new HashSet<>(anomalyEvent.getAppIdList()), null);
+
+        assertEquals(expectedSignalId, result);
+        verify(signalRepo, times(1)).insertSignal(eq(createdSignal), eq(anomalyEvent.getAccountId()), eq(mockAnomalySummary), eq(true), anySet(), eq(false), anyInt());
+        verify(forwarder, times(1)).sendSignalMessages(any());
+        verify(metrics, times(1)).updateSignalOpenCount(1);
+    }
+}
diff --git a/src/test/java/com/heal/signal/detector/process/InfoSignalProcessorTest.java b/src/test/java/com/heal/signal/detector/process/InfoSignalProcessorTest.java
index ee55673..a765351 100644
--- a/src/test/java/com/heal/signal/detector/process/InfoSignalProcessorTest.java
+++ b/src/test/java/com/heal/signal/detector/process/InfoSignalProcessorTest.java
@@ -6,6 +6,7 @@ import com.heal.configuration.pojos.Account;
 import com.heal.configuration.pojos.CompInstKpiEntity;
 import com.heal.signal.detector.TestMain;
 import com.heal.signal.detector.opensearch.SignalRepo;
+import com.heal.signal.detector.util.HealthMetrics;
 import com.heal.signal.detector.util.LocalQueues;
 import com.heal.signal.detector.util.TestUtil;
 import org.junit.jupiter.api.Test;
@@ -27,7 +28,9 @@ class InfoSignalProcessorTest extends TestMain {
     SignalRepo signalRepo;
 
     @Mock
-    private LocalQueues localQueues;
+    LocalQueues localQueues;
+    @Mock
+    HealthMetrics metrics;
 
     private final String accountId = "sample_account_identifier";
 
@@ -36,9 +39,7 @@ class InfoSignalProcessorTest extends TestMain {
 
     @Test
     void processBatchJobEvent() throws Exception {
-
-
-        Mockito.when(signalRepo.getOpenSignals(accountId, true)).thenThrow(new Exception());
+        Mockito.when(signalRepo.getOpenSignals(accountId, true, true)).thenThrow(new Exception());
 
         Map<String, String> metaDataMap = new HashMap<>();
         metaDataMap.put("group_name", groupName);
diff --git a/src/test/java/com/heal/signal/detector/process/ProblemSignalProcessorTest.java b/src/test/java/com/heal/signal/detector/process/ProblemSignalProcessorTest.java
index 4926c4f..86ee0c1 100644
--- a/src/test/java/com/heal/signal/detector/process/ProblemSignalProcessorTest.java
+++ b/src/test/java/com/heal/signal/detector/process/ProblemSignalProcessorTest.java
@@ -6,6 +6,7 @@ import com.heal.configuration.pojos.Account;
 import com.heal.configuration.pojos.BasicKpiEntity;
 import com.heal.signal.detector.TestMain;
 import com.heal.signal.detector.opensearch.SignalRepo;
+import com.heal.signal.detector.util.HealthMetrics;
 import com.heal.signal.detector.util.LocalQueues;
 import com.heal.signal.detector.util.TestUtil;
 import org.junit.jupiter.api.Test;
@@ -27,6 +28,8 @@ class ProblemSignalProcessorTest extends TestMain {
 
     @Mock
     private LocalQueues localQueues;
+    @Mock
+    HealthMetrics healthMetrics;
 
     private final String accountId = "sample_account_identifier";
 
@@ -35,7 +38,7 @@ class ProblemSignalProcessorTest extends TestMain {
 
     @Test
     void processBatchJobEvent() throws Exception {
-        Mockito.when(signalRepo.getOpenSignals(accountId, true)).thenThrow(new Exception());
+        Mockito.when(signalRepo.getOpenSignals(accountId, true, true)).thenThrow(new Exception());
 
         Map<String, String> metaDataMap = new HashMap<>();
         metaDataMap.put("group_name", groupName);
diff --git a/src/test/java/com/heal/signal/detector/process/SignalHandlerTest.java b/src/test/java/com/heal/signal/detector/process/SignalHandlerTest.java
new file mode 100644
index 0000000..fbb0faf
--- /dev/null
+++ b/src/test/java/com/heal/signal/detector/process/SignalHandlerTest.java
@@ -0,0 +1,236 @@
+package com.heal.signal.detector.process;
+
+import com.appnomic.appsone.common.protbuf.AnomalyEventProtos;
+import com.appnomic.appsone.common.protbuf.SignalProtos;
+import com.heal.configuration.enums.SignalType;
+import com.heal.configuration.pojos.Account;
+import com.heal.configuration.pojos.BasicKpiEntity;
+import com.heal.configuration.pojos.CompInstKpiEntity;
+import com.heal.configuration.pojos.opensearch.Anomalies;
+import com.heal.configuration.pojos.opensearch.SignalDetails;
+import com.heal.signal.detector.TestMain;
+import com.heal.signal.detector.opensearch.SignalRepo;
+import com.heal.signal.detector.service.ForwarderToQueue;
+import com.heal.signal.detector.util.Commons;
+import com.heal.signal.detector.util.HealthMetrics;
+import com.heal.signal.detector.util.RedisUtilities;
+import com.heal.signal.detector.util.TestUtil;
+import com.heal.signal.detector.pojos.SignalStatus;
+import org.junit.jupiter.api.BeforeEach;
+import org.junit.jupiter.api.Test;
+import org.mockito.InjectMocks;
+import org.mockito.Mock;
+import org.mockito.MockitoAnnotations;
+
+import java.util.*;
+
+import static org.mockito.ArgumentMatchers.any;
+import static org.mockito.Mockito.*;
+
+class SignalHandlerTest extends TestMain {
+
+    @InjectMocks
+    SignalHandler signalHandler;
+
+    @Mock
+    Commons commons;
+
+    @Mock
+    GenericSignalProcessor genericSignalProcessor;
+
+    @Mock
+    SignalRepo signalRepo;
+
+    @Mock
+    RedisUtilities redisUtilities;
+
+    @Mock
+    ForwarderToQueue forwarder;
+
+    @Mock
+    HealthMetrics metrics;
+
+    @BeforeEach
+    void setUp() {
+        MockitoAnnotations.openMocks(this);
+    }
+
+    @Test
+    void handleAnomalyEvent_open_workload_with_txnKpi_should_call_problem_processor() {
+        String accountId = "acc1";
+        String appId = "app1";
+        String kpiId = "100";
+        Map<String, String> meta = new HashMap<>();
+        AnomalyEventProtos.AnomalyEvent anomalyEvent = TestUtil.buildAnomalyData(System.currentTimeMillis(), System.currentTimeMillis(), meta,
+                new HashMap<>(), "AE-1", accountId, appId, "T", "OP", kpiId, "10", true, "High", false);
+
+        BasicKpiEntity txnKpi = BasicKpiEntity.builder().identifier(kpiId).build();
+        CompInstKpiEntity kpiEntity = null;
+        Account account = Account.builder().identifier(accountId).id(1).build();
+        Anomalies anomaly = mock(Anomalies.class);
+        when(anomaly.getSignalIds()).thenReturn(Collections.emptySet());
+
+        Set<String> appIds = new HashSet<>(anomalyEvent.getAppIdList());
+
+        signalHandler.handleAnomalyEvent(anomalyEvent, "OPEN", true, null, accountId, anomalyEvent.getAnomalyId(), 431, meta, txnKpi, kpiEntity, account, anomaly, appIds);
+
+        verify(genericSignalProcessor, times(1)).processSignalEvent(eq(SignalType.PROBLEM), eq(anomalyEvent), eq(txnKpi), eq(account), eq(appIds), isNull());
+    }
+
+    @Test
+    void handleAnomalyEvent_open_with_txnKpi_non_workload_should_call_early_warning_and_return() {
+        String accountId = "acc2";
+        String appId = "app2";
+        String kpiId = "200";
+        Map<String, String> meta = new HashMap<>();
+        AnomalyEventProtos.AnomalyEvent anomalyEvent = TestUtil.buildAnomalyData(System.currentTimeMillis(), System.currentTimeMillis(), meta,
+                new HashMap<>(), "AE-2", accountId, appId, "T", "OP", kpiId, "20", false, "Medium", false);
+
+        BasicKpiEntity txnKpi = BasicKpiEntity.builder().identifier(kpiId).build();
+        CompInstKpiEntity kpiEntity = null;
+        Account account = Account.builder().identifier(accountId).id(2).build();
+        Anomalies anomaly = mock(Anomalies.class);
+        when(anomaly.getSignalIds()).thenReturn(Collections.emptySet());
+
+        Set<String> appIds = new HashSet<>(anomalyEvent.getAppIdList());
+
+        signalHandler.handleAnomalyEvent(anomalyEvent, "OPEN", false, null, accountId, anomalyEvent.getAnomalyId(), 431, meta, txnKpi, kpiEntity, account, anomaly, appIds);
+
+        verify(genericSignalProcessor, times(1)).processSignalEvent(eq(SignalType.EARLY_WARNING), eq(anomalyEvent), eq(txnKpi), eq(account), eq(appIds), isNull());
+    }
+
+    @Test
+    void handleAnomalyEvent_open_without_txnKpi_should_call_early_warning_with_kpiEntity() {
+        String accountId = "acc3";
+        String appId = "app3";
+        String kpiId = "300";
+        Map<String, String> meta = new HashMap<>();
+        AnomalyEventProtos.AnomalyEvent anomalyEvent = TestUtil.buildAnomalyData(System.currentTimeMillis(), System.currentTimeMillis(), meta,
+                new HashMap<>(), "AE-3", accountId, appId, "T", "OP", kpiId, "30", false, "Low", false);
+
+        BasicKpiEntity txnKpi = null;
+        CompInstKpiEntity kpiEntity = CompInstKpiEntity.builder().identifier(kpiId).build();
+        Account account = Account.builder().identifier(accountId).id(3).build();
+        Anomalies anomaly = mock(Anomalies.class);
+        when(anomaly.getSignalIds()).thenReturn(Collections.emptySet());
+
+        Set<String> appIds = new HashSet<>(anomalyEvent.getAppIdList());
+
+        signalHandler.handleAnomalyEvent(anomalyEvent, "OPEN", false, null, accountId, anomalyEvent.getAnomalyId(), 431, meta, txnKpi, kpiEntity, account, anomaly, appIds);
+
+        verify(genericSignalProcessor, times(1)).processSignalEvent(eq(SignalType.EARLY_WARNING), eq(anomalyEvent), eq(kpiEntity), eq(account), eq(appIds), isNull());
+    }
+
+    @Test
+    void handleAnomalyEvent_close_should_close_signal_and_send_message() {
+        String accountId = "acc4";
+        String appId = "app4";
+        String kpiId = "400";
+        String anomalyId = "ANOM-4";
+
+        Map<String, String> meta = new HashMap<>();
+        AnomalyEventProtos.AnomalyEvent anomalyEvent = TestUtil.buildAnomalyData(System.currentTimeMillis(), System.currentTimeMillis(), meta,
+                new HashMap<>(), anomalyId, accountId, appId, "T", "OP", kpiId, "40", false, "Low", false);
+
+        BasicKpiEntity txnKpi = null;
+        CompInstKpiEntity kpiEntity = CompInstKpiEntity.builder().identifier(kpiId).build();
+        Account account = Account.builder().identifier(accountId).id(4).build();
+
+        // Prepare anomaly with one signal id
+        Anomalies anomaly = mock(Anomalies.class);
+        Set<String> signalIds = new HashSet<>();
+        signalIds.add("E-1-1-1-1");
+        when(anomaly.getSignalIds()).thenReturn(signalIds);
+
+        // Prepare SignalDetails returned by repo
+        SignalDetails signal = SignalDetails.builder()
+                .signalId("E-1-1-1-1")
+                .currentStatus(SignalStatus.OPEN.name())
+                .anomalies(new HashSet<>(Collections.singletonList(anomalyId)))
+                .metadata(new HashMap<>())
+                .serviceIds(new HashSet<>(Collections.singletonList("svc1")))
+                .relatedSignals(new HashSet<>())
+                .startedTime(System.currentTimeMillis())
+                .build();
+
+        when(signalRepo.getSignalById(signalIds, accountId)).thenReturn(Collections.singletonList(signal));
+        when(signalRepo.closeSignal(any(SignalDetails.class), eq(accountId), eq(appId))).thenReturn(true);
+        when(signalRepo.removeAnomalyFromSignal(eq(anomalyId), eq(signal.getSignalId()), eq(accountId), anyLong(), eq(signal.getCurrentStatus()), anySet(), eq(true), anySet())).thenReturn(true);
+
+        // commons.getSignalProto used in closeSignal -> return a simple proto
+        when(commons.getSignalProto(any(SignalDetails.class), anyBoolean(), anyBoolean(), anyBoolean(), anyBoolean(), any())).thenReturn(SignalProtos.SignalDetails.newBuilder().build());
+
+        // redisUtilities.getSignalSummary may be used in path; return null so that branch won't fail
+        when(redisUtilities.getSignalSummary(anyString())).thenReturn(null);
+
+        Set<String> appIds = new HashSet<>(anomalyEvent.getAppIdList());
+
+        signalHandler.handleAnomalyEvent(anomalyEvent, "CLOSE", false, null, accountId, anomalyId, 431, meta, txnKpi, kpiEntity, account, anomaly, appIds);
+
+        verify(signalRepo, times(1)).closeSignal(any(SignalDetails.class), eq(accountId), eq(appId));
+        verify(signalRepo, times(1)).removeAnomalyFromSignal(eq(anomalyId), eq(signal.getSignalId()), eq(accountId), anyLong(), eq(signal.getCurrentStatus()), anySet(), eq(true), anySet());
+        verify(forwarder, atLeastOnce()).sendSignalMessages(any());
+    }
+
+    @Test
+    void closeSignal_successful_updates_metadata_and_sends_messages() {
+        String accountId = "acc_close1";
+        Set<String> appIds = new HashSet<>();
+        appIds.add("appA");
+        appIds.add("appB");
+
+        SignalDetails signal = SignalDetails.builder()
+                .signalId("S-1")
+                .currentStatus(SignalStatus.OPEN.name())
+                .metadata(new HashMap<>())
+                .startedTime(System.currentTimeMillis())
+                .build();
+
+        when(signalRepo.closeSignal(any(SignalDetails.class), eq(accountId), anyString())).thenReturn(true);
+        when(commons.getSignalProto(any(SignalDetails.class), anyBoolean(), anyBoolean(), anyBoolean(), anyBoolean(), any())).thenReturn(SignalProtos.SignalDetails.newBuilder().setSignalId("S-1").build());
+
+        signalHandler.closeSignal(signal, accountId, appIds, "closing for test", null);
+
+        // status and metadata updated on the same object
+        assert signal.getCurrentStatus().equals(SignalStatus.CLOSED.name());
+        assert signal.getMetadata().get("closing_reason").equals("closing for test");
+        assert signal.getMetadata().get("end_time") != null && !signal.getMetadata().get("end_time").isEmpty();
+
+        // repo called for each appId
+        verify(signalRepo, times(appIds.size())).closeSignal(any(SignalDetails.class), eq(accountId), anyString());
+        // forwarder should have been invoked for each successful close
+        verify(forwarder, times(appIds.size())).sendSignalMessages(any());
+        // metrics updated for each appId
+        verify(metrics, times(appIds.size())).updateSignalCloseCount();
+    }
+
+    @Test
+    void closeSignal_when_repo_fails_does_not_send_message_but_updates_metrics() {
+        String accountId = "acc_close2";
+        Set<String> appIds = new HashSet<>();
+        appIds.add("appX");
+
+        SignalDetails signal = SignalDetails.builder()
+                .signalId("S-2")
+                .currentStatus(SignalStatus.OPEN.name())
+                .metadata(new HashMap<>())
+                .startedTime(System.currentTimeMillis())
+                .build();
+
+        when(signalRepo.closeSignal(any(SignalDetails.class), eq(accountId), anyString())).thenReturn(false);
+
+        signalHandler.closeSignal(signal, accountId, appIds, "closing fail test", null);
+
+        // status still set to CLOSED in object before repo update
+        assert signal.getCurrentStatus().equals(SignalStatus.CLOSED.name());
+        assert signal.getMetadata().get("closing_reason").equals("closing fail test");
+        assert signal.getMetadata().get("end_time") != null && !signal.getMetadata().get("end_time").isEmpty();
+
+        // repo called once
+        verify(signalRepo, times(appIds.size())).closeSignal(any(SignalDetails.class), eq(accountId), anyString());
+        // forwarder should NOT be invoked because repo returned false
+        verify(forwarder, never()).sendSignalMessages(any());
+        // metrics updated even if repo fails (method updates before checking isUpdated)
+        verify(metrics, times(appIds.size())).updateSignalCloseCount();
+    }
+}
diff --git a/src/test/java/com/heal/signal/detector/process/WarningSignalProcessorTest.java b/src/test/java/com/heal/signal/detector/process/WarningSignalProcessorTest.java
index 631bdc8..7a78cb5 100644
--- a/src/test/java/com/heal/signal/detector/process/WarningSignalProcessorTest.java
+++ b/src/test/java/com/heal/signal/detector/process/WarningSignalProcessorTest.java
@@ -6,6 +6,7 @@ import com.heal.configuration.pojos.Account;
 import com.heal.configuration.pojos.BasicKpiEntity;
 import com.heal.signal.detector.TestMain;
 import com.heal.signal.detector.opensearch.SignalRepo;
+import com.heal.signal.detector.util.HealthMetrics;
 import com.heal.signal.detector.util.LocalQueues;
 import com.heal.signal.detector.util.TestUtil;
 import org.junit.jupiter.api.Test;
@@ -28,6 +29,8 @@ class WarningSignalProcessorTest extends TestMain {
 
     @Mock
     private LocalQueues localQueues;
+    @Mock
+    HealthMetrics metrics;
 
     private final String accountId = "sample_account_identifier";
 
@@ -36,7 +39,7 @@ class WarningSignalProcessorTest extends TestMain {
 
     @Test
     void processBatchJobEvent() throws Exception {
-        Mockito.when(signalRepo.getOpenSignals(accountId, true)).thenThrow(new Exception());
+        Mockito.when(signalRepo.getOpenSignals(accountId, true, true)).thenThrow(new Exception());
 
         Map<String, String> metaDataMap = new HashMap<>();
         metaDataMap.put("group_name", groupName);
@@ -56,6 +59,7 @@ class WarningSignalProcessorTest extends TestMain {
 
         BasicKpiEntity kpiEntity = BasicKpiEntity.builder().identifier(kpiId).build();
 
+
         warningSignalProcessor.processEarlyWarningEvent(anomalyEvent, kpiEntity, account);
 
         verify(localQueues).addToFailedOpenSignalQueue(argThat(pojo ->
diff --git a/src/test/java/com/heal/signal/detector/scheduler/DroppedAnomalyCheckerTest.java b/src/test/java/com/heal/signal/detector/scheduler/DroppedAnomalyCheckerTest.java
index 2cbd909..b8b60b7 100644
--- a/src/test/java/com/heal/signal/detector/scheduler/DroppedAnomalyCheckerTest.java
+++ b/src/test/java/com/heal/signal/detector/scheduler/DroppedAnomalyCheckerTest.java
@@ -7,10 +7,7 @@ import com.heal.configuration.pojos.BasicKpiEntity;
 import com.heal.configuration.pojos.CompInstKpiEntity;
 import com.heal.signal.detector.TestMain;
 import com.heal.signal.detector.pojos.FailedOpenSignalPojo;
-import com.heal.signal.detector.process.BatchSignalProcessor;
-import com.heal.signal.detector.process.InfoSignalProcessor;
-import com.heal.signal.detector.process.ProblemSignalProcessor;
-import com.heal.signal.detector.process.WarningSignalProcessor;
+import com.heal.signal.detector.process.*;
 import com.heal.signal.detector.util.LocalQueues;
 import com.heal.signal.detector.util.TestUtil;
 import org.junit.jupiter.api.Test;
@@ -40,6 +37,9 @@ class DroppedAnomalyCheckerTest extends TestMain {
     @Mock
     WarningSignalProcessor warningSignalProcessor;
 
+    @Mock
+    GenericSignalProcessor genericSignalProcessor;
+
     @Mock
     private LocalQueues localQueues;
 
@@ -143,11 +143,15 @@ class DroppedAnomalyCheckerTest extends TestMain {
                 "not equals", "sample_kpi_identifier", "", false, "Severe", false);
         Account account = Account.builder().identifier(accountId).id(9).build();
         BasicKpiEntity kpiEntity = BasicKpiEntity.builder().identifier(kpiId).build();
+        Set<String> appIds = new HashSet<>();
+        appIds.add("app1");
 
         FailedOpenSignalPojo failedAnomalyData = FailedOpenSignalPojo.builder().signalType(SignalType.PROBLEM)
                 .anomalyEvent(anomalyEvent)
                 .account(account)
                 .kpiEntity(kpiEntity)
+                .appIds(appIds)
+                .signalIds(null)
                 .build();
 
 
@@ -157,7 +161,7 @@ class DroppedAnomalyCheckerTest extends TestMain {
 
         droppedAnomalyChecker.checkDroppedAnomaly();
 
-        verify(problemSignalProcessor).processProblemEvent(anomalyEvent, kpiEntity, account);
+        verify(genericSignalProcessor).processSignalEvent(SignalType.PROBLEM,anomalyEvent, kpiEntity, account, appIds, null);
 
     }
 
@@ -180,11 +184,15 @@ class DroppedAnomalyCheckerTest extends TestMain {
                 "not equals", "sample_kpi_identifier", "", false, "Severe", false);
         Account account = Account.builder().identifier(accountId).id(9).build();
         BasicKpiEntity kpiEntity = BasicKpiEntity.builder().identifier(kpiId).build();
+        Set<String> appIds = new HashSet<>();
+        appIds.add("app1");
 
         FailedOpenSignalPojo failedAnomalyData = FailedOpenSignalPojo.builder().signalType(SignalType.EARLY_WARNING)
                 .anomalyEvent(anomalyEvent)
                 .account(account)
                 .kpiEntity(kpiEntity)
+                .appIds(appIds)
+                .signalIds(null)
                 .build();
 
 
@@ -194,7 +202,7 @@ class DroppedAnomalyCheckerTest extends TestMain {
 
         droppedAnomalyChecker.checkDroppedAnomaly();
 
-        verify(warningSignalProcessor).processEarlyWarningEvent(anomalyEvent, kpiEntity, account);
+        verify(genericSignalProcessor).processSignalEvent(SignalType.EARLY_WARNING,anomalyEvent, kpiEntity, account, appIds, null);
 
     }
 }
\ No newline at end of file
diff --git a/src/test/java/com/heal/signal/detector/util/CommonsTest.java b/src/test/java/com/heal/signal/detector/util/CommonsTest.java
new file mode 100644
index 0000000..90d87dd
--- /dev/null
+++ b/src/test/java/com/heal/signal/detector/util/CommonsTest.java
@@ -0,0 +1,473 @@
+package com.heal.signal.detector.util;
+
+import com.heal.configuration.enums.SignalType;
+import com.heal.configuration.pojos.BasicEntity;
+import com.heal.configuration.pojos.MaintenanceDetails;
+import com.heal.configuration.pojos.Service;
+import com.heal.configuration.pojos.opensearch.SignalDetails;
+import com.heal.signal.detector.TestMain;
+import com.heal.signal.detector.cache.CacheWrapper;
+import com.heal.signal.detector.pojos.SignalStatus;
+import org.junit.jupiter.api.Nested;
+import org.junit.jupiter.api.Test;
+import org.mockito.InjectMocks;
+import org.mockito.Mock;
+import org.mockito.Mockito;
+
+import java.sql.Timestamp;
+import java.util.*;
+import java.util.concurrent.TimeUnit;
+
+/**
+ * <AUTHOR>
+ */
+public class CommonsTest extends TestMain {
+
+    @InjectMocks
+    Commons commons;
+    @Mock
+    CacheWrapper wrapper;
+    @Mock
+    RedisUtilities redisUtilities;
+    @Mock
+    HealthMetrics metrics;
+
+    private String accountIdentifier = "sample_account_identifier";
+    private String applicationIdentifier = "sample_application_identifier";
+    private String serviceIdentifier = "sample_service_identifier";
+    private String instanceIdentifier = "sample_instance_identifier";
+    private String incidentIdentifier = "E" + "sample_anomaly_identifier";
+    private String eventIdentifier = "sample_anomaly_identifier";
+
+    @Nested
+    class ProcessAndGetOpenSignals {
+        @Test
+        public void inputServiceListEmpty() throws Exception {
+            Set<SignalDetails> signalDetailsSet = commons.processAndGetOpenSignals(
+                    new HashSet<>(), accountIdentifier, eventIdentifier,
+                    new HashMap<>(), new HashMap<>(), new HashSet<>(), true, new HashSet<>());
+            assert signalDetailsSet.isEmpty();
+        }
+
+        @Test
+        public void getNeighbours_method_throws_exception() throws Exception {
+            Service service = Service.builder().id(1).name(serviceIdentifier).identifier(serviceIdentifier).build();
+
+            Mockito.when(wrapper.getNeighbours(accountIdentifier, service.getIdentifier()))
+                    .thenThrow(new RuntimeException("Random Exception"));
+
+            Set<SignalDetails> signalDetailsSet = commons.processAndGetOpenSignals(Set.of(service), accountIdentifier,
+                    eventIdentifier, new HashMap<>(), new HashMap<>(), new HashSet<>(), true, new HashSet<>());
+            assert signalDetailsSet.isEmpty();
+        }
+
+        @Nested
+        class ProcessAndGetOpenSignals_noOpenSignalsFromOS_noOpenSignalsInRedis {
+            @Test
+            public void noNeighboursPresent_noCommonSvcPresent() throws Exception {
+                commons.includeOutbounds = 1;
+                Service service = Service.builder().id(1).name(serviceIdentifier).identifier(serviceIdentifier).build();
+
+                Mockito.when(wrapper.getNeighbours(accountIdentifier, service.getIdentifier()))
+                        .thenReturn(new HashSet<>());
+
+                Set<SignalDetails> signalDetailsSet = commons.processAndGetOpenSignals(Set.of(service), accountIdentifier,
+                        eventIdentifier, new HashMap<>(), new HashMap<>(), new HashSet<>(), false, new HashSet<>());
+                assert signalDetailsSet.isEmpty();
+            }
+
+            @Test
+            public void neighboursPresentForInputService_noCommonSvcPresent() throws Exception {
+                Service service = Service.builder().id(1).name(serviceIdentifier + 1).identifier(serviceIdentifier + 1).build();
+
+                Mockito.when(wrapper.getNeighbours(accountIdentifier, service.getIdentifier()))
+                        .thenReturn(Set.of(BasicEntity.builder().id(2).name(serviceIdentifier + 2).identifier(serviceIdentifier + 2).build()));
+
+                // New API with app-scoped open signals; return empty for any app
+                Mockito.when(redisUtilities.getOpenSignalsForApp(Mockito.anyString())).thenReturn(new HashMap<>());
+
+                Set<SignalDetails> signalDetailsSet = commons.processAndGetOpenSignals(Set.of(service), accountIdentifier, eventIdentifier,
+                        new HashMap<>(), new HashMap<>(), new HashSet<>(), true, new HashSet<>());
+                assert signalDetailsSet.isEmpty();
+            }
+
+            @Test
+            public void noNeighboursPresentForInputService_includeOutboundsFalse_noCommonSvcPresent() throws Exception {
+                commons.includeOutbounds = 0;
+                Service service = Service.builder().id(1).name(serviceIdentifier + 1).identifier(serviceIdentifier + 1).build();
+
+                Mockito.when(wrapper.getNeighbours(accountIdentifier, service.getIdentifier()))
+                        .thenReturn(new HashSet<>());
+
+                Mockito.when(redisUtilities.getOpenSignalsForApp(Mockito.anyString())).thenReturn(new HashMap<>());
+
+                Set<SignalDetails> signalDetailsSet = commons.processAndGetOpenSignals(Set.of(service), accountIdentifier, eventIdentifier,
+                        new HashMap<>(), new HashMap<>(), new HashSet<>(), true, new HashSet<>());
+                assert signalDetailsSet.isEmpty();
+            }
+
+            @Test
+            public void noNeighboursForInputService_accountOutboundsPresent_noCommonSvcPresent() throws Exception {
+                commons.includeOutbounds = 1;
+                Service service = Service.builder().id(1).name(serviceIdentifier + 1).identifier(serviceIdentifier + 1).build();
+
+                Mockito.when(wrapper.getNeighbours(accountIdentifier, service.getIdentifier())).thenReturn(new HashSet<>());
+
+                Mockito.when(wrapper.getAccountOutbounds(accountIdentifier))
+                        .thenReturn(Map.of(1, List.of(BasicEntity.builder().id(2).name(serviceIdentifier + 2).identifier(serviceIdentifier + 2).build())));
+
+                Mockito.when(redisUtilities.getOpenSignalsForApp(Mockito.anyString())).thenReturn(new HashMap<>());
+
+                Set<SignalDetails> signalDetailsSet = commons.processAndGetOpenSignals(Set.of(service), accountIdentifier, eventIdentifier,
+                        new HashMap<>(), new HashMap<>(), new HashSet<>(), true, new HashSet<>());
+                assert signalDetailsSet.isEmpty();
+            }
+
+            @Test
+            public void noNeighboursForInputService_noAccountOutboundsPresent_CommonSvcPresent() throws Exception {
+                commons.includeOutbounds = 1;
+                Service service = Service.builder().id(1).name(serviceIdentifier + 1).identifier(serviceIdentifier + 1).build();
+
+                Mockito.when(wrapper.getNeighbours(accountIdentifier, service.getIdentifier())).thenReturn(new HashSet<>());
+
+                Mockito.when(wrapper.getAccountOutbounds(accountIdentifier)).thenReturn(new HashMap<>());
+
+                Mockito.when(redisUtilities.getOpenSignalsForApp(Mockito.anyString())).thenReturn(new HashMap<>());
+
+                Map<String, String> dcDrServiceMap = Map.of(serviceIdentifier + 1, serviceIdentifier + 2, serviceIdentifier + 3, serviceIdentifier + 4);
+                Map<String, String> drDcServiceMap = Map.of(serviceIdentifier + 2, serviceIdentifier + 1, serviceIdentifier + 4, serviceIdentifier + 3);
+
+                Set<SignalDetails> signalDetailsSet = commons.processAndGetOpenSignals(Set.of(service), accountIdentifier, eventIdentifier,
+                        dcDrServiceMap, drDcServiceMap, new HashSet<>(), true, new HashSet<>());
+                assert signalDetailsSet.isEmpty();
+        // Update all other test cases for processAndGetOpenSignals to add the new appIds argument as the last parameter.
+        // If you add new tests, ensure to mock redisUtilities.getOpenSignalsForApp(appId) as needed for coverage.
+            }
+
+            @Test
+            public void noNeighboursForInputService_accountOutboundsPresent_CommonSvcPresent() throws Exception {
+                commons.includeOutbounds = 1;
+                Service service = Service.builder().id(1).name(serviceIdentifier + 1).identifier(serviceIdentifier + 1).build();
+
+                Mockito.when(wrapper.getNeighbours(accountIdentifier, service.getIdentifier())).thenReturn(new HashSet<>());
+
+                Mockito.when(wrapper.getAccountOutbounds(accountIdentifier))
+                        .thenReturn(Map.of(1, List.of(BasicEntity.builder().id(2).name(serviceIdentifier + 2).identifier(serviceIdentifier + 2).build())));
+
+                Mockito.when(redisUtilities.getOpenSignalsForApp(Mockito.anyString())).thenReturn(new HashMap<>());
+
+                Map<String, String> dcDrServiceMap = Map.of(serviceIdentifier + 1, serviceIdentifier + 2, serviceIdentifier + 3, serviceIdentifier + 4);
+                Map<String, String> drDcServiceMap = Map.of(serviceIdentifier + 2, serviceIdentifier + 1, serviceIdentifier + 4, serviceIdentifier + 3);
+
+                Set<SignalDetails> signalDetailsSet = commons.processAndGetOpenSignals(Set.of(service), accountIdentifier, eventIdentifier,
+                        dcDrServiceMap, drDcServiceMap, new HashSet<>(), true, new HashSet<>());
+                assert signalDetailsSet.isEmpty();
+            }
+        }
+
+        @Nested
+        class ProcessAndGetOpenSignals_openSignalsFromOSPresent_noOpenSignalsInRedis {
+            @Test
+            public void noNeighboursPresent_noCommonSvcPresent() throws Exception {
+                commons.includeOutbounds = 1;
+                Service service = Service.builder().id(1).name(serviceIdentifier).identifier(serviceIdentifier).build();
+
+                SignalDetails signalDetailsFromOS = SignalDetails.builder()
+                        .signalId(incidentIdentifier + 1)
+                        .serviceIds(Set.of(serviceIdentifier))
+                        .signalType(SignalType.EARLY_WARNING.name())
+                        .currentStatus(SignalStatus.OPEN.name())
+                        .metadata(Map.of("Source", "HEAL"))
+                        .build();
+
+                Mockito.when(wrapper.getNeighbours(accountIdentifier, service.getIdentifier()))
+                        .thenReturn(new HashSet<>());
+
+                Mockito.when(redisUtilities.getOpenSignalsForApp(Mockito.anyString())).thenReturn(new HashMap<>());
+
+                Set<SignalDetails> signalDetailsSet = commons.processAndGetOpenSignals(Set.of(service), accountIdentifier,
+                        eventIdentifier, new HashMap<>(), new HashMap<>(), Set.of(signalDetailsFromOS), false, new HashSet<>());
+                assert !signalDetailsSet.isEmpty();
+                assert signalDetailsSet.size() == 1;
+                assert signalDetailsSet.stream().anyMatch(c -> c.getSignalId().equals(incidentIdentifier + 1));
+            }
+
+            @Test
+            public void neighboursPresentForInputService_noCommonSvcPresent() throws Exception {
+                Service service = Service.builder().id(1).name(serviceIdentifier + 1).identifier(serviceIdentifier + 1).build();
+
+                SignalDetails signalDetailsFromOS = SignalDetails.builder()
+                        .signalId(incidentIdentifier + 1)
+                        .serviceIds(Set.of(serviceIdentifier + 1))
+                        .signalType(SignalType.EARLY_WARNING.name())
+                        .currentStatus(SignalStatus.OPEN.name())
+                        .metadata(Map.of("Source", "HEAL"))
+                        .build();
+
+                Mockito.when(wrapper.getNeighbours(accountIdentifier, service.getIdentifier()))
+                        .thenReturn(Set.of(BasicEntity.builder().id(2).name(serviceIdentifier + 2).identifier(serviceIdentifier + 2).build()));
+
+                Mockito.when(redisUtilities.getOpenSignalsForApp(Mockito.anyString())).thenReturn(new HashMap<>());
+
+                Mockito.when(redisUtilities.getSignalDetails(Mockito.eq(incidentIdentifier + 2)))
+                        .thenReturn(null);
+
+                Set<SignalDetails> signalDetailsSet = commons.processAndGetOpenSignals(Set.of(service), accountIdentifier, eventIdentifier,
+                        new HashMap<>(), new HashMap<>(), Set.of(signalDetailsFromOS), true, new HashSet<>());
+                assert !signalDetailsSet.isEmpty();
+                assert signalDetailsSet.size() == 1;
+                assert signalDetailsSet.stream().anyMatch(c -> c.getSignalId().equals(incidentIdentifier + 1) && c.getServiceIds().contains(serviceIdentifier + 1));
+            }
+
+            @Test
+            public void noNeighboursPresentForInputService_includeOutboundsFalse_noCommonSvcPresent() throws Exception {
+                commons.includeOutbounds = 0;
+                Service service = Service.builder().id(1).name(serviceIdentifier).identifier(serviceIdentifier).build();
+
+                SignalDetails signalDetailsFromOS = SignalDetails.builder()
+                        .signalId(incidentIdentifier + 1)
+                        .serviceIds(Set.of(serviceIdentifier))
+                        .signalType(SignalType.EARLY_WARNING.name())
+                        .currentStatus(SignalStatus.OPEN.name())
+                        .metadata(Map.of("Source", "HEAL"))
+                        .build();
+
+                SignalDetails signalDetailsOfRedisSignalFromOS = SignalDetails.builder()
+                        .signalId(incidentIdentifier + 2)
+                        .serviceIds(Set.of(serviceIdentifier))
+                        .signalType(SignalType.EARLY_WARNING.name())
+                        .currentStatus(SignalStatus.OPEN.name())
+                        .metadata(Map.of("Source", "HEAL"))
+                        .build();
+
+                Mockito.when(wrapper.getNeighbours(accountIdentifier, service.getIdentifier()))
+                        .thenReturn(new HashSet<>());
+
+                Mockito.when(redisUtilities.getOpenSignalsForApp(Mockito.eq(applicationIdentifier))).thenReturn(Map.of(serviceIdentifier, Map.of(incidentIdentifier + 2, 1L)));
+
+                Mockito.when(redisUtilities.getSignalDetails(Mockito.eq(incidentIdentifier + 2)))
+                        .thenReturn(signalDetailsOfRedisSignalFromOS);
+
+                Set<SignalDetails> signalDetailsSet = commons.processAndGetOpenSignals(Set.of(service), accountIdentifier, eventIdentifier,
+                        new HashMap<>(), new HashMap<>(), Set.of(signalDetailsFromOS), true, Set.of(applicationIdentifier));
+                assert !signalDetailsSet.isEmpty();
+                assert signalDetailsSet.size() == 2;
+                assert signalDetailsSet.stream().anyMatch(c -> c.getSignalId().equals(incidentIdentifier + 1));
+                assert signalDetailsSet.stream().anyMatch(c -> c.getSignalId().equals(incidentIdentifier + 2));
+            }
+
+//            @Test
+//            public void noNeighboursForInputService_accountOutboundsPresent_noCommonSvcPresent() throws Exception {
+//                commons.includeOutbounds = 1;
+//                Service service = Service.builder().id(1).name(serviceIdentifier + 1).identifier(serviceIdentifier + 1).build();
+//
+//                SignalDetails signalDetailsFromOS = SignalDetails.builder()
+//                        .signalId(incidentIdentifier + 1)
+//                        .serviceIds(Set.of(serviceIdentifier + 1))
+//                        .signalType(SignalType.EARLY_WARNING.name())
+//                        .currentStatus(SignalStatus.OPEN.name())
+//                        .metadata(Map.of("Source", "HEAL"))
+//                        .build();
+//
+//                SignalDetails signalDetailsOfRedisSignalFromOS = SignalDetails.builder()
+//                        .signalId(incidentIdentifier + 2)
+//                        .serviceIds(Set.of(serviceIdentifier + 2))
+//                        .signalType(SignalType.EARLY_WARNING.name())
+//                        .currentStatus(SignalStatus.OPEN.name())
+//                        .metadata(Map.of("Source", "HEAL"))
+//                        .build();
+//
+//                Mockito.when(wrapper.getNeighbours(accountIdentifier, service.getIdentifier()))
+//                        .thenReturn(new HashSet<>());
+//
+//                Mockito.when(wrapper.getAccountOutbounds(accountIdentifier))
+//                        .thenReturn(Map.of(1, List.of(BasicEntity.builder().id(2).name(serviceIdentifier + 2).identifier(serviceIdentifier + 2).build())));
+//
+//                Mockito.when(redisUtilities.getOpenSignalsInRedis()).thenReturn(Map.of(serviceIdentifier + 2, Map.of(incidentIdentifier + 2, 1L)));
+//
+//                Mockito.when(redisUtilities.getSignalDetails(Mockito.eq(incidentIdentifier + 2)))
+//                        .thenReturn(signalDetailsOfRedisSignalFromOS);
+//
+//                Set<SignalDetails> signalDetailsSet = commons.processAndGetOpenSignals(Set.of(service), accountIdentifier, eventIdentifier,
+//                        new HashMap<>(), new HashMap<>(), Set.of(signalDetailsFromOS), true);
+//
+//                assert !signalDetailsSet.isEmpty();
+//                assert signalDetailsSet.size() == 2;
+//                assert signalDetailsSet.stream().anyMatch(c -> c.getSignalId().equals(incidentIdentifier + 1) && c.getServiceIds().contains(serviceIdentifier + 1));
+//                assert signalDetailsSet.stream().anyMatch(c -> c.getSignalId().equals(incidentIdentifier + 2) && c.getServiceIds().contains(serviceIdentifier + 2));
+//            }
+
+            @Test
+            public void noNeighboursForInputService_noAccountOutboundsPresent_CommonSvcPresent() throws Exception {
+                commons.includeOutbounds = 1;
+                Service service = Service.builder().id(1).name(serviceIdentifier + 1).identifier(serviceIdentifier + 1).build();
+
+                SignalDetails signalDetailsFromOS = SignalDetails.builder()
+                        .signalId(incidentIdentifier + 1)
+                        .serviceIds(Set.of(serviceIdentifier + 1))
+                        .signalType(SignalType.EARLY_WARNING.name())
+                        .currentStatus(SignalStatus.OPEN.name())
+                        .metadata(Map.of("Source", "HEAL"))
+                        .build();
+
+                SignalDetails signalDetailsOfRedisSignalFromOS = SignalDetails.builder()
+                        .signalId(incidentIdentifier + 2)
+                        .serviceIds(Set.of(serviceIdentifier + 2))
+                        .signalType(SignalType.EARLY_WARNING.name())
+                        .currentStatus(SignalStatus.OPEN.name())
+                        .metadata(Map.of("Source", "HEAL"))
+                        .build();
+
+                Mockito.when(wrapper.getNeighbours(accountIdentifier, service.getIdentifier())).thenReturn(new HashSet<>());
+
+                Mockito.when(wrapper.getAccountOutbounds(accountIdentifier)).thenReturn(new HashMap<>());
+
+                Mockito.when(redisUtilities.getOpenSignalsForApp(Mockito.anyString())).thenReturn(Map.of(serviceIdentifier + 2, Map.of(incidentIdentifier + 2, 1L)));
+
+                Mockito.when(redisUtilities.getSignalDetails(Mockito.eq(incidentIdentifier + 2)))
+                        .thenReturn(signalDetailsOfRedisSignalFromOS);
+
+                Map<String, String> dcDrServiceMap = Map.of(serviceIdentifier + 1, serviceIdentifier + 2, serviceIdentifier + 3, serviceIdentifier + 4);
+                Map<String, String> drDcServiceMap = Map.of(serviceIdentifier + 2, serviceIdentifier + 1, serviceIdentifier + 4, serviceIdentifier + 3);
+
+                Set<SignalDetails> signalDetailsSet = commons.processAndGetOpenSignals(Set.of(service), accountIdentifier, eventIdentifier,
+                        dcDrServiceMap, drDcServiceMap, Set.of(signalDetailsFromOS), true, Set.of(applicationIdentifier));
+                assert !signalDetailsSet.isEmpty();
+                assert signalDetailsSet.size() == 2;
+                assert signalDetailsSet.stream().anyMatch(c -> c.getSignalId().equals(incidentIdentifier + 1) && c.getServiceIds().contains(serviceIdentifier + 1));
+                assert signalDetailsSet.stream().anyMatch(c -> c.getSignalId().equals(incidentIdentifier + 2) && c.getServiceIds().contains(serviceIdentifier + 2));
+            }
+
+            @Test
+            public void noNeighboursForInputService_accountOutboundsPresent_CommonSvcPresent() throws Exception {
+                commons.includeOutbounds = 1;
+                Service service = Service.builder().id(1).name(serviceIdentifier + 1).identifier(serviceIdentifier + 1).build();
+
+                SignalDetails signalDetailsFromOS = SignalDetails.builder()
+                        .signalId(incidentIdentifier + 1)
+                        .serviceIds(Set.of(serviceIdentifier + 1))
+                        .signalType(SignalType.EARLY_WARNING.name())
+                        .currentStatus(SignalStatus.OPEN.name())
+                        .metadata(Map.of("Source", "HEAL"))
+                        .build();
+
+                SignalDetails signalDetailsOfRedisSignalFromOS = SignalDetails.builder()
+                        .signalId(incidentIdentifier + 2)
+                        .serviceIds(Set.of(serviceIdentifier + 2))
+                        .signalType(SignalType.EARLY_WARNING.name())
+                        .currentStatus(SignalStatus.OPEN.name())
+                        .metadata(Map.of("Source", "HEAL"))
+                        .build();
+
+                Mockito.when(wrapper.getNeighbours(accountIdentifier, service.getIdentifier())).thenReturn(new HashSet<>());
+
+                Mockito.when(wrapper.getAccountOutbounds(accountIdentifier))
+                        .thenReturn(Map.of(1, List.of(BasicEntity.builder().id(3).name(serviceIdentifier + 3).identifier(serviceIdentifier + 3).build())));
+
+                Mockito.when(redisUtilities.getOpenSignalsForApp(Mockito.anyString())).thenReturn(Map.of(serviceIdentifier + 2, Map.of(incidentIdentifier + 2, 1L)));
+
+                Mockito.when(redisUtilities.getSignalDetails(Mockito.eq(incidentIdentifier + 2)))
+                        .thenReturn(signalDetailsOfRedisSignalFromOS);
+
+                Map<String, String> dcDrServiceMap = Map.of(serviceIdentifier + 1, serviceIdentifier + 2, serviceIdentifier + 3, serviceIdentifier + 4);
+                Map<String, String> drDcServiceMap = Map.of(serviceIdentifier + 2, serviceIdentifier + 1, serviceIdentifier + 4, serviceIdentifier + 3);
+
+                Set<SignalDetails> signalDetailsSet = commons.processAndGetOpenSignals(Set.of(service), accountIdentifier, eventIdentifier,
+                        dcDrServiceMap, drDcServiceMap, Set.of(signalDetailsFromOS), true, Set.of(applicationIdentifier));
+
+                assert !signalDetailsSet.isEmpty();
+                assert signalDetailsSet.size() == 2;
+                assert signalDetailsSet.stream().anyMatch(c -> c.getSignalId().equals(incidentIdentifier + 1) && c.getServiceIds().contains(serviceIdentifier + 1));
+                assert signalDetailsSet.stream().anyMatch(c -> c.getSignalId().equals(incidentIdentifier + 2) && c.getServiceIds().contains(serviceIdentifier + 2));
+            }
+        }
+    }
+
+    @Nested
+    class IsServiceUnderMaintenance {
+        @Test
+        public void inputServiceSetNullOrEmpty() {
+            assert !commons.isServicesUnderMaintenance(Set.of(accountIdentifier), null, incidentIdentifier);
+            assert !commons.isServicesUnderMaintenance(Set.of(accountIdentifier), new HashSet<>(), incidentIdentifier);
+        }
+
+        @Test
+        public void inputAccountSetNullOrEmpty() {
+            assert !commons.isServicesUnderMaintenance(null, Set.of(serviceIdentifier), incidentIdentifier);
+            assert !commons.isServicesUnderMaintenance(new HashSet<>(), Set.of(serviceIdentifier), incidentIdentifier);
+        }
+
+        @Test
+        public void validServiceListSize_notEquals_input_serviceListSize() {
+            Service service = Service.builder().id(1).name(serviceIdentifier).identifier(serviceIdentifier).status(1).build();
+
+            Mockito.when(wrapper.getAccountServices(accountIdentifier)).thenReturn(List.of(service));
+
+            assert !commons.isServicesUnderMaintenance(Set.of(accountIdentifier), Set.of(serviceIdentifier + 1, serviceIdentifier + 2), incidentIdentifier);
+        }
+
+        @Test
+        public void getServiceMaintenanceDetails_returns_null_or_empty() {
+            Service service = Service.builder().id(1).name(serviceIdentifier).identifier(serviceIdentifier).status(1).build();
+
+            Mockito.when(wrapper.getAccountServices(accountIdentifier)).thenReturn(List.of(service));
+
+            Mockito.when(wrapper.getServiceMaintenanceDetails(accountIdentifier, serviceIdentifier)).thenReturn(null);
+            assert !commons.isServicesUnderMaintenance(Set.of(accountIdentifier), Set.of(serviceIdentifier), incidentIdentifier);
+
+            Mockito.when(wrapper.getServiceMaintenanceDetails(accountIdentifier, serviceIdentifier)).thenReturn(new ArrayList<>());
+            assert !commons.isServicesUnderMaintenance(Set.of(accountIdentifier), Set.of(serviceIdentifier), incidentIdentifier);
+        }
+
+        @Test
+        public void isUnderMaintenance_returns_false_for_one_input_service() {
+            commons.offsetFromGMT = 0;
+            Service service1 = Service.builder().id(1).name(serviceIdentifier + 1).identifier(serviceIdentifier + 1).status(1).build();
+            Service service2 = Service.builder().id(2).name(serviceIdentifier + 2).identifier(serviceIdentifier + 2).status(1).build();
+
+            Mockito.when(wrapper.getAccountServices(accountIdentifier + 1)).thenReturn(List.of(service1));
+            Mockito.when(wrapper.getAccountServices(accountIdentifier + 2)).thenReturn(List.of(service2));
+
+            //Under maintenance
+            Mockito.when(wrapper.getServiceMaintenanceDetails(accountIdentifier + 1, serviceIdentifier + 1))
+                    .thenReturn(List.of(MaintenanceDetails.builder()
+                            .startTime(new Timestamp(Calendar.getInstance().getTimeInMillis() - TimeUnit.HOURS.toMillis(10)))
+                            .endTime(new Timestamp(Calendar.getInstance().getTimeInMillis() + TimeUnit.HOURS.toMillis(10)))
+                            .build()));
+            //Not under maintenance
+            Mockito.when(wrapper.getServiceMaintenanceDetails(accountIdentifier + 2, serviceIdentifier + 2))
+                    .thenReturn(List.of(MaintenanceDetails.builder()
+                            .startTime(new Timestamp(Calendar.getInstance().getTimeInMillis() + TimeUnit.HOURS.toMillis(1)))
+                            .endTime(null)
+                            .build()));
+
+            assert !commons.isServicesUnderMaintenance(Set.of(accountIdentifier + 1, accountIdentifier + 2), Set.of(serviceIdentifier + 1, serviceIdentifier + 2), incidentIdentifier);
+        }
+
+        @Test
+        public void isUnderMaintenance_returns_true_for_all_input_services() {
+            commons.offsetFromGMT = 0;
+            Service service1 = Service.builder().id(1).name(serviceIdentifier + 1).identifier(serviceIdentifier + 1).status(1).build();
+            Service service2 = Service.builder().id(2).name(serviceIdentifier + 2).identifier(serviceIdentifier + 2).status(1).build();
+            Service service3 = Service.builder().id(3).name(serviceIdentifier + 3).identifier(serviceIdentifier + 3).status(0).build();
+
+            Mockito.when(wrapper.getAccountServices(accountIdentifier + 1)).thenReturn(List.of(service1));
+            Mockito.when(wrapper.getAccountServices(accountIdentifier + 2)).thenReturn(List.of(service2, service3));
+
+            //Under maintenance
+            Mockito.when(wrapper.getServiceMaintenanceDetails(accountIdentifier + 1, serviceIdentifier + 1))
+                    .thenReturn(List.of(MaintenanceDetails.builder()
+                            .startTime(new Timestamp(Calendar.getInstance().getTimeInMillis() - TimeUnit.HOURS.toMillis(10)))
+                            .endTime(new Timestamp(Calendar.getInstance().getTimeInMillis() + TimeUnit.HOURS.toMillis(10)))
+                            .build()));
+            //Under maintenance
+            Mockito.when(wrapper.getServiceMaintenanceDetails(accountIdentifier + 2, serviceIdentifier + 2))
+                    .thenReturn(List.of(MaintenanceDetails.builder()
+                            .startTime(new Timestamp(Calendar.getInstance().getTimeInMillis() - TimeUnit.HOURS.toMillis(1)))
+                            .endTime(null)
+                            .build()));
+
+            assert commons.isServicesUnderMaintenance(Set.of(accountIdentifier + 1, accountIdentifier + 2), Set.of(serviceIdentifier + 1, serviceIdentifier + 2), incidentIdentifier);
+        }
+    }
+}
diff --git a/templates/conf.properties.tmpl b/templates/conf.properties.tmpl
index 221b115..439bc36 100644
--- a/templates/conf.properties.tmpl
+++ b/templates/conf.properties.tmpl
@@ -49,6 +49,9 @@ thread.pool.max.queue.size={{key "service/signaldetector/threadpoolmaxqueuesize"
 # ==========================================================
 signal.severity.high={{key "service/signaldetector/signalseverityhigh"}}
 signal.severity.low={{key "service/signaldetector/signalseveritylow"}}
+signal.severity.id.high={{key "service/signaldetector/signal/severity/id/high"}}
+signal.severity.id.medium={{key "service/signaldetector/signal/severity/id/medium"}}
+signal.severity.id.low={{key "service/signaldetector/signal/severity/id/low"}}
 
 
 anomaly.delay.minutes={{key "service/signaldetector/anomaly/delay/minutes"}}
@@ -111,5 +114,5 @@ service.neighbours.configuration.cache.max.size={{key "service/signaldetector/se
 heal.types.configuration.cache.expire.interval.minutes={{key "service/signaldetector/heal/types/configurationcache/expire/interval/minutes" }}
 heal.types.configuration.cache.max.size={{key "service/signaldetector/heal/types/configurationcache/max/size" }}
 
-dropped.anomaly.checker.scheduler.seconds={{ "service/signaldetector/dropped/anomaly/checker/scheduler/interval/seconds" }}
-failed.open.signal.queue.max.size={{ "service/signaldetector/failed/open/signal/queue/max/size" }}
\ No newline at end of file
+dropped.anomaly.checker.scheduler.seconds={{key "service/signaldetector/dropped/anomaly/checker/scheduler/interval/seconds" }}
+failed.open.signal.queue.max.size={{key "service/signaldetector/failed/open/signal/queue/max/size" }}
\ No newline at end of file
