job "signal-detector" {
        region = "India"
        datacenters = ["dc1"]

        task "signal-detector" {
                driver = "docker"

                constraint {
                        attribute = "${attr.unique.network.ip-address}"
                        value = "**************"
                }

                artifact {
                        source = "https://perf-113.appnomic:8090/heal-signal-detector_0.0.1.tar"
                }

                template {
                        data = <<EOH
                            UID="{{ key "service/users/userid" }}"
                            GID="{{ key "service/users/groupid" }}"
			                CONSUL_URL="{{ key "service/consul/https/url" }}"
			                JMX_PORT="12005"
                        EOH

                        destination = "local/file.env"
                        env = true
                }

                config {
                        load = "heal-signal-detector_0.0.1.tar"
                        image = "heal-signal-detector:0.0.1"
                        network_mode="bridge"
                        volumes = [
                                "/home/<USER>/appnomic/centeralized_logs/signal-detector:/tmp/logs",
				                "/home/<USER>/appnomic/Appsone_Service/data/cert/cacerts:/usr/local/openjdk-11/lib/security/cacerts"
                        ]

                        extra_hosts = [
"rabbitmq.appnomic:**************",
"cassandra-node2.appnomic:**************",
"haproxy.appnomic:**************",
"notification-processor.appnomic:**************",
"a1eventhandler.appnomic:**************",
"flink-jobmanager-1.appnomic:**************",
"cassandra-node1.appnomic:**************",
"txnbroker.appnomic:**************",
"incident-management-broker.appnomic:**************",
"grafana.appnomic:**************",
"batch-job-prcessor.appnomic:**************",
"keycloak.appnomic:**************",
"mle.appnomic:**************",
"opensearch.appnomic:**************",
"cassandra-node3.appnomic:**************",
"redis-node1.appnomic:**************",
"redis-node2.appnomic:**************",
"redis-node3.appnomic:**************"
                        ]
                        port_map {
				health_port = 8989
			        jvm_port = 12004
                        }
                }

                resources {
                        cpu = 500
                        memory = 2048
                        network {
                                mbits = "10"
				port "health_port" {
                                        static = "8989"
                                }
                                port "jvm_port" {
                                        static = "12004"

                                }
                        }
                }

                service {
                        name = "signal-detector",
                        tags = ["signal-detector"]
                }
        }
}
