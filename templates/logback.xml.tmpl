<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="30 seconds">
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>/tmp/logs/signal-detector.log</file>

        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>/tmp/logs/signal-detector_%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>{{ key "service/signaldetector/logs/maxfilesize" }}</maxFileSize>
            <totalSizeCap>{{ key "service/signaldetector/logs/totalsizecap" }}</totalSizeCap>
            <maxHistory>{{ key "service/signaldetector/logs/maxhistory" }}</maxHistory>
        </rollingPolicy>

        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS, Asia/Kolkata} [%thread] %-5level %logger{36}:%line - %msg%n</pattern>
        </encoder>
    </appender>

    <appender name="CORE_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>/tmp/logs/signal-detector-core.log</file>

        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>/tmp/logs/signal-detector-core_%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>{{ key "service/signaldetector/logs/maxfilesize" }}</maxFileSize>
            <totalSizeCap>{{ key "service/signaldetector/logs/totalsizecap" }}</totalSizeCap>
            <maxHistory>{{ key "service/signaldetector/logs/maxhistory" }}</maxHistory>
        </rollingPolicy>

        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS, Asia/Kolkata} [%thread] %-5level %logger{36}:%line - %msg%n</pattern>
        </encoder>
    </appender>


    <appender name="STATS_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>/tmp/logs/signal-detector-stats.log</file>

        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>/tmp/logs/signal-detector-stats_%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>{{ key "service/signaldetector/auditlogs/maxfilesize" }}</maxFileSize>
            <totalSizeCap>{{ key "service/signaldetector/auditlogs/totalsizecap" }}</totalSizeCap>
            <maxHistory>{{ key "service/signaldetector/auditlogs/maxhistory" }}</maxHistory>
        </rollingPolicy>

        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS, Asia/Kolkata} [%thread] %-5level %logger{36}:%line - %msg%n</pattern>
        </encoder>
    </appender>

    <logger name="com.heal.signal.detector" level="{{ key "service/signaldetector/loglevel" }}" additivity="false">
        <appender-ref ref="FILE"/>
    </logger>


    <logger name="com.heal.signal.detector.scheduler.StatisticsScheduler" level="{{ key "service/signaldetector/statsloglevel" }}" additivity="false">
        <appender-ref ref="STATS_FILE"/>
    </logger>

    <root level = "{{ key "service/signaldetector/rootloglevel" }}">
        <appender-ref ref = "CORE_FILE"/>
    </root>

</configuration>