# Gemini Context: Signal Detector Service

This document provides context for the `signal-detector` service, a component of the HEAL platform responsible for detecting signals in data.

## Service Purpose

This service is responsible for processing incoming data to detect meaningful signals or patterns. It likely consumes data from a message queue, applies some detection logic, and then forwards the detected signals to other services for further processing.

## Tech Stack

- **Language:** Java 17
- **Framework:** Spring Boot 3.4.1
- **Build Tool:** Apache Maven
- **Dependencies:** Spring AMQP (RabbitMQ), Spring Data Redis, OpenSearch, gRPC

## How to Build

Build the service using Maven:

```bash
mvn clean install
```

This will produce a JAR file and a distributable tarball in the `target/` directory.

## How to Run

The service is run via the `entrypoint.sh` script within a Docker container. This script configures the JVM and starts the Spring Boot application.

To run the service locally after building it:

```bash
java -jar target/heal-signal-detector-*.jar
```

## How to Test

Execute the unit tests using <PERSON>ven:

```bash
mvn test
```

## Key Files & Entry Points

- **Main Application:** `src/main/java/com/heal/signal/detector/SignalDetectorApplication.java` is the main entry point for the Spring Boot application.
- **Configuration:** Service configuration is managed by `consul-template` and can be found in the `conf/` and `templates/` directories.
- **Maven Configuration:** `pom.xml` contains the project's dependencies and build configuration.

## Service Dependencies

- **Consul:** For service discovery and configuration.
- **RabbitMQ:** Used as a message broker to receive data.
- **Redis:** Used for caching or state management.
- **OpenSearch:** Used to query data for signal detection.
- **Configuration POJOs:** Depends on the `configuration-pojos` project for shared data structures.
