import com.appnomic.appsone.common.protbuf.AnomalyEventProtos;
import com.rabbitmq.client.*;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class BatchAnomalyEvent {
    private final static String QUEUE_NAME = "anomaly-event-signal-messages";
    private final static AMQP.BasicProperties persistent = new AMQP.BasicProperties.Builder().deliveryMode(2).build();

    public static void main(String[] args) {
        if(args.length < 9) {
            System.err.println("Arguments length:"+args.length);
            System.err.println("Usage: java -cp ./lib/*:. BatchAnomalyEvent <MQHostAddress> <MQPort> <TimeInGMT(2021-02-05 16:30:00)> <KpiId> <kpiValue> <BatchJobId> <GroupName> <AccountId> <AppId> [<key1#@@#value1@##@key2#@@#value2] [<RMQSSLConnection(1/0)>]");
            System.err.println("Example: java -cp ./lib/*:. BatchAnomalyEvent localhost 5672 \"2021-01-15 16:30:00\" 12 1 \"Job1\" \"EOD\" \"123455\" \"appid-123\" \"\" \"true\"");
            System.err.println("Example: java -cp ./lib/*:. BatchAnomalyEvent localhost 5672 \"2021-01-15 16:30:00\" 12 1 \"Job1\" \"EOD\" \"123455\" \"appid-123\" \"key1@##@value1#@@#key2@##@value2\" \"true\"");
            System.exit(-9);
        }
        String hostAddress = null, hostPort = null;
        String norOrSor = "SOR";
        String severity = "Severe";
        String operationType = "not equals";
        String timeInGMT = "",groupName = "", kpiId = "", accountId = "", applicationId = "", kpiValue = "", batchJobId = "", metaDataStr = "";
        int timeZoneInSec = 19800;
        boolean isSSL = true;
        try {
            hostAddress = args[0];
            hostPort = args[1];
            timeInGMT = args[2];
            kpiId = args[3];
            kpiValue = args[4];
            batchJobId = args[5];
            groupName = args[6];
            accountId = args[7];
            applicationId = args[8];
            if(args.length >= 10) {
                metaDataStr = args[9];
            }

            if(args.length == 11) {
                isSSL = Boolean.parseBoolean(args[10]);
            }
            ConnectionFactory factory = new ConnectionFactory();
            if(isSSL) {
                factory.useSslProtocol();
            } else {
                System.out.println("RabbitMQ connection is non-ssl.");
            }

            Map<String, String> reconstructedUtilMap = new HashMap<>();
            if(metaDataStr.trim().length() > 0) {
                reconstructedUtilMap = Arrays.stream(metaDataStr.split("@##@"))
//                    .filter( s -> s.length() > 0)
                        .map(s -> s.split("#@@#"))
//                    .filter(s -> s.length == 2)
                        .collect(Collectors.toMap(s -> s[0], s -> s[1]));
            }

//            factory.setHost(hostAddress);
            factory.setVirtualHost("/");
//            factory.setPort(Integer.parseInt(hostPort));
            factory.setUsername("admin");
            factory.setPassword("Admin@123");

            Address address = new Address(hostAddress, Integer.parseInt(hostPort));
            Connection connection = factory.newConnection(Collections.singletonList(address));
            Channel channel = connection.createChannel();
            channel.queueDeclare(QUEUE_NAME, true, false, false, null);

            Map<String, String> metaDataMap = new HashMap<>(reconstructedUtilMap);
            metaDataMap.put("group_name", groupName);

            Map<String, Double> threasholdMap = new HashMap<>();
            Calendar time = Calendar.getInstance(TimeZone.getTimeZone("UTC"));
            time.set(Calendar.SECOND, 0);
            time.set(Calendar.MILLISECOND, 0);
            long startTime = time.getTimeInMillis();
            long endTime = time.getTimeInMillis();

            String anomalyEventId =  "AE-B-"+ batchJobId + "-" + kpiId + "-" + endTime/1000;
            publishAnomalyData(startTime, endTime, channel, norOrSor, metaDataMap, threasholdMap, batchJobId,
                    kpiId, severity, accountId, applicationId, kpiValue, operationType, anomalyEventId);
            System.out.println("Successfully sent to RabbitMQ for "+hostAddress+":"+hostPort+" server.");
        } catch (Exception e) {
            System.err.println("Not able to push data into '"+QUEUE_NAME+"' queue for '"+hostAddress+":"+hostPort+"' server.");
            log.error("Error occurred while pushing data to {} queue. Details:",QUEUE_NAME, e);
        }
        System.exit(-9);
    }


    private static void publishAnomalyData(long startTimeGMT, long currentTimeGMT , Channel channel, String norOrSor,
                                             Map<String, String> metaDataMap, Map<String, Double> thresholds, String batchJobId, String kpiId,
                                             String severity, String accountId, String applicationId,
                                             String kpiValue, String operation, String anomalyEventId) throws IOException {


        AnomalyEventProtos.AnomalyEvent.Builder anomalyEventBuilder = AnomalyEventProtos.AnomalyEvent.newBuilder();
        anomalyEventBuilder.setAccountId(accountId);
        anomalyEventBuilder.addAllAppId(Collections.singleton(applicationId));
        anomalyEventBuilder.setAnomalyId(anomalyEventId);
        anomalyEventBuilder.setThresholdType(norOrSor);
        anomalyEventBuilder.setOperationType(operation);
        anomalyEventBuilder.setStartTimeGMT(startTimeGMT);
        anomalyEventBuilder.setEndTimeGMT(currentTimeGMT);
        anomalyEventBuilder.setAnomalyTriggerTimeGMT(startTimeGMT);

        AnomalyEventProtos.BatchInfo.Builder builder = AnomalyEventProtos.BatchInfo.newBuilder();
        builder.setBatchJob(batchJobId);
        builder.setKpiId(kpiId);
        builder.putAllThresholds(thresholds);
        builder.setValue(kpiValue);
        builder.setIsWorkload(false);

        if(null != severity) {
            builder.setThresholdSeverity(severity);
        }
        Map<String, String> metaData = new HashMap<>(metaDataMap);
        builder.putAllMetadata(metaData);

        anomalyEventBuilder.setBatchInfo(builder.build());
        AnomalyEventProtos.AnomalyEvent event = anomalyEventBuilder.build();

        System.out.println("Anomaly event: " + anomalyEventBuilder);

        ByteArrayOutputStream os = new ByteArrayOutputStream();
        event.writeTo(os);
        channel.basicPublish("", QUEUE_NAME, persistent, os.toByteArray());

    }
}
