package com.heal.signal.detector;

import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.SpringExtension;

/**
 * <AUTHOR>
 */
@ExtendWith(SpringExtension.class)
@SpringBootTest
@TestPropertySource(properties = {"spring.profiles.active=test"})
public class TestMain {
}
