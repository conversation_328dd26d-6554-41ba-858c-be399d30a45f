package com.heal.signal.detector.util;

import com.appnomic.appsone.common.protbuf.AnomalyEventProtos;
import com.heal.signal.detector.TestMain;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

public class TestUtil extends TestMain {

    public static AnomalyEventProtos.AnomalyEvent buildAnomalyData(long startTimeGMT,
            long currentTimeGMT,
            Map<String, String> metaDataMap,
            Map<String, Double> thresholds,
            String anomalyEventId,
            String accountId,
            String appId,
            String thresholdType,
            String operationType,
            String kpiId,
            String value,
            boolean isWorkload,
            String thresholdSeverity,
            boolean useBatchInfo // true = BatchInfo, false = KpiInfo
    ) {

        AnomalyEventProtos.AnomalyEvent.Builder anomalyEventBuilder = AnomalyEventProtos.AnomalyEvent.newBuilder();
        anomalyEventBuilder.setAccountId(accountId);
        anomalyEventBuilder.addAllAppId(Collections.singleton(appId));
        anomalyEventBuilder.setAnomalyId(anomalyEventId);
        anomalyEventBuilder.setThresholdType(thresholdType);
        anomalyEventBuilder.setOperationType(operationType);
        anomalyEventBuilder.setStartTimeGMT(startTimeGMT);
        anomalyEventBuilder.setEndTimeGMT(currentTimeGMT);
        anomalyEventBuilder.setAnomalyTriggerTimeGMT(startTimeGMT);

        Map<String, String> metaData = new HashMap<>(metaDataMap);

        if (useBatchInfo) {
            AnomalyEventProtos.BatchInfo.Builder batchBuilder = AnomalyEventProtos.BatchInfo.newBuilder();
            batchBuilder.setBatchJob(""); // still hardcoded as no param was provided
            batchBuilder.setKpiId(kpiId);
            batchBuilder.putAllThresholds(thresholds);
            batchBuilder.setValue(value);
            batchBuilder.setIsWorkload(isWorkload);
            batchBuilder.setThresholdSeverity(thresholdSeverity);
            batchBuilder.putAllMetadata(metaData);
            anomalyEventBuilder.setBatchInfo(batchBuilder.build());
        } else {
            AnomalyEventProtos.KpiInfo.Builder kpiBuilder = AnomalyEventProtos.KpiInfo.newBuilder();
            kpiBuilder.setKpiId(kpiId);
            kpiBuilder.putAllThresholds(thresholds);
            kpiBuilder.setValue(value);
            kpiBuilder.setIsWorkload(isWorkload);
            kpiBuilder.setThresholdSeverity(thresholdSeverity);
            kpiBuilder.putAllMetadata(metaData);
            anomalyEventBuilder.setKpis(kpiBuilder.build());
        }

        return anomalyEventBuilder.build();
    }

}
