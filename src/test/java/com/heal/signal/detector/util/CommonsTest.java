package com.heal.signal.detector.util;

import com.heal.configuration.enums.SignalType;
import com.heal.configuration.pojos.BasicEntity;
import com.heal.configuration.pojos.MaintenanceDetails;
import com.heal.configuration.pojos.Service;
import com.heal.configuration.pojos.opensearch.SignalDetails;
import com.heal.signal.detector.TestMain;
import com.heal.signal.detector.cache.CacheWrapper;
import com.heal.signal.detector.pojos.SignalStatus;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

import java.sql.Timestamp;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
public class CommonsTest extends TestMain {

    @InjectMocks
    Commons commons;
    @Mock
    CacheWrapper wrapper;
    @Mock
    RedisUtilities redisUtilities;
    @Mock
    HealthMetrics metrics;

    private String accountIdentifier = "sample_account_identifier";
    private String applicationIdentifier = "sample_application_identifier";
    private String serviceIdentifier = "sample_service_identifier";
    private String instanceIdentifier = "sample_instance_identifier";
    private String incidentIdentifier = "E" + "sample_anomaly_identifier";
    private String eventIdentifier = "sample_anomaly_identifier";

    @Nested
    class ProcessAndGetOpenSignals {
        @Test
        public void inputServiceListEmpty() throws Exception {
            Set<SignalDetails> signalDetailsSet = commons.processAndGetOpenSignals(
                    new HashSet<>(), accountIdentifier, eventIdentifier,
                    new HashMap<>(), new HashMap<>(), new HashSet<>(), true, new HashSet<>());
            assert signalDetailsSet.isEmpty();
        }

        @Test
        public void getNeighbours_method_throws_exception() throws Exception {
            Service service = Service.builder().id(1).name(serviceIdentifier).identifier(serviceIdentifier).build();

            Mockito.when(wrapper.getNeighbours(accountIdentifier, service.getIdentifier()))
                    .thenThrow(new RuntimeException("Random Exception"));

            Set<SignalDetails> signalDetailsSet = commons.processAndGetOpenSignals(Set.of(service), accountIdentifier,
                    eventIdentifier, new HashMap<>(), new HashMap<>(), new HashSet<>(), true, new HashSet<>());
            assert signalDetailsSet.isEmpty();
        }

        @Nested
        class ProcessAndGetOpenSignals_noOpenSignalsFromOS_noOpenSignalsInRedis {
            @Test
            public void noNeighboursPresent_noCommonSvcPresent() throws Exception {
                commons.includeOutbounds = 1;
                Service service = Service.builder().id(1).name(serviceIdentifier).identifier(serviceIdentifier).build();

                Mockito.when(wrapper.getNeighbours(accountIdentifier, service.getIdentifier()))
                        .thenReturn(new HashSet<>());

                Set<SignalDetails> signalDetailsSet = commons.processAndGetOpenSignals(Set.of(service), accountIdentifier,
                        eventIdentifier, new HashMap<>(), new HashMap<>(), new HashSet<>(), false, new HashSet<>());
                assert signalDetailsSet.isEmpty();
            }

            @Test
            public void neighboursPresentForInputService_noCommonSvcPresent() throws Exception {
                Service service = Service.builder().id(1).name(serviceIdentifier + 1).identifier(serviceIdentifier + 1).build();

                Mockito.when(wrapper.getNeighbours(accountIdentifier, service.getIdentifier()))
                        .thenReturn(Set.of(BasicEntity.builder().id(2).name(serviceIdentifier + 2).identifier(serviceIdentifier + 2).build()));

                // New API with app-scoped open signals; return empty for any app
                Mockito.when(redisUtilities.getOpenSignalsForApp(Mockito.anyString())).thenReturn(new HashMap<>());

                Set<SignalDetails> signalDetailsSet = commons.processAndGetOpenSignals(Set.of(service), accountIdentifier, eventIdentifier,
                        new HashMap<>(), new HashMap<>(), new HashSet<>(), true, new HashSet<>());
                assert signalDetailsSet.isEmpty();
            }

            @Test
            public void noNeighboursPresentForInputService_includeOutboundsFalse_noCommonSvcPresent() throws Exception {
                commons.includeOutbounds = 0;
                Service service = Service.builder().id(1).name(serviceIdentifier + 1).identifier(serviceIdentifier + 1).build();

                Mockito.when(wrapper.getNeighbours(accountIdentifier, service.getIdentifier()))
                        .thenReturn(new HashSet<>());

                Mockito.when(redisUtilities.getOpenSignalsForApp(Mockito.anyString())).thenReturn(new HashMap<>());

                Set<SignalDetails> signalDetailsSet = commons.processAndGetOpenSignals(Set.of(service), accountIdentifier, eventIdentifier,
                        new HashMap<>(), new HashMap<>(), new HashSet<>(), true, new HashSet<>());
                assert signalDetailsSet.isEmpty();
            }

            @Test
            public void noNeighboursForInputService_accountOutboundsPresent_noCommonSvcPresent() throws Exception {
                commons.includeOutbounds = 1;
                Service service = Service.builder().id(1).name(serviceIdentifier + 1).identifier(serviceIdentifier + 1).build();

                Mockito.when(wrapper.getNeighbours(accountIdentifier, service.getIdentifier())).thenReturn(new HashSet<>());

                Mockito.when(wrapper.getAccountOutbounds(accountIdentifier))
                        .thenReturn(Map.of(1, List.of(BasicEntity.builder().id(2).name(serviceIdentifier + 2).identifier(serviceIdentifier + 2).build())));

                Mockito.when(redisUtilities.getOpenSignalsForApp(Mockito.anyString())).thenReturn(new HashMap<>());

                Set<SignalDetails> signalDetailsSet = commons.processAndGetOpenSignals(Set.of(service), accountIdentifier, eventIdentifier,
                        new HashMap<>(), new HashMap<>(), new HashSet<>(), true, new HashSet<>());
                assert signalDetailsSet.isEmpty();
            }

            @Test
            public void noNeighboursForInputService_noAccountOutboundsPresent_CommonSvcPresent() throws Exception {
                commons.includeOutbounds = 1;
                Service service = Service.builder().id(1).name(serviceIdentifier + 1).identifier(serviceIdentifier + 1).build();

                Mockito.when(wrapper.getNeighbours(accountIdentifier, service.getIdentifier())).thenReturn(new HashSet<>());

                Mockito.when(wrapper.getAccountOutbounds(accountIdentifier)).thenReturn(new HashMap<>());

                Mockito.when(redisUtilities.getOpenSignalsForApp(Mockito.anyString())).thenReturn(new HashMap<>());

                Map<String, String> dcDrServiceMap = Map.of(serviceIdentifier + 1, serviceIdentifier + 2, serviceIdentifier + 3, serviceIdentifier + 4);
                Map<String, String> drDcServiceMap = Map.of(serviceIdentifier + 2, serviceIdentifier + 1, serviceIdentifier + 4, serviceIdentifier + 3);

                Set<SignalDetails> signalDetailsSet = commons.processAndGetOpenSignals(Set.of(service), accountIdentifier, eventIdentifier,
                        dcDrServiceMap, drDcServiceMap, new HashSet<>(), true, new HashSet<>());
                assert signalDetailsSet.isEmpty();
        // Update all other test cases for processAndGetOpenSignals to add the new appIds argument as the last parameter.
        // If you add new tests, ensure to mock redisUtilities.getOpenSignalsForApp(appId) as needed for coverage.
            }

            @Test
            public void noNeighboursForInputService_accountOutboundsPresent_CommonSvcPresent() throws Exception {
                commons.includeOutbounds = 1;
                Service service = Service.builder().id(1).name(serviceIdentifier + 1).identifier(serviceIdentifier + 1).build();

                Mockito.when(wrapper.getNeighbours(accountIdentifier, service.getIdentifier())).thenReturn(new HashSet<>());

                Mockito.when(wrapper.getAccountOutbounds(accountIdentifier))
                        .thenReturn(Map.of(1, List.of(BasicEntity.builder().id(2).name(serviceIdentifier + 2).identifier(serviceIdentifier + 2).build())));

                Mockito.when(redisUtilities.getOpenSignalsForApp(Mockito.anyString())).thenReturn(new HashMap<>());

                Map<String, String> dcDrServiceMap = Map.of(serviceIdentifier + 1, serviceIdentifier + 2, serviceIdentifier + 3, serviceIdentifier + 4);
                Map<String, String> drDcServiceMap = Map.of(serviceIdentifier + 2, serviceIdentifier + 1, serviceIdentifier + 4, serviceIdentifier + 3);

                Set<SignalDetails> signalDetailsSet = commons.processAndGetOpenSignals(Set.of(service), accountIdentifier, eventIdentifier,
                        dcDrServiceMap, drDcServiceMap, new HashSet<>(), true, new HashSet<>());
                assert signalDetailsSet.isEmpty();
            }
        }

        @Nested
        class ProcessAndGetOpenSignals_openSignalsFromOSPresent_noOpenSignalsInRedis {
            @Test
            public void noNeighboursPresent_noCommonSvcPresent() throws Exception {
                commons.includeOutbounds = 1;
                Service service = Service.builder().id(1).name(serviceIdentifier).identifier(serviceIdentifier).build();

                SignalDetails signalDetailsFromOS = SignalDetails.builder()
                        .signalId(incidentIdentifier + 1)
                        .serviceIds(Set.of(serviceIdentifier))
                        .signalType(SignalType.EARLY_WARNING.name())
                        .currentStatus(SignalStatus.OPEN.name())
                        .metadata(Map.of("Source", "HEAL"))
                        .build();

                Mockito.when(wrapper.getNeighbours(accountIdentifier, service.getIdentifier()))
                        .thenReturn(new HashSet<>());

                Mockito.when(redisUtilities.getOpenSignalsForApp(Mockito.anyString())).thenReturn(new HashMap<>());

                Set<SignalDetails> signalDetailsSet = commons.processAndGetOpenSignals(Set.of(service), accountIdentifier,
                        eventIdentifier, new HashMap<>(), new HashMap<>(), Set.of(signalDetailsFromOS), false, new HashSet<>());
                assert !signalDetailsSet.isEmpty();
                assert signalDetailsSet.size() == 1;
                assert signalDetailsSet.stream().anyMatch(c -> c.getSignalId().equals(incidentIdentifier + 1));
            }

            @Test
            public void neighboursPresentForInputService_noCommonSvcPresent() throws Exception {
                Service service = Service.builder().id(1).name(serviceIdentifier + 1).identifier(serviceIdentifier + 1).build();

                SignalDetails signalDetailsFromOS = SignalDetails.builder()
                        .signalId(incidentIdentifier + 1)
                        .serviceIds(Set.of(serviceIdentifier + 1))
                        .signalType(SignalType.EARLY_WARNING.name())
                        .currentStatus(SignalStatus.OPEN.name())
                        .metadata(Map.of("Source", "HEAL"))
                        .build();

                Mockito.when(wrapper.getNeighbours(accountIdentifier, service.getIdentifier()))
                        .thenReturn(Set.of(BasicEntity.builder().id(2).name(serviceIdentifier + 2).identifier(serviceIdentifier + 2).build()));

                Mockito.when(redisUtilities.getOpenSignalsForApp(Mockito.anyString())).thenReturn(new HashMap<>());

                Mockito.when(redisUtilities.getSignalDetails(Mockito.eq(incidentIdentifier + 2)))
                        .thenReturn(null);

                Set<SignalDetails> signalDetailsSet = commons.processAndGetOpenSignals(Set.of(service), accountIdentifier, eventIdentifier,
                        new HashMap<>(), new HashMap<>(), Set.of(signalDetailsFromOS), true, new HashSet<>());
                assert !signalDetailsSet.isEmpty();
                assert signalDetailsSet.size() == 1;
                assert signalDetailsSet.stream().anyMatch(c -> c.getSignalId().equals(incidentIdentifier + 1) && c.getServiceIds().contains(serviceIdentifier + 1));
            }

            @Test
            public void noNeighboursPresentForInputService_includeOutboundsFalse_noCommonSvcPresent() throws Exception {
                commons.includeOutbounds = 0;
                Service service = Service.builder().id(1).name(serviceIdentifier).identifier(serviceIdentifier).build();

                SignalDetails signalDetailsFromOS = SignalDetails.builder()
                        .signalId(incidentIdentifier + 1)
                        .serviceIds(Set.of(serviceIdentifier))
                        .signalType(SignalType.EARLY_WARNING.name())
                        .currentStatus(SignalStatus.OPEN.name())
                        .metadata(Map.of("Source", "HEAL"))
                        .build();

                SignalDetails signalDetailsOfRedisSignalFromOS = SignalDetails.builder()
                        .signalId(incidentIdentifier + 2)
                        .serviceIds(Set.of(serviceIdentifier))
                        .signalType(SignalType.EARLY_WARNING.name())
                        .currentStatus(SignalStatus.OPEN.name())
                        .metadata(Map.of("Source", "HEAL"))
                        .build();

                Mockito.when(wrapper.getNeighbours(accountIdentifier, service.getIdentifier()))
                        .thenReturn(new HashSet<>());

                Mockito.when(redisUtilities.getOpenSignalsForApp(Mockito.eq(applicationIdentifier))).thenReturn(Map.of(serviceIdentifier, Map.of(incidentIdentifier + 2, 1L)));

                Mockito.when(redisUtilities.getSignalDetails(Mockito.eq(incidentIdentifier + 2)))
                        .thenReturn(signalDetailsOfRedisSignalFromOS);

                Set<SignalDetails> signalDetailsSet = commons.processAndGetOpenSignals(Set.of(service), accountIdentifier, eventIdentifier,
                        new HashMap<>(), new HashMap<>(), Set.of(signalDetailsFromOS), true, Set.of(applicationIdentifier));
                assert !signalDetailsSet.isEmpty();
                assert signalDetailsSet.size() == 2;
                assert signalDetailsSet.stream().anyMatch(c -> c.getSignalId().equals(incidentIdentifier + 1));
                assert signalDetailsSet.stream().anyMatch(c -> c.getSignalId().equals(incidentIdentifier + 2));
            }

//            @Test
//            public void noNeighboursForInputService_accountOutboundsPresent_noCommonSvcPresent() throws Exception {
//                commons.includeOutbounds = 1;
//                Service service = Service.builder().id(1).name(serviceIdentifier + 1).identifier(serviceIdentifier + 1).build();
//
//                SignalDetails signalDetailsFromOS = SignalDetails.builder()
//                        .signalId(incidentIdentifier + 1)
//                        .serviceIds(Set.of(serviceIdentifier + 1))
//                        .signalType(SignalType.EARLY_WARNING.name())
//                        .currentStatus(SignalStatus.OPEN.name())
//                        .metadata(Map.of("Source", "HEAL"))
//                        .build();
//
//                SignalDetails signalDetailsOfRedisSignalFromOS = SignalDetails.builder()
//                        .signalId(incidentIdentifier + 2)
//                        .serviceIds(Set.of(serviceIdentifier + 2))
//                        .signalType(SignalType.EARLY_WARNING.name())
//                        .currentStatus(SignalStatus.OPEN.name())
//                        .metadata(Map.of("Source", "HEAL"))
//                        .build();
//
//                Mockito.when(wrapper.getNeighbours(accountIdentifier, service.getIdentifier()))
//                        .thenReturn(new HashSet<>());
//
//                Mockito.when(wrapper.getAccountOutbounds(accountIdentifier))
//                        .thenReturn(Map.of(1, List.of(BasicEntity.builder().id(2).name(serviceIdentifier + 2).identifier(serviceIdentifier + 2).build())));
//
//                Mockito.when(redisUtilities.getOpenSignalsInRedis()).thenReturn(Map.of(serviceIdentifier + 2, Map.of(incidentIdentifier + 2, 1L)));
//
//                Mockito.when(redisUtilities.getSignalDetails(Mockito.eq(incidentIdentifier + 2)))
//                        .thenReturn(signalDetailsOfRedisSignalFromOS);
//
//                Set<SignalDetails> signalDetailsSet = commons.processAndGetOpenSignals(Set.of(service), accountIdentifier, eventIdentifier,
//                        new HashMap<>(), new HashMap<>(), Set.of(signalDetailsFromOS), true);
//
//                assert !signalDetailsSet.isEmpty();
//                assert signalDetailsSet.size() == 2;
//                assert signalDetailsSet.stream().anyMatch(c -> c.getSignalId().equals(incidentIdentifier + 1) && c.getServiceIds().contains(serviceIdentifier + 1));
//                assert signalDetailsSet.stream().anyMatch(c -> c.getSignalId().equals(incidentIdentifier + 2) && c.getServiceIds().contains(serviceIdentifier + 2));
//            }

            @Test
            public void noNeighboursForInputService_noAccountOutboundsPresent_CommonSvcPresent() throws Exception {
                commons.includeOutbounds = 1;
                Service service = Service.builder().id(1).name(serviceIdentifier + 1).identifier(serviceIdentifier + 1).build();

                SignalDetails signalDetailsFromOS = SignalDetails.builder()
                        .signalId(incidentIdentifier + 1)
                        .serviceIds(Set.of(serviceIdentifier + 1))
                        .signalType(SignalType.EARLY_WARNING.name())
                        .currentStatus(SignalStatus.OPEN.name())
                        .metadata(Map.of("Source", "HEAL"))
                        .build();

                SignalDetails signalDetailsOfRedisSignalFromOS = SignalDetails.builder()
                        .signalId(incidentIdentifier + 2)
                        .serviceIds(Set.of(serviceIdentifier + 2))
                        .signalType(SignalType.EARLY_WARNING.name())
                        .currentStatus(SignalStatus.OPEN.name())
                        .metadata(Map.of("Source", "HEAL"))
                        .build();

                Mockito.when(wrapper.getNeighbours(accountIdentifier, service.getIdentifier())).thenReturn(new HashSet<>());

                Mockito.when(wrapper.getAccountOutbounds(accountIdentifier)).thenReturn(new HashMap<>());

                Mockito.when(redisUtilities.getOpenSignalsForApp(Mockito.anyString())).thenReturn(Map.of(serviceIdentifier + 2, Map.of(incidentIdentifier + 2, 1L)));

                Mockito.when(redisUtilities.getSignalDetails(Mockito.eq(incidentIdentifier + 2)))
                        .thenReturn(signalDetailsOfRedisSignalFromOS);

                Map<String, String> dcDrServiceMap = Map.of(serviceIdentifier + 1, serviceIdentifier + 2, serviceIdentifier + 3, serviceIdentifier + 4);
                Map<String, String> drDcServiceMap = Map.of(serviceIdentifier + 2, serviceIdentifier + 1, serviceIdentifier + 4, serviceIdentifier + 3);

                Set<SignalDetails> signalDetailsSet = commons.processAndGetOpenSignals(Set.of(service), accountIdentifier, eventIdentifier,
                        dcDrServiceMap, drDcServiceMap, Set.of(signalDetailsFromOS), true, Set.of(applicationIdentifier));
                assert !signalDetailsSet.isEmpty();
                assert signalDetailsSet.size() == 2;
                assert signalDetailsSet.stream().anyMatch(c -> c.getSignalId().equals(incidentIdentifier + 1) && c.getServiceIds().contains(serviceIdentifier + 1));
                assert signalDetailsSet.stream().anyMatch(c -> c.getSignalId().equals(incidentIdentifier + 2) && c.getServiceIds().contains(serviceIdentifier + 2));
            }

            @Test
            public void noNeighboursForInputService_accountOutboundsPresent_CommonSvcPresent() throws Exception {
                commons.includeOutbounds = 1;
                Service service = Service.builder().id(1).name(serviceIdentifier + 1).identifier(serviceIdentifier + 1).build();

                SignalDetails signalDetailsFromOS = SignalDetails.builder()
                        .signalId(incidentIdentifier + 1)
                        .serviceIds(Set.of(serviceIdentifier + 1))
                        .signalType(SignalType.EARLY_WARNING.name())
                        .currentStatus(SignalStatus.OPEN.name())
                        .metadata(Map.of("Source", "HEAL"))
                        .build();

                SignalDetails signalDetailsOfRedisSignalFromOS = SignalDetails.builder()
                        .signalId(incidentIdentifier + 2)
                        .serviceIds(Set.of(serviceIdentifier + 2))
                        .signalType(SignalType.EARLY_WARNING.name())
                        .currentStatus(SignalStatus.OPEN.name())
                        .metadata(Map.of("Source", "HEAL"))
                        .build();

                Mockito.when(wrapper.getNeighbours(accountIdentifier, service.getIdentifier())).thenReturn(new HashSet<>());

                Mockito.when(wrapper.getAccountOutbounds(accountIdentifier))
                        .thenReturn(Map.of(1, List.of(BasicEntity.builder().id(3).name(serviceIdentifier + 3).identifier(serviceIdentifier + 3).build())));

                Mockito.when(redisUtilities.getOpenSignalsForApp(Mockito.anyString())).thenReturn(Map.of(serviceIdentifier + 2, Map.of(incidentIdentifier + 2, 1L)));

                Mockito.when(redisUtilities.getSignalDetails(Mockito.eq(incidentIdentifier + 2)))
                        .thenReturn(signalDetailsOfRedisSignalFromOS);

                Map<String, String> dcDrServiceMap = Map.of(serviceIdentifier + 1, serviceIdentifier + 2, serviceIdentifier + 3, serviceIdentifier + 4);
                Map<String, String> drDcServiceMap = Map.of(serviceIdentifier + 2, serviceIdentifier + 1, serviceIdentifier + 4, serviceIdentifier + 3);

                Set<SignalDetails> signalDetailsSet = commons.processAndGetOpenSignals(Set.of(service), accountIdentifier, eventIdentifier,
                        dcDrServiceMap, drDcServiceMap, Set.of(signalDetailsFromOS), true, Set.of(applicationIdentifier));

                assert !signalDetailsSet.isEmpty();
                assert signalDetailsSet.size() == 2;
                assert signalDetailsSet.stream().anyMatch(c -> c.getSignalId().equals(incidentIdentifier + 1) && c.getServiceIds().contains(serviceIdentifier + 1));
                assert signalDetailsSet.stream().anyMatch(c -> c.getSignalId().equals(incidentIdentifier + 2) && c.getServiceIds().contains(serviceIdentifier + 2));
            }
        }
    }

    @Nested
    class IsServiceUnderMaintenance {
        @Test
        public void inputServiceSetNullOrEmpty() {
            assert !commons.isServicesUnderMaintenance(Set.of(accountIdentifier), null, incidentIdentifier);
            assert !commons.isServicesUnderMaintenance(Set.of(accountIdentifier), new HashSet<>(), incidentIdentifier);
        }

        @Test
        public void inputAccountSetNullOrEmpty() {
            assert !commons.isServicesUnderMaintenance(null, Set.of(serviceIdentifier), incidentIdentifier);
            assert !commons.isServicesUnderMaintenance(new HashSet<>(), Set.of(serviceIdentifier), incidentIdentifier);
        }

        @Test
        public void validServiceListSize_notEquals_input_serviceListSize() {
            Service service = Service.builder().id(1).name(serviceIdentifier).identifier(serviceIdentifier).status(1).build();

            Mockito.when(wrapper.getAccountServices(accountIdentifier)).thenReturn(List.of(service));

            assert !commons.isServicesUnderMaintenance(Set.of(accountIdentifier), Set.of(serviceIdentifier + 1, serviceIdentifier + 2), incidentIdentifier);
        }

        @Test
        public void getServiceMaintenanceDetails_returns_null_or_empty() {
            Service service = Service.builder().id(1).name(serviceIdentifier).identifier(serviceIdentifier).status(1).build();

            Mockito.when(wrapper.getAccountServices(accountIdentifier)).thenReturn(List.of(service));

            Mockito.when(wrapper.getServiceMaintenanceDetails(accountIdentifier, serviceIdentifier)).thenReturn(null);
            assert !commons.isServicesUnderMaintenance(Set.of(accountIdentifier), Set.of(serviceIdentifier), incidentIdentifier);

            Mockito.when(wrapper.getServiceMaintenanceDetails(accountIdentifier, serviceIdentifier)).thenReturn(new ArrayList<>());
            assert !commons.isServicesUnderMaintenance(Set.of(accountIdentifier), Set.of(serviceIdentifier), incidentIdentifier);
        }

        @Test
        public void isUnderMaintenance_returns_false_for_one_input_service() {
            commons.offsetFromGMT = 0;
            Service service1 = Service.builder().id(1).name(serviceIdentifier + 1).identifier(serviceIdentifier + 1).status(1).build();
            Service service2 = Service.builder().id(2).name(serviceIdentifier + 2).identifier(serviceIdentifier + 2).status(1).build();

            Mockito.when(wrapper.getAccountServices(accountIdentifier + 1)).thenReturn(List.of(service1));
            Mockito.when(wrapper.getAccountServices(accountIdentifier + 2)).thenReturn(List.of(service2));

            //Under maintenance
            Mockito.when(wrapper.getServiceMaintenanceDetails(accountIdentifier + 1, serviceIdentifier + 1))
                    .thenReturn(List.of(MaintenanceDetails.builder()
                            .startTime(new Timestamp(Calendar.getInstance().getTimeInMillis() - TimeUnit.HOURS.toMillis(10)))
                            .endTime(new Timestamp(Calendar.getInstance().getTimeInMillis() + TimeUnit.HOURS.toMillis(10)))
                            .build()));
            //Not under maintenance
            Mockito.when(wrapper.getServiceMaintenanceDetails(accountIdentifier + 2, serviceIdentifier + 2))
                    .thenReturn(List.of(MaintenanceDetails.builder()
                            .startTime(new Timestamp(Calendar.getInstance().getTimeInMillis() + TimeUnit.HOURS.toMillis(1)))
                            .endTime(null)
                            .build()));

            assert !commons.isServicesUnderMaintenance(Set.of(accountIdentifier + 1, accountIdentifier + 2), Set.of(serviceIdentifier + 1, serviceIdentifier + 2), incidentIdentifier);
        }

        @Test
        public void isUnderMaintenance_returns_true_for_all_input_services() {
            commons.offsetFromGMT = 0;
            Service service1 = Service.builder().id(1).name(serviceIdentifier + 1).identifier(serviceIdentifier + 1).status(1).build();
            Service service2 = Service.builder().id(2).name(serviceIdentifier + 2).identifier(serviceIdentifier + 2).status(1).build();
            Service service3 = Service.builder().id(3).name(serviceIdentifier + 3).identifier(serviceIdentifier + 3).status(0).build();

            Mockito.when(wrapper.getAccountServices(accountIdentifier + 1)).thenReturn(List.of(service1));
            Mockito.when(wrapper.getAccountServices(accountIdentifier + 2)).thenReturn(List.of(service2, service3));

            //Under maintenance
            Mockito.when(wrapper.getServiceMaintenanceDetails(accountIdentifier + 1, serviceIdentifier + 1))
                    .thenReturn(List.of(MaintenanceDetails.builder()
                            .startTime(new Timestamp(Calendar.getInstance().getTimeInMillis() - TimeUnit.HOURS.toMillis(10)))
                            .endTime(new Timestamp(Calendar.getInstance().getTimeInMillis() + TimeUnit.HOURS.toMillis(10)))
                            .build()));
            //Under maintenance
            Mockito.when(wrapper.getServiceMaintenanceDetails(accountIdentifier + 2, serviceIdentifier + 2))
                    .thenReturn(List.of(MaintenanceDetails.builder()
                            .startTime(new Timestamp(Calendar.getInstance().getTimeInMillis() - TimeUnit.HOURS.toMillis(1)))
                            .endTime(null)
                            .build()));

            assert commons.isServicesUnderMaintenance(Set.of(accountIdentifier + 1, accountIdentifier + 2), Set.of(serviceIdentifier + 1, serviceIdentifier + 2), incidentIdentifier);
        }
    }
}
