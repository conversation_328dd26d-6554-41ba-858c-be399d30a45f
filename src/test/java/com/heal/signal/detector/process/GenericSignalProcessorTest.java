package com.heal.signal.detector.process;

import com.appnomic.appsone.common.protbuf.AnomalyEventProtos;
import com.heal.configuration.enums.SignalType;
import com.heal.configuration.pojos.Account;
import com.heal.configuration.pojos.AnomalySummary;
import com.heal.configuration.pojos.BasicKpiEntity;
import com.heal.configuration.pojos.KpiCategoryDetails;
import com.heal.configuration.pojos.opensearch.SignalDetails;
import com.heal.signal.detector.TestMain;
import com.heal.signal.detector.cache.CacheWrapper;
import com.heal.signal.detector.opensearch.SignalRepo;
import com.heal.signal.detector.pojos.FailedOpenSignalPojo;
import com.heal.signal.detector.service.ForwarderToQueue;
import com.heal.signal.detector.util.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

class GenericSignalProcessorTest extends TestMain {

    @InjectMocks
    GenericSignalProcessor genericSignalProcessor;

    @Mock
    RedisUtilities redisUtilities;

    @Mock
    Commons commons;

    @Mock
    HealthMetrics metrics;

    @Mock
    SignalRepo signalRepo;

    @Mock
    ForwarderToQueue forwarder;

    @Mock
    CacheWrapper wrapper;

    @Mock
    LocalQueues localQueues;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void processSignalEvent_when_getOpenSignals_throws_should_enqueue_failedOpenSignalAndReturnNull() throws Exception {
        String accountId = "acct1";
        String appId = "app1";
        String kpiId = "101";
        Map<String, String> meta = new HashMap<>();
        AnomalyEventProtos.AnomalyEvent anomalyEvent = TestUtil.buildAnomalyData(System.currentTimeMillis(), System.currentTimeMillis(), meta,
                new HashMap<>(), "AE-G-1", accountId, appId, "T", "OP", kpiId, "10", false, "Low", false);

        Account account = Account.builder().identifier(accountId).id(10).build();

        when(signalRepo.getOpenSignals(eq(accountId), eq(true), eq(true))).thenThrow(new RuntimeException("OS down"));

        String result = genericSignalProcessor.processSignalEvent(SignalType.EARLY_WARNING, anomalyEvent, BasicKpiEntity.builder().identifier(kpiId).build(), account, new HashSet<>(anomalyEvent.getAppIdList()), null);

        assertNull(result);
        verify(localQueues, times(1)).addToFailedOpenSignalQueue(argThat((FailedOpenSignalPojo pojo) ->
                pojo.getSignalType() == SignalType.EARLY_WARNING && pojo.getAnomalyEvent() == anomalyEvent && pojo.getAccount() == account
        ));
    }

    @Test
    void processSignalEvent_create_new_signal_success_path_returnsSignalId_and_sendsMessage() throws Exception {
        String accountId = "acct2";
        String appId = "app2";
        String kpiId = "202";
        Map<String, String> meta = new HashMap<>();
        AnomalyEventProtos.AnomalyEvent anomalyEvent = TestUtil.buildAnomalyData(System.currentTimeMillis(), System.currentTimeMillis(), meta,
                new HashMap<>(), "AE-G-2", accountId, appId, "T", "OP", kpiId, "55", false, "431", false);

        Account account = Account.builder().identifier(accountId).id(20).build();

        // Mock kpiEntity with non-null categoryDetails and groupIdentifier to avoid NPE inside processSignalEvent
        BasicKpiEntity kpiEntity = mock(BasicKpiEntity.class);
        when(kpiEntity.getIdentifier()).thenReturn(kpiId);
        KpiCategoryDetails categoryDetails = mock(KpiCategoryDetails.class);
        when(categoryDetails.getIdentifier()).thenReturn("cat-1");
        when(kpiEntity.getCategoryDetails()).thenReturn(categoryDetails);
        when(kpiEntity.getGroupIdentifier()).thenReturn("grp-1");

        when(signalRepo.getOpenSignals(eq(accountId), eq(true), eq(true))).thenReturn(new HashSet<>());
        when(commons.getSignalMetaData(eq(anomalyEvent), eq(SignalType.EARLY_WARNING))).thenReturn(new HashMap<>());
        // Ensure processAndGetOpenSignals returns empty set (mock default would be null and cause NPE)
        when(commons.processAndGetOpenSignals(anySet(), anyString(), anyString(), anyMap(), anyMap(), anySet(), anyBoolean(), anySet())).thenReturn(new HashSet<>());
        // Avoid null from redis utilities
        when(redisUtilities.getServiceAliases()).thenReturn(new HashSet<>());

        String expectedSignalId = "E-20-202-999-" + (anomalyEvent.getEndTimeGMT() / 1000);
        when(commons.createSignalId(anyString(), anyInt(), anyInt(), anyInt(), anyLong())).thenReturn(expectedSignalId);

        SignalDetails createdSignal = SignalDetails.builder().signalId(expectedSignalId).currentStatus("OPEN").serviceIds(new HashSet<>()).anomalies(new HashSet<>(Collections.singleton(anomalyEvent.getAnomalyId()))).metadata(new HashMap<>()).build();
        when(commons.createSignal(eq(expectedSignalId), anyLong(), anyLong(), anyLong(), anyString(), anySet(), eq(SignalType.EARLY_WARNING), anySet(), anySet(), anySet(), any(), any(), anyInt(), anyMap(), eq(anomalyEvent.getAccountId()))).thenReturn(createdSignal);

        AnomalySummary mockAnomalySummary = mock(AnomalySummary.class);
        when(commons.getAnomalySummary(
                anyString(), anyString(), anyString(), anySet(), eq(SignalType.EARLY_WARNING), anyInt(), anyLong(), anyBoolean(),
                anyString(), anyString(), anyString(), anyString(), anyString(), anyMap(), anyString(), anyMap(), anyString()
        )).thenReturn(mockAnomalySummary);

        when(signalRepo.insertSignal(eq(createdSignal), eq(anomalyEvent.getAccountId()), any(AnomalySummary.class), eq(true), anySet(), eq(false), anyInt())).thenReturn(true);
        when(signalRepo.updateAnomaly(eq(anomalyEvent.getAnomalyId()), eq(anomalyEvent.getEndTimeGMT()), anySet(), eq(anomalyEvent.getAccountId()))).thenReturn(true);

        when(commons.getSignalProto(eq(createdSignal), anyBoolean(), anyBoolean(), anyBoolean(), anyBoolean(), eq(mockAnomalySummary))).thenReturn(com.appnomic.appsone.common.protbuf.SignalProtos.SignalDetails.newBuilder().setSignalId(expectedSignalId).build());

        String result = genericSignalProcessor.processSignalEvent(SignalType.EARLY_WARNING, anomalyEvent, kpiEntity, account, new HashSet<>(anomalyEvent.getAppIdList()), null);

        assertEquals(expectedSignalId, result);
        verify(signalRepo, times(1)).insertSignal(eq(createdSignal), eq(anomalyEvent.getAccountId()), eq(mockAnomalySummary), eq(true), anySet(), eq(false), anyInt());
        verify(forwarder, times(1)).sendSignalMessages(any());
        verify(metrics, times(1)).updateSignalOpenCount(1);
    }
}
