package com.heal.signal.detector.process;

import com.appnomic.appsone.common.protbuf.AnomalyEventProtos;
import com.appnomic.appsone.common.protbuf.SignalProtos;
import com.heal.configuration.enums.SignalType;
import com.heal.configuration.pojos.Account;
import com.heal.configuration.pojos.BasicKpiEntity;
import com.heal.configuration.pojos.CompInstKpiEntity;
import com.heal.configuration.pojos.opensearch.Anomalies;
import com.heal.configuration.pojos.opensearch.SignalDetails;
import com.heal.signal.detector.TestMain;
import com.heal.signal.detector.opensearch.SignalRepo;
import com.heal.signal.detector.service.ForwarderToQueue;
import com.heal.signal.detector.util.Commons;
import com.heal.signal.detector.util.HealthMetrics;
import com.heal.signal.detector.util.RedisUtilities;
import com.heal.signal.detector.util.TestUtil;
import com.heal.signal.detector.pojos.SignalStatus;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.*;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

class SignalHandlerTest extends TestMain {

    @InjectMocks
    SignalHandler signalHandler;

    @Mock
    Commons commons;

    @Mock
    GenericSignalProcessor genericSignalProcessor;

    @Mock
    SignalRepo signalRepo;

    @Mock
    RedisUtilities redisUtilities;

    @Mock
    ForwarderToQueue forwarder;

    @Mock
    HealthMetrics metrics;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void handleAnomalyEvent_open_workload_with_txnKpi_should_call_problem_processor() {
        String accountId = "acc1";
        String appId = "app1";
        String kpiId = "100";
        Map<String, String> meta = new HashMap<>();
        AnomalyEventProtos.AnomalyEvent anomalyEvent = TestUtil.buildAnomalyData(System.currentTimeMillis(), System.currentTimeMillis(), meta,
                new HashMap<>(), "AE-1", accountId, appId, "T", "OP", kpiId, "10", true, "High", false);

        BasicKpiEntity txnKpi = BasicKpiEntity.builder().identifier(kpiId).build();
        CompInstKpiEntity kpiEntity = null;
        Account account = Account.builder().identifier(accountId).id(1).build();
        Anomalies anomaly = mock(Anomalies.class);
        when(anomaly.getSignalIds()).thenReturn(Collections.emptySet());

        Set<String> appIds = new HashSet<>(anomalyEvent.getAppIdList());

        signalHandler.handleAnomalyEvent(anomalyEvent, "OPEN", true, null, accountId, anomalyEvent.getAnomalyId(), 431, meta, txnKpi, kpiEntity, account, anomaly, appIds);

        verify(genericSignalProcessor, times(1)).processSignalEvent(eq(SignalType.PROBLEM), eq(anomalyEvent), eq(txnKpi), eq(account), eq(appIds), isNull());
    }

    @Test
    void handleAnomalyEvent_open_with_txnKpi_non_workload_should_call_early_warning_and_return() {
        String accountId = "acc2";
        String appId = "app2";
        String kpiId = "200";
        Map<String, String> meta = new HashMap<>();
        AnomalyEventProtos.AnomalyEvent anomalyEvent = TestUtil.buildAnomalyData(System.currentTimeMillis(), System.currentTimeMillis(), meta,
                new HashMap<>(), "AE-2", accountId, appId, "T", "OP", kpiId, "20", false, "Medium", false);

        BasicKpiEntity txnKpi = BasicKpiEntity.builder().identifier(kpiId).build();
        CompInstKpiEntity kpiEntity = null;
        Account account = Account.builder().identifier(accountId).id(2).build();
        Anomalies anomaly = mock(Anomalies.class);
        when(anomaly.getSignalIds()).thenReturn(Collections.emptySet());

        Set<String> appIds = new HashSet<>(anomalyEvent.getAppIdList());

        signalHandler.handleAnomalyEvent(anomalyEvent, "OPEN", false, null, accountId, anomalyEvent.getAnomalyId(), 431, meta, txnKpi, kpiEntity, account, anomaly, appIds);

        verify(genericSignalProcessor, times(1)).processSignalEvent(eq(SignalType.EARLY_WARNING), eq(anomalyEvent), eq(txnKpi), eq(account), eq(appIds), isNull());
    }

    @Test
    void handleAnomalyEvent_open_without_txnKpi_should_call_early_warning_with_kpiEntity() {
        String accountId = "acc3";
        String appId = "app3";
        String kpiId = "300";
        Map<String, String> meta = new HashMap<>();
        AnomalyEventProtos.AnomalyEvent anomalyEvent = TestUtil.buildAnomalyData(System.currentTimeMillis(), System.currentTimeMillis(), meta,
                new HashMap<>(), "AE-3", accountId, appId, "T", "OP", kpiId, "30", false, "Low", false);

        BasicKpiEntity txnKpi = null;
        CompInstKpiEntity kpiEntity = CompInstKpiEntity.builder().identifier(kpiId).build();
        Account account = Account.builder().identifier(accountId).id(3).build();
        Anomalies anomaly = mock(Anomalies.class);
        when(anomaly.getSignalIds()).thenReturn(Collections.emptySet());

        Set<String> appIds = new HashSet<>(anomalyEvent.getAppIdList());

        signalHandler.handleAnomalyEvent(anomalyEvent, "OPEN", false, null, accountId, anomalyEvent.getAnomalyId(), 431, meta, txnKpi, kpiEntity, account, anomaly, appIds);

        verify(genericSignalProcessor, times(1)).processSignalEvent(eq(SignalType.EARLY_WARNING), eq(anomalyEvent), eq(kpiEntity), eq(account), eq(appIds), isNull());
    }

    @Test
    void handleAnomalyEvent_close_should_close_signal_and_send_message() {
        String accountId = "acc4";
        String appId = "app4";
        String kpiId = "400";
        String anomalyId = "ANOM-4";

        Map<String, String> meta = new HashMap<>();
        AnomalyEventProtos.AnomalyEvent anomalyEvent = TestUtil.buildAnomalyData(System.currentTimeMillis(), System.currentTimeMillis(), meta,
                new HashMap<>(), anomalyId, accountId, appId, "T", "OP", kpiId, "40", false, "Low", false);

        BasicKpiEntity txnKpi = null;
        CompInstKpiEntity kpiEntity = CompInstKpiEntity.builder().identifier(kpiId).build();
        Account account = Account.builder().identifier(accountId).id(4).build();

        // Prepare anomaly with one signal id
        Anomalies anomaly = mock(Anomalies.class);
        Set<String> signalIds = new HashSet<>();
        signalIds.add("E-1-1-1-1");
        when(anomaly.getSignalIds()).thenReturn(signalIds);

        // Prepare SignalDetails returned by repo
        SignalDetails signal = SignalDetails.builder()
                .signalId("E-1-1-1-1")
                .currentStatus(SignalStatus.OPEN.name())
                .anomalies(new HashSet<>(Collections.singletonList(anomalyId)))
                .metadata(new HashMap<>())
                .serviceIds(new HashSet<>(Collections.singletonList("svc1")))
                .relatedSignals(new HashSet<>())
                .startedTime(System.currentTimeMillis())
                .build();

        when(signalRepo.getSignalById(signalIds, accountId)).thenReturn(Collections.singletonList(signal));
        when(signalRepo.closeSignal(any(SignalDetails.class), eq(accountId), eq(appId))).thenReturn(true);
        when(signalRepo.removeAnomalyFromSignal(eq(anomalyId), eq(signal.getSignalId()), eq(accountId), anyLong(), eq(signal.getCurrentStatus()), anySet(), eq(true), anySet())).thenReturn(true);

        // commons.getSignalProto used in closeSignal -> return a simple proto
        when(commons.getSignalProto(any(SignalDetails.class), anyBoolean(), anyBoolean(), anyBoolean(), anyBoolean(), any())).thenReturn(SignalProtos.SignalDetails.newBuilder().build());

        // redisUtilities.getSignalSummary may be used in path; return null so that branch won't fail
        when(redisUtilities.getSignalSummary(anyString())).thenReturn(null);

        Set<String> appIds = new HashSet<>(anomalyEvent.getAppIdList());

        signalHandler.handleAnomalyEvent(anomalyEvent, "CLOSE", false, null, accountId, anomalyId, 431, meta, txnKpi, kpiEntity, account, anomaly, appIds);

        verify(signalRepo, times(1)).closeSignal(any(SignalDetails.class), eq(accountId), eq(appId));
        verify(signalRepo, times(1)).removeAnomalyFromSignal(eq(anomalyId), eq(signal.getSignalId()), eq(accountId), anyLong(), eq(signal.getCurrentStatus()), anySet(), eq(true), anySet());
        verify(forwarder, atLeastOnce()).sendSignalMessages(any());
    }

    @Test
    void closeSignal_successful_updates_metadata_and_sends_messages() {
        String accountId = "acc_close1";
        Set<String> appIds = new HashSet<>();
        appIds.add("appA");
        appIds.add("appB");

        SignalDetails signal = SignalDetails.builder()
                .signalId("S-1")
                .currentStatus(SignalStatus.OPEN.name())
                .metadata(new HashMap<>())
                .startedTime(System.currentTimeMillis())
                .build();

        when(signalRepo.closeSignal(any(SignalDetails.class), eq(accountId), anyString())).thenReturn(true);
        when(commons.getSignalProto(any(SignalDetails.class), anyBoolean(), anyBoolean(), anyBoolean(), anyBoolean(), any())).thenReturn(SignalProtos.SignalDetails.newBuilder().setSignalId("S-1").build());

        signalHandler.closeSignal(signal, accountId, appIds, "closing for test", null);

        // status and metadata updated on the same object
        assert signal.getCurrentStatus().equals(SignalStatus.CLOSED.name());
        assert signal.getMetadata().get("closing_reason").equals("closing for test");
        assert signal.getMetadata().get("end_time") != null && !signal.getMetadata().get("end_time").isEmpty();

        // repo called for each appId
        verify(signalRepo, times(appIds.size())).closeSignal(any(SignalDetails.class), eq(accountId), anyString());
        // forwarder should have been invoked for each successful close
        verify(forwarder, times(appIds.size())).sendSignalMessages(any());
        // metrics updated for each appId
        verify(metrics, times(appIds.size())).updateSignalCloseCount();
    }

    @Test
    void closeSignal_when_repo_fails_does_not_send_message_but_updates_metrics() {
        String accountId = "acc_close2";
        Set<String> appIds = new HashSet<>();
        appIds.add("appX");

        SignalDetails signal = SignalDetails.builder()
                .signalId("S-2")
                .currentStatus(SignalStatus.OPEN.name())
                .metadata(new HashMap<>())
                .startedTime(System.currentTimeMillis())
                .build();

        when(signalRepo.closeSignal(any(SignalDetails.class), eq(accountId), anyString())).thenReturn(false);

        signalHandler.closeSignal(signal, accountId, appIds, "closing fail test", null);

        // status still set to CLOSED in object before repo update
        assert signal.getCurrentStatus().equals(SignalStatus.CLOSED.name());
        assert signal.getMetadata().get("closing_reason").equals("closing fail test");
        assert signal.getMetadata().get("end_time") != null && !signal.getMetadata().get("end_time").isEmpty();

        // repo called once
        verify(signalRepo, times(appIds.size())).closeSignal(any(SignalDetails.class), eq(accountId), anyString());
        // forwarder should NOT be invoked because repo returned false
        verify(forwarder, never()).sendSignalMessages(any());
        // metrics updated even if repo fails (method updates before checking isUpdated)
        verify(metrics, times(appIds.size())).updateSignalCloseCount();
    }
}
