package com.heal.signal.detector.scheduler;

import com.appnomic.appsone.common.protbuf.AnomalyEventProtos;
import com.heal.configuration.enums.SignalType;
import com.heal.configuration.pojos.Account;
import com.heal.configuration.pojos.BasicKpiEntity;
import com.heal.configuration.pojos.CompInstKpiEntity;
import com.heal.signal.detector.TestMain;
import com.heal.signal.detector.pojos.FailedOpenSignalPojo;
import com.heal.signal.detector.process.*;
import com.heal.signal.detector.util.LocalQueues;
import com.heal.signal.detector.util.TestUtil;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

import java.util.*;
import java.util.concurrent.ArrayBlockingQueue;

import static org.mockito.Mockito.verify;

class DroppedAnomalyCheckerTest extends TestMain {

    @InjectMocks
    DroppedAnomalyChecker droppedAnomalyChecker;

    @Mock
    BatchSignalProcessor batchSignalProcessor;

    @Mock
    InfoSignalProcessor infoSignalProcessor;

    @Mock
    ProblemSignalProcessor problemSignalProcessor;

    @Mock
    WarningSignalProcessor warningSignalProcessor;

    @Mock
    GenericSignalProcessor genericSignalProcessor;

    @Mock
    private LocalQueues localQueues;

    private final String accountId = "sample_account_identifier";

    private final String groupName = "sample_group_name";
    private final String kpiId = "sample_kpi_identifier";

    @Test
    void testProcessBatchJobEvent() throws Exception {


        Map<String, String> metaDataMap = new HashMap<>();
        metaDataMap.put("group_name", groupName);

        Map<String, Double> threasholdMap = new HashMap<>();
        Calendar time = Calendar.getInstance(TimeZone.getTimeZone("UTC"));
        time.set(Calendar.SECOND, 0);
        time.set(Calendar.MILLISECOND, 0);
        long startTime = time.getTimeInMillis();
        long endTime = time.getTimeInMillis();
        String batchJobId = "";
        String anomalyEventId = "AE-B-" + batchJobId + "-" + kpiId + "-" + endTime / 1000;
        AnomalyEventProtos.AnomalyEvent anomalyEvent = TestUtil.buildAnomalyData(startTime, endTime, metaDataMap, threasholdMap,
                anomalyEventId, "sample_account_identifier", "sample_application_identifier", "SOR",
                "not equals", "sample_kpi_identifier", "", false, "Severe", true);


        Account account = Account.builder().identifier(accountId).id(9).build();

        FailedOpenSignalPojo failedAnomalyData = FailedOpenSignalPojo.builder().signalType(SignalType.BATCH_JOB)
                .anomalyEvent(anomalyEvent)
                .account(account)
                .build();


        Queue<FailedOpenSignalPojo> failedOpenSignalQueue = new ArrayBlockingQueue<>(1000);
        failedOpenSignalQueue.offer(failedAnomalyData);
        Mockito.when(localQueues.getFailedQueue()).thenReturn(failedOpenSignalQueue);

        droppedAnomalyChecker.checkDroppedAnomaly();

        verify(batchSignalProcessor).processBatchJobEvent(anomalyEvent, account);

    }

    @Test
    void testProcessInfoEvent() {

        Map<String, String> metaDataMap = new HashMap<>();
        metaDataMap.put("group_name", groupName);

        Map<String, Double> threasholdMap = new HashMap<>();
        Calendar time = Calendar.getInstance(TimeZone.getTimeZone("UTC"));
        time.set(Calendar.SECOND, 0);
        time.set(Calendar.MILLISECOND, 0);
        long startTime = time.getTimeInMillis();
        long endTime = time.getTimeInMillis();
        String batchJobId = "";
        String anomalyEventId = "AE-B-" + batchJobId + "-" + kpiId + "-" + endTime / 1000;
        AnomalyEventProtos.AnomalyEvent anomalyEvent = TestUtil.buildAnomalyData(startTime, endTime, metaDataMap, threasholdMap,
                anomalyEventId, "sample_account_identifier", "sample_application_identifier", "SOR",
                "not equals", "sample_kpi_identifier", "", false, "Severe", false);
        Account account = Account.builder().identifier(accountId).id(9).build();
        CompInstKpiEntity kpiEntity = CompInstKpiEntity.builder().identifier(kpiId).build();

        FailedOpenSignalPojo failedAnomalyData = FailedOpenSignalPojo.builder().signalType(SignalType.INFO)
                .anomalyEvent(anomalyEvent)
                .account(account)
                .compInstKpiEntity(kpiEntity)
                .build();


        Queue<FailedOpenSignalPojo> failedOpenSignalQueue = new ArrayBlockingQueue<>(1000);
        failedOpenSignalQueue.offer(failedAnomalyData);
        Mockito.when(localQueues.getFailedQueue()).thenReturn(failedOpenSignalQueue);

        droppedAnomalyChecker.checkDroppedAnomaly();

        verify(infoSignalProcessor).processInfoEvent(anomalyEvent, kpiEntity, account);

    }


    @Test
    void testProcessProblemEvent() throws Exception {

        Map<String, String> metaDataMap = new HashMap<>();
        metaDataMap.put("group_name", groupName);

        Map<String, Double> threasholdMap = new HashMap<>();
        Calendar time = Calendar.getInstance(TimeZone.getTimeZone("UTC"));
        time.set(Calendar.SECOND, 0);
        time.set(Calendar.MILLISECOND, 0);
        long startTime = time.getTimeInMillis();
        long endTime = time.getTimeInMillis();
        String batchJobId = "";
        String anomalyEventId = "AE-B-" + batchJobId + "-" + kpiId + "-" + endTime / 1000;
        AnomalyEventProtos.AnomalyEvent anomalyEvent = TestUtil.buildAnomalyData(startTime, endTime, metaDataMap, threasholdMap,
                anomalyEventId, "sample_account_identifier", "sample_application_identifier", "SOR",
                "not equals", "sample_kpi_identifier", "", false, "Severe", false);
        Account account = Account.builder().identifier(accountId).id(9).build();
        BasicKpiEntity kpiEntity = BasicKpiEntity.builder().identifier(kpiId).build();
        Set<String> appIds = new HashSet<>();
        appIds.add("app1");

        FailedOpenSignalPojo failedAnomalyData = FailedOpenSignalPojo.builder().signalType(SignalType.PROBLEM)
                .anomalyEvent(anomalyEvent)
                .account(account)
                .kpiEntity(kpiEntity)
                .appIds(appIds)
                .signalIds(null)
                .build();


        Queue<FailedOpenSignalPojo> failedOpenSignalQueue = new ArrayBlockingQueue<>(1000);
        failedOpenSignalQueue.offer(failedAnomalyData);
        Mockito.when(localQueues.getFailedQueue()).thenReturn(failedOpenSignalQueue);

        droppedAnomalyChecker.checkDroppedAnomaly();

        verify(genericSignalProcessor).processSignalEvent(SignalType.PROBLEM,anomalyEvent, kpiEntity, account, appIds, null);

    }

    @Test
    void testProcessWarningEvent() throws Exception {

        Map<String, String> metaDataMap = new HashMap<>();
        metaDataMap.put("group_name", groupName);

        Map<String, Double> threasholdMap = new HashMap<>();
        Calendar time = Calendar.getInstance(TimeZone.getTimeZone("UTC"));
        time.set(Calendar.SECOND, 0);
        time.set(Calendar.MILLISECOND, 0);
        long startTime = time.getTimeInMillis();
        long endTime = time.getTimeInMillis();
        String batchJobId = "";
        String anomalyEventId = "AE-B-" + batchJobId + "-" + kpiId + "-" + endTime / 1000;
        AnomalyEventProtos.AnomalyEvent anomalyEvent = TestUtil.buildAnomalyData(startTime, endTime, metaDataMap, threasholdMap,
                anomalyEventId, "sample_account_identifier", "sample_application_identifier", "SOR",
                "not equals", "sample_kpi_identifier", "", false, "Severe", false);
        Account account = Account.builder().identifier(accountId).id(9).build();
        BasicKpiEntity kpiEntity = BasicKpiEntity.builder().identifier(kpiId).build();
        Set<String> appIds = new HashSet<>();
        appIds.add("app1");

        FailedOpenSignalPojo failedAnomalyData = FailedOpenSignalPojo.builder().signalType(SignalType.EARLY_WARNING)
                .anomalyEvent(anomalyEvent)
                .account(account)
                .kpiEntity(kpiEntity)
                .appIds(appIds)
                .signalIds(null)
                .build();


        Queue<FailedOpenSignalPojo> failedOpenSignalQueue = new ArrayBlockingQueue<>(1000);
        failedOpenSignalQueue.offer(failedAnomalyData);
        Mockito.when(localQueues.getFailedQueue()).thenReturn(failedOpenSignalQueue);

        droppedAnomalyChecker.checkDroppedAnomaly();

        verify(genericSignalProcessor).processSignalEvent(SignalType.EARLY_WARNING,anomalyEvent, kpiEntity, account, appIds, null);

    }
}