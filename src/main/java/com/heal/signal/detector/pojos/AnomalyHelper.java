/* <PERSON><PERSON> created on 20/04/22 inside the package - com.heal.signal.detector.pojos */
package com.heal.signal.detector.pojos;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Set;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AnomalyHelper {

    private String anomalyId;
    private Set<String> signalIds;
    private String accountId;
    private long anomalyTime;
    private int retry;
}
