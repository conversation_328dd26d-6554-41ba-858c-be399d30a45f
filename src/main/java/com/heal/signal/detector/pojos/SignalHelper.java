package com.heal.signal.detector.pojos;

import com.heal.configuration.pojos.opensearch.SignalDetails;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SignalHelper {
    private Set<String> anomalies = new HashSet<>();
    private Set<String> affectedServices = new HashSet<>();
    private Set<String> rcaServices = new HashSet<>();
    private Set<String> rcaAnomalies = new HashSet<>();
    private Set<String> signals = new HashSet<>();
    private Map<String, SignalDetails> signalDetailsMap = new HashMap<>();
    private Map<String, SignalChanges> signalDetailChanges = new HashMap<>();

}
