package com.heal.signal.detector.pojos;

import com.appnomic.appsone.common.protbuf.AnomalyEventProtos;
import com.heal.configuration.enums.SignalType;
import com.heal.configuration.pojos.Account;
import com.heal.configuration.pojos.BasicKpiEntity;
import com.heal.configuration.pojos.CompInstKpiEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Set;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class FailedOpenSignalPojo {
    private AnomalyEventProtos.AnomalyEvent anomalyEvent;
    private BasicKpiEntity kpiEntity;
    private Account account;
    private SignalType signalType;
    private CompInstKpiEntity compInstKpiEntity;
    private Set<String> appIds;
    private Set<String> signalIds;
}
