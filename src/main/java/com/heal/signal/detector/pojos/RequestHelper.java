package com.heal.signal.detector.pojos;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.opensearch.client.opensearch.core.IndexRequest;
import org.opensearch.client.opensearch.core.UpdateRequest;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RequestHelper {

    //At any time either updateRequest or indexRequest will be populated.
    private UpdateRequest<?,?> updateRequest;
    private IndexRequest<?> indexRequest;
    private String accountIdentifier;
    private String indexName;
}
