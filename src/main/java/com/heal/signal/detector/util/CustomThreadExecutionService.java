package com.heal.signal.detector.util;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Map;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicLong;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class CustomThreadExecutionService {

    private final Map<String, ThreadPoolExecutor> appExecutorMap = new ConcurrentHashMap<>();

    @Value("${thread.pool.core.size:1}")
    private int corePoolSize;

    @Value("${thread.pool.max.size:1}")
    private int maxPoolSize;

    @Value("${thread.pool.max.queue.size:1000}")
    private int maxQueueSize;

    @Autowired
    private HealthMetrics healthMetrics;

    public void executeOnAppThread(String mapKey, Runnable task) {
        log.trace("executeOnAppThread method called.");
        if (appExecutorMap.containsKey(mapKey)) {
            log.trace("Running task for [{}] on existing thread.", mapKey);
            ThreadPoolExecutor threadPoolExecutor = appExecutorMap.get(mapKey);
            threadPoolExecutor.execute(task);
        } else {
            String threadPoolNameFormat = "SD-Worker-" + mapKey + "-%d";
            ThreadFactory namedThreadFactory = new ThreadFactoryBuilder().setNameFormat(threadPoolNameFormat).build();
            LinkedBlockingQueue<Runnable> workQueue = new LinkedBlockingQueue<>(maxQueueSize);
            ThreadPoolExecutor executor = new ThreadPoolExecutor(corePoolSize, maxPoolSize, 0L, TimeUnit.MILLISECONDS, workQueue, namedThreadFactory);
            executor.setRejectedExecutionHandler((runnable, threadPoolExecutor) -> healthMetrics.updateThreadRejectedTaskCount(threadPoolNameFormat));
            appExecutorMap.put(mapKey, executor);
            log.trace("Creating new thread for [{}]", mapKey);
            executor.execute(task);
        }
    }

    public String getWorkerStatistics() {
        StringBuilder sb = new StringBuilder();
        sb.append("Current ApplicationExecutorMap size : ").append(appExecutorMap.size());
        AtomicLong totalQueueSize = new AtomicLong();
        appExecutorMap.forEach((key, value) -> {
            sb.append(", ").append("Application : ").append(key).append(", Time : ").append(getCurrentTimeStamp()).append(", ThreadPool stats:- ");
            sb.append("Current Pool Size : ").append(value.getPoolSize()).append(", ");
            sb.append("Core Pool Size : ").append(value.getCorePoolSize()).append(", ");
            sb.append("Max Pool Size : ").append(value.getMaximumPoolSize()).append(", ");
            sb.append("Active thread count : ").append(value.getActiveCount()).append(", ");
            sb.append("Idle thread count : ").append(value.getPoolSize() - value.getActiveCount()).append(", ");
            sb.append("Tasks completed : ").append(value.getCompletedTaskCount()).append(", ");
            sb.append("Pending tasks : ").append(value.getTaskCount() - value.getCompletedTaskCount()).append(", ");
            sb.append("Queue size : ").append(value.getQueue().size()).append(", ");
            totalQueueSize.addAndGet(value.getQueue().size());
            sb.append("Queue max size : ").append(maxQueueSize).append("\n");
        });

        sb.append(", ").append("Total queue size : ").append(totalQueueSize.get());

        return sb.toString();
    }

    public int getThreadPoolCoreSize() {
        return corePoolSize;
    }

    public String getCurrentTimeStamp() {
        LocalDateTime dateTime = Instant.ofEpochMilli(System.currentTimeMillis())
                .atZone(ZoneId.systemDefault())
                .toLocalDateTime();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        return dateTime.format(formatter);
    }
}
