package com.heal.signal.detector.util;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class Constants {

    //RabbitMQ specific
    public static final boolean IS_QUEUE_DURABLE = true;
    public static final boolean IS_QUEUE_EXCLUSIVE = false;
    public static final boolean QUEUE_AUTO_DELETE = false;


    //local cache constants
    public static final String CACHE_MAXIMUM_SIZE_PROPERTY_NAME = "local.cache.maximum.size";
    public static final String CACHE_MAXIMUM_SIZE_DEFAULT_VALUE = "5000";
    public static final String CACHE_TIMEOUT_IN_MINUTES_PROPERTY_NAME = "local.cache.timeout.minute";
    public static final String CACHE_TIMEOUT_IN_MINUTES_DEFAULT_VALUE = "5";

    public static final String VIEW_ALL_TYPES = "ALL_TYPES";

    public static final String ACCOUNTS = "accountsCache";
    public static final String ACCOUNT_OUTBOUNDS = "accountOutbounds";
    public static final String APPLICATION_BY_IDENTIFIER = "applicationByIdentifierCache";
    public static final String COMPONENT_KPIS = "componentKpisCache";
    public static final String COMPONENT_KPIS_BY_IDENTIFIER = "componentKpiByIdentifierCache";
    public static final String SERVICE_BY_IDENTIFIER = "serviceByIdentifierCache";
    public static final String SERVICE_MAINTENANCE_BY_IDENTIFIER = "serviceMaintenanceCache";
    public static final String INSTANCE_MAINTENANCE_BY_IDENTIFIER = "instanceMaintenanceCache";
    public static final String INSTANCE_KPIS_BY_IDENTIFIER = "instanceKpisCache";
    public static final String SERVICE_NEIGHBOURS_BY_IDENTIFIER = "serviceNeighboursCache";
    public static final String HEAL_TYPES = "healTypesCache";
    public static final String TENANTS = "tenantsCache";
    public static final String ACCOUNT_SERVICES = "accountServices";
    public static final String APPLICATIONS = "applicationsCache";

    public static final String TRANSACTION_COMPONENT_IDENTIFIER = "Transaction";
}
