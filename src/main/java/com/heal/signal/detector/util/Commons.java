package com.heal.signal.detector.util;

import com.appnomic.appsone.common.protbuf.AnomalyEventProtos;
import com.appnomic.appsone.common.protbuf.SignalProtos;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.heal.configuration.enums.SignalType;
import com.heal.configuration.pojos.*;
import com.heal.configuration.pojos.opensearch.SignalDetails;
import com.heal.signal.detector.cache.CacheWrapper;
import com.heal.signal.detector.pojos.SignalStatus;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

@Slf4j
@Component
public class Commons {

    @Autowired
    RedisUtilities redisUtilities;

    @Value("${signal.latest.events.count:10}")
    protected int lastNEvents;

    @Value("${early.warning.processing.include.outbounds:1}")
    protected int includeOutbounds;

    @Autowired
    HealthMetrics metrics;

    @Autowired
    CacheWrapper wrapper;

    @Value("${offset.from.gmt:19800000}")
    protected long offsetFromGMT;

    public boolean isUnderMaintenance(List<MaintenanceDetails> maintenanceDetailsList, long timeInMilliSecs) {
        Timestamp timestamp;
        try {
            timestamp = new Timestamp(timeInMilliSecs);
        } catch (Exception e) {
            log.error("Error while timestamp conversion, gmtTimeInMilliSecs:{}", timeInMilliSecs, e);
            return false;
        }
        boolean isUnderMaintenance;
        for (MaintenanceDetails maintenanceDetails : maintenanceDetailsList) {
            if (maintenanceDetails == null) continue;
            log.debug("maintenanceDetails== {}", maintenanceDetails);
            Timestamp startTime = maintenanceDetails.getStartTime();
            Timestamp endTime = maintenanceDetails.getEndTime();
            if (startTime == null) return false;
            log.debug("isUnderMaintenance() : data time : {}, maintenance start time : {}, maintenance end time : {}.", timestamp.getTime(), startTime.getTime(), endTime != null ? endTime.getTime() : null);
            if (endTime == null) {
                isUnderMaintenance = (startTime.before(timestamp) || startTime.equals(timestamp));
            } else {
                isUnderMaintenance =
                        ((startTime.before(timestamp) || startTime.equals(timestamp)) && (endTime.after(timestamp) || endTime.equals(timestamp)));
            }
            if (isUnderMaintenance) return isUnderMaintenance;
        }
        return false;
    }


    public boolean isServicesUnderMaintenance(Set<String> accountIdentifierSet, Set<String> services, String signalId) {
        log.debug("Checking the maintenance window for account identifier : {}, and services : {}, signalId:{}", accountIdentifierSet, services, signalId);
        if (services == null || services.isEmpty()) return false;
        if (accountIdentifierSet == null || accountIdentifierSet.isEmpty()) return false;

        Map<String, Set<String>> accSvcMap = accountIdentifierSet.stream()
                .collect(Collectors.toMap(a -> a, a -> {
                    List<BasicEntity> serviceList = wrapper.getAccountServices(a);

                    return serviceList.stream()
                            .filter(c -> c.getStatus() == 1)
                            .map(BasicEntity::getIdentifier)
                            .filter(services::contains)
                            .collect(Collectors.toSet());
                }));

        int validSvcListSize = accSvcMap.values().stream().flatMap(Collection::stream).collect(Collectors.toSet()).size();

        if (services.size() != validSvcListSize) {
            log.error("Invalid services present in the signal service list. Marking services as not under maintenance for signalId:{}.", signalId);
            return false;
        }

        AtomicInteger underMaintenanceCount = new AtomicInteger();
        accSvcMap.forEach((accIdentifier, svcList) -> {
            for (String serviceId : svcList) {
                List<MaintenanceDetails> maintenanceDetailsList = wrapper.getServiceMaintenanceDetails(accIdentifier, serviceId);
                if (maintenanceDetailsList == null || maintenanceDetailsList.isEmpty()) {
                    log.info("Maintenance details is not found in DS config data for service :{} and account :{}, signalId:{}", serviceId, accIdentifier, signalId);
                    continue;
                }

                boolean isMaintenance = isUnderMaintenance(maintenanceDetailsList, Calendar.getInstance().getTimeInMillis() + offsetFromGMT);
                log.debug("Is service :{} of account :{} is under maintenance :{}, signalId:{}.", serviceId, accIdentifier, isMaintenance, signalId);
                if (isMaintenance) {
                    underMaintenanceCount.addAndGet(1);
                }
            }

            log.info("services : {} of account : {} are under maintenance:{}, signalId:{}", services, accIdentifier, underMaintenanceCount, signalId);
        });

        // All the services in signal are under maintenance then true, otherwise false.
        return validSvcListSize == underMaintenanceCount.get();
    }

    public long getSignalIdealTime(String signalTypeStr, int infoSignalIdleTimeInMin, int batchSignalIdleTimeInMin,
                                   int signalIdleTimeInMin) {
        if (signalTypeStr == null || signalTypeStr.trim().isEmpty()) {
            return -1;
        }

        try {
            SignalType signalType = SignalType.valueOf(signalTypeStr);
            if (SignalType.INFO.compareTo(signalType) == 0) {
                return infoSignalIdleTimeInMin * 60000L;
            }

            if (SignalType.BATCH_JOB.compareTo(signalType) == 0) {
                return batchSignalIdleTimeInMin * 60000L;
            }

            return signalIdleTimeInMin * 60000L;
        } catch (Exception e) {
            log.error("Error occurred while getting signal ideal time. Signal type:{}", signalTypeStr, e);
            return -1;
        }
    }

    public Map<String, String> getSignalMetaData(AnomalyEventProtos.AnomalyEvent anomalyEvent, SignalType signalType) {
        Map<String, String> metaData = new HashMap<>();

        if (SignalType.BATCH_JOB.compareTo(signalType) == 0) {
            metaData.put("batch_job_id", anomalyEvent.getBatchInfo().getBatchJob());
            metaData.put("kpi_id", anomalyEvent.getBatchInfo().getKpiId());
            if (anomalyEvent.getBatchInfo().getMetadataMap().getOrDefault("isMaintenanceExcluded", "0").equalsIgnoreCase("1")) {
                metaData.put("lastMaintenanceExcluded", String.valueOf(anomalyEvent.getEndTimeGMT()));
            }
            metaData.putAll(anomalyEvent.getBatchInfo().getMetadataMap());
        } else {
            if (anomalyEvent.getKpis().getMetadataMap().getOrDefault("isMaintenanceExcluded", "0").equalsIgnoreCase("1")) {
                metaData.put("lastMaintenanceExcluded", String.valueOf(anomalyEvent.getEndTimeGMT()));
            }
            metaData.putAll(anomalyEvent.getKpis().getMetadataMap());
        }

        return metaData;
    }

    public String createSignalId(String prefix, int accountId, int kpiId, int serviceId, long timeInMin) {
        return prefix +
                "-" +
                accountId +
                "-" +
                kpiId +
                "-" +
                serviceId +
                "-" +
                timeInMin;
    }


    public SignalDetails createSignal(String signalId, long signalStartTime, long violationTime, long txnViolationTime, String signalStatus,
                                      Set<String> rootCauseAnomaliesIds, SignalType signalType, Set<String> serviceIds, Set<String> rootCauseServiceIds,
                                      Set<String> anomalyIds, Set<String> entryServiceIds, Set<String> relatedSignals, int severityId,
                                      Map<String, String> metaData, String accountIdentifier) {

        SignalDetails.SignalDetailsBuilder builder = SignalDetails.builder()
                .signalId(signalId)
                .startedTime(signalStartTime)
                .updatedTime(violationTime)
                .txnAnomalyTime(txnViolationTime)
                .currentStatus(signalStatus)
                .statusDetails(new HashSet<>() {{
                    add(signalStatus);
                }})
                .signalType(signalType.name())
                .severityId(severityId)
                .entryServiceId(entryServiceIds)
                .relatedSignals(relatedSignals)
                .serviceIds(serviceIds)
                .rootCauseAnomalyIds(rootCauseAnomaliesIds)
                .rootCauseServiceIds(rootCauseServiceIds)
                .anomalies(anomalyIds)
                .metadata(metaData)
                .accountIdentifiers(new HashSet<>() {{
                    add(accountIdentifier);
                }});

        if (signalType.compareTo(SignalType.PROBLEM) == 0) {
            builder.txnAnomalyTime(txnViolationTime);
        }

        return builder.build();
    }

    public Set<SignalDetails> processAndGetOpenSignals(Set<Service> services, String accountIdentifier, String anomalyId,
                                                       Map<String, String> dcDrServiceMap, Map<String, String> drDcServiceMap,
                                                       Set<SignalDetails> openSignalsFromOS, boolean isEarlyWarning, Set<String> appIds) throws Exception {
        try {
            Set<String> sourceAndDestinationServicesSet = services.stream()
                    .map(service -> {
                        try {
                            Set<String> sourceAndDestinationServices = new HashSet<>() {{
                                add(service.getIdentifier());
                            }};

                        Set<String> destinationServices = new HashSet<>();
                        Map<String, Map<String, Long>> openSignalsForApp = new HashMap<>();
                        appIds.forEach(appId -> {
                            openSignalsForApp.putAll(redisUtilities.getOpenSignalsForApp(appId));
                        });
                        if (!openSignalsForApp.isEmpty()) {
                            destinationServices = openSignalsForApp.keySet();
                        }

                        if (destinationServices.isEmpty()) {
                            log.info("No other services found for appId:{}, serviceId:{}, serviceName:{}, accountId:{} for anomalyId:{}.",
                                    appIds, service.getIdentifier(), service.getName(), accountIdentifier, anomalyId);
                        } else {
                            log.debug("{}:Services found for appId:{}, serviceId:{}, serviceName:{}, accountId:{} for anomalyId:{}.",
                                    destinationServices.size(), appIds, service.getIdentifier(), service.getName(), accountIdentifier, anomalyId);
                            sourceAndDestinationServices.addAll(destinationServices);
                        }

                            sourceAndDestinationServices.addAll(sourceAndDestinationServices.stream().map(svc -> {
                                        String linkedServiceIdentifierFromOtherAccount = dcDrServiceMap.getOrDefault(svc, drDcServiceMap.get(svc));

                                        if (linkedServiceIdentifierFromOtherAccount != null) {
                                            log.debug("Linked service {} found in service alias list for service {}", linkedServiceIdentifierFromOtherAccount, svc);
                                            return linkedServiceIdentifierFromOtherAccount;
                                        } else {
                                            log.debug("No linked service found in service alias list for service {}", svc);
                                            return null;
                                        }
                                    })
                                    .filter(Objects::nonNull)
                                    .collect(Collectors.toSet()));

                            log.debug("{}:Neighbours/Outbound/Cross Account Linked connections found for serviceId:{}, serviceName:{}, accountId:{} for anomalyId:{}.",
                                    sourceAndDestinationServices.size(), service.getIdentifier(), service.getName(), accountIdentifier, anomalyId);

                            return sourceAndDestinationServices;
                        } catch (Exception e) {
                            log.error("Exception while getting Open Signals related to service {}.", service.getIdentifier());
                            return null;
                        }
                    })
                    .filter(Objects::nonNull)
                    .flatMap(Collection::parallelStream)
                    .collect(Collectors.toSet());

            return getOpenSignals(accountIdentifier, openSignalsFromOS, sourceAndDestinationServicesSet, anomalyId, appIds);
        } catch (Exception e) {
            log.error("Exception while getting filtered open signal details from OS and Redis. ", e);
            throw new Exception("Exception while getting filtered open signal details from OS and Redis. " + e.getMessage());
        }
    }

    public Set<SignalDetails> getOpenSignals(String accountIdentifier, Set<SignalDetails> openSignalDetailsFromOS, Set<String> serviceIdentifiers, String anomalyId, Set<String> appIds) {
        Set<SignalDetails> filteredOpenSignalDetailsFromOS = openSignalDetailsFromOS.parallelStream()
                .filter(s -> s.getSignalType().equalsIgnoreCase(SignalType.PROBLEM.name())
                        || s.getSignalType().equalsIgnoreCase(SignalType.EARLY_WARNING.name()))
                .filter(s -> serviceIdentifiers.stream().anyMatch(serviceIdentifier -> s.getServiceIds().contains(serviceIdentifier)))
                .collect(Collectors.toSet());

        Set<String> signalIdsFromOS = filteredOpenSignalDetailsFromOS.stream().map(SignalDetails::getSignalId).collect(Collectors.toSet());
        log.debug("Number of open signals from open search for anomalyId:{}, accountId:{}, serviceIds:{} is {} signals. SignalIds:{}",
                anomalyId, accountIdentifier, serviceIdentifiers, filteredOpenSignalDetailsFromOS.size(), signalIdsFromOS);

        Map<String, Map<String, Long>> svcSignalsTimeMapFromRedis = new HashMap<>();
        appIds.forEach(appId -> {
            try {
                Map<String, Map<String, Long>> openSignalsByService = redisUtilities.getOpenSignalsForApp(appId);
                if (openSignalsByService == null || openSignalsByService.isEmpty()) return;

                // Deep-merge: serviceId -> (signalId -> time) across all appIds
                openSignalsByService.forEach((serviceId, signalTimeMap) -> {
                    if (signalTimeMap == null || signalTimeMap.isEmpty()) return;
                    svcSignalsTimeMapFromRedis
                            .computeIfAbsent(serviceId, k -> new HashMap<>())
                            .putAll(signalTimeMap);
                });
            } catch (Exception e) {
                log.error("Error while fetching open signals for appId:{}", appId, e);
                metrics.updateErrors();
            }
        });
        if (svcSignalsTimeMapFromRedis == null || svcSignalsTimeMapFromRedis.isEmpty()) {
            return filteredOpenSignalDetailsFromOS;
        }

        Set<String> signalIdsFromRedis = new HashSet<>();
        AtomicLong startTime = new AtomicLong(System.currentTimeMillis());
        serviceIdentifiers.forEach(s -> {
            Map<String, Long> signalTimeMapFromRedis = svcSignalsTimeMapFromRedis.getOrDefault(s, new HashMap<>());

            log.debug("Signals from redis cache for service id:{} is signalIds:{}", s, signalTimeMapFromRedis.keySet());

            if (!signalTimeMapFromRedis.isEmpty()) {
                signalIdsFromRedis.addAll(signalTimeMapFromRedis.entrySet()
                        .stream()
                        .filter(entry -> !signalIdsFromOS.contains(entry.getKey()))
                        .map(entry -> {
                            startTime.set(Math.min(startTime.get(), entry.getValue()));
                            return entry.getKey();
                        })
                        .collect(Collectors.toSet()));
            }
        });

        if (signalIdsFromRedis.isEmpty()) {
            return filteredOpenSignalDetailsFromOS;
        }

        List<SignalDetails> signalDetailsFromRedis = new ArrayList<>();
        signalIdsFromRedis.forEach(signalId -> {
            if (signalId.startsWith("E") || signalId.startsWith("P")) {
                SignalDetails signalDetails = redisUtilities.getSignalDetails(signalId);
                if (signalDetails == null) {
                    log.warn("Signal details not found in redis for signalId:{}.", signalId);
                } else {
                    signalDetailsFromRedis.add(signalDetails);
                }
            }
        });
        if (signalDetailsFromRedis.isEmpty()) {
            return filteredOpenSignalDetailsFromOS;
        }

        filteredOpenSignalDetailsFromOS.addAll(signalDetailsFromRedis.stream()
                .filter(Objects::nonNull)
                .filter(entry -> !"MLE".equalsIgnoreCase(entry.getMetadata().getOrDefault("Source", "HEAL")))
                .filter(s -> s.getCurrentStatus().equalsIgnoreCase(SignalStatus.OPEN.name()))
                .filter(s -> s.getSignalType().equalsIgnoreCase(SignalType.PROBLEM.name())
                        || s.getSignalType().equalsIgnoreCase(SignalType.EARLY_WARNING.name()))
                .collect(Collectors.toSet()));

        log.debug("Signals of size from redis cache and open search is:{}, serviceIdentifier:{}", filteredOpenSignalDetailsFromOS.size(), serviceIdentifiers);

        return filteredOpenSignalDetailsFromOS;
    }

    public AnomalySummary getAnomalySummary(String instanceId, String kpiId, String categoryId, Set<String> serviceIdentifiers,
                                            SignalType signalType, int severityId, long anomalyTime, boolean isTxnAnomaly,
                                            String anomalyId, String kpiAttribute, String kpiGroupId, String thresholdType,
                                            String operationType, Map<String, String> metaData, String kpiValue,
                                            Map<String, Double> thresholds, String accountIdentifier) {
        String dAnomalyId = severityId == 296 ? anomalyId : null;
        String sAnomalyId = severityId == 296 ? null : anomalyId;

        return AnomalySummary.builder()
                .signalType(signalType)
                .severityId(severityId)
                .kpiId(kpiId)
                .kpiAttribute(kpiAttribute)
                .kpiGroupId(kpiGroupId)
                .categoryId(categoryId)
                .serviceId(serviceIdentifiers)
                .eventTime(anomalyTime)
                .defaultAnomalyId(dAnomalyId)
                .severeAnomalyId(sAnomalyId)
                .instanceId(instanceId)
                .isTxnAnomaly(isTxnAnomaly)
                .anomalyId(anomalyId)
                .thresholdType(thresholdType)
                .operationType(operationType)
                .kpiValue(kpiValue)
                .thresholds(thresholds)
                .metadata(metaData)
                .txnId(isTxnAnomaly ? instanceId : null)
                .accountIdentifier(accountIdentifier)
                .build();
    }

    public SignalProtos.SignalDetails getSignalProto(SignalDetails signalDetails, boolean isRemainder,
                                                     boolean isSeverityChanged, boolean isServiceAdded, boolean isStatusChanged,
                                                     AnomalySummary anomalySummary) {

        SignalProtos.SignalDetails.Builder signalProtoBuilder = SignalProtos.SignalDetails.newBuilder();

        try {
            Set<AnomalySummary> anomalySummaries = redisUtilities.getSignalAnomalies(signalDetails.getSignalId());

            boolean isMLE = signalDetails.getMetadata().getOrDefault("Source", "").equalsIgnoreCase("MLE");
            log.debug("SignalId: {} is ML generated: {}: ", signalDetails.getSignalId(), isMLE);

            Set<AnomalySummary> topNAnomalies = anomalySummaries.stream()
                    .sorted(Comparator.comparing(AnomalySummary::getEventTime, Comparator.reverseOrder()))
                    .limit(isMLE ? 1 : lastNEvents - (anomalySummary == null ? 0 : 1))
                    .collect(Collectors.toSet());

            String serviceId = (isServiceAdded && anomalySummary != null) ? String.join(",", anomalySummary.getServiceId()) : null;
            if (anomalySummary != null) {
                anomalySummaries.add(anomalySummary);
                signalDetails.getMetadata().put("AnomalyId", anomalySummary.getAnomalyId());
                topNAnomalies.add(anomalySummary);
            }

            if (isServiceAdded && anomalySummary != null) {
                signalDetails.getMetadata().put("ServiceAdded", serviceId);
            }

            List<SignalProtos.AnomalyDetail> anomalyDetailList = topNAnomalies.parallelStream()
                    .map(as -> SignalProtos.AnomalyDetail.newBuilder()
                            .setAnomalyId(as.getAnomalyId())
                            .setSeverityId(as.getSeverityId())
                            .setIsWorkLoad(as.isTxnAnomaly())
                            .setInstanceId(as.getInstanceId())
                            .setKpiId(as.getKpiId())
                            .setKpiGroupId((as.getKpiGroupId() == null) ? "" : as.getKpiGroupId())
                            .setCategoryId(as.getCategoryId())
                            .setKpiAttribute(as.getKpiAttribute())
                            .setAnomalyTime(as.getEventTime())
                            .addAllServiceIds(as.getServiceId())
                            .setKpiValue(as.getKpiValue())
                            .putAllMetadata(new HashMap<>() {{
                                putAll(as.getMetadata());
                                put("accountIdentifier", as.getAccountIdentifier());
                            }})
                            .putAllThresholds(as.getThresholds())
                            .setOperationType(as.getOperationType())
                            .setThresholdType(as.getThresholdType())
                            .build())
                    .collect(Collectors.toList());


            signalProtoBuilder
                    .setAccountId(String.join(",", signalDetails.getAccountIdentifiers()))
                    .setSignalType(signalDetails.getSignalType())
                    .setSignalId(signalDetails.getSignalId())
                    .setSignalStatus(signalDetails.getCurrentStatus())
                    .setIsRemainder(isRemainder)
                    .setIsSeverityChanged(isSeverityChanged)
                    .setIsServiceAdded(isServiceAdded)
                    .putAllMetadata(new HashMap<>() {{
                        putAll(signalDetails.getMetadata());
                        put("accountIdentifiers", String.join(",", signalDetails.getAccountIdentifiers()));
                    }})
                    .setStartTime(signalDetails.getStartedTime())
                    .setUpdateTime(signalDetails.getUpdatedTime())
                    .addAllServiceIds(signalDetails.getServiceIds())
                    .addAllRootCauseServiceIds(signalDetails.getRootCauseServiceIds())
                    .addAllEntryServiceIds(signalDetails.getEntryServiceId() == null ? new ArrayList<>() : signalDetails.getEntryServiceId())
                    .setSignalSeverityId(anomalySummary != null ? anomalySummary.getSeverityId() : signalDetails.getSeverityId()) // checking the latest anomaly as signal severity
                    .build();

            signalProtoBuilder.addAllAnomalyDetails(anomalyDetailList);

            if (signalDetails.getRelatedSignals() != null && !signalDetails.getRelatedSignals().isEmpty()) {
                signalProtoBuilder.addAllRelatedSignals(signalDetails.getRelatedSignals());
            }

            SignalSummary summary = redisUtilities.getSignalSummary(signalDetails.getSignalId());
            if (summary == null) {
                log.error("Signal summary not found for signalId:{}, notification will have issues.", signalDetails.getSignalId());
            } else {
                signalProtoBuilder.setAnomalies(summary.getAnomalies() != null ? summary.getAnomalies().size() : 0);
            }

            List<SignalProtos.ServiceSummary> signalServiceSummariesProto = redisUtilities.getSignalServiceSummary(signalDetails.getSignalId()).parallelStream()
                    .map(serviceSummary -> SignalProtos.ServiceSummary.newBuilder()
                            .setServiceId(serviceSummary.getServiceIdentifier())
                            .addAllSevereAnomalies(serviceSummary.getSevereAnomalies())
                            .addAllDefaultAnomalies(serviceSummary.getDefaultAnomalies())
                            .addAllInstanceIds(serviceSummary.getInstances())
                            .addAllRequestIds(serviceSummary.getRequests())
                            .putAllCategories(serviceSummary.getCategories())
                            .setAnomalyTime(serviceSummary.getLatestAnomalyTimeInGMT())
                            .build())
                    .collect(Collectors.toList());
            signalProtoBuilder.addAllServiceSummary(signalServiceSummariesProto);

            List<SignalProtos.InstanceSummary> signalInstanceSummariesProto = redisUtilities.getSignalInstanceSummary(signalDetails.getSignalId())
                    .parallelStream()
                    .map(instanceSummary -> SignalProtos.InstanceSummary.newBuilder()
                            .setInstanceId(instanceSummary.getInstanceIdentifier())
                            .addAllSevereAnomalies(instanceSummary.getSevereAnomalies())
                            .addAllDefaultAnomalies(instanceSummary.getDefaultAnomalies())
                            .putAllKpiCategories(instanceSummary.getKpiCategories())
                            .putAllKpiSeverity(instanceSummary.getKpiSeverity())
                            .putAllKpiLatestTime(instanceSummary.getKpiLatestTime())
                            .setAnomalyTime(instanceSummary.getLatestEventTimeInGMT())
                            .build()).collect(Collectors.toList());
            signalProtoBuilder.addAllInstanceSummary(signalInstanceSummariesProto);

            List<SignalProtos.RequestSummary> signalTxnSummariesProto = redisUtilities.getSignalTransactionSummary(signalDetails.getSignalId())
                    .parallelStream()
                    .map(instanceSummary -> SignalProtos.RequestSummary.newBuilder()
                            .setTransactionId(instanceSummary.getTransactionIdentifier())
                            .setServiceId(instanceSummary.getServiceIdentifier())
                            .addAllSevereAnomalies(instanceSummary.getSevereAnomalies())
                            .addAllDefaultAnomalies(instanceSummary.getDefaultAnomalies())
                            .putAllKpiCategories(instanceSummary.getKpiCategories())
                            .putAllKpiSeverity(instanceSummary.getKpiSeverity())
                            .putAllKpiLatestTime(instanceSummary.getKpiLatestTime())
                            .setAnomalyTime(instanceSummary.getLatestEventTimeInGMT())
                            .build())
                    .collect(Collectors.toList());
            signalProtoBuilder.addAllRequestSummary(signalTxnSummariesProto);
        } catch (Exception e) {
            log.error("Error while populating signal proto for signalId:{}", signalDetails.getSignalId(), e);
            metrics.updateErrors();
        }
        return signalProtoBuilder.build();
    }

    public ObjectMapper getObjectMapperWithHtmlEncoder() {
        ObjectMapper objectMapper = new ObjectMapper();
        SimpleModule simpleModule = new SimpleModule("HTML-Encoder", objectMapper.version()).addDeserializer(String.class, new EscapeHTML());
        objectMapper.registerModule(simpleModule);
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        return objectMapper;
    }
}
