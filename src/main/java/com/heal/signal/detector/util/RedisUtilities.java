package com.heal.signal.detector.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.heal.configuration.pojos.*;
import com.heal.configuration.pojos.opensearch.SignalDetails;
import com.heal.signal.detector.pojos.SignalStatus;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Repository;

import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

@Slf4j
@Repository
public class RedisUtilities {

    @Autowired
    RedisTemplate<String, Object> redisTemplate;

    @Autowired
    HealthMetrics metrics;

    @Autowired
    private ObjectMapper objectMapper;

    public List<Account> getAccounts() {
        Object obj = null;
        try {
            obj = redisTemplate.opsForHash().entries("/accounts")
                    .get("ACCOUNT_DATA");
            if (obj == null) {
                log.error("Accounts unavailable in redis cache.");
                metrics.updateRedisKeysNotFound();
                metrics.updateSnapshots("/accounts", 1);
                return null;
            }

            List<Account> accounts = objectMapper.readValue(obj.toString(), new TypeReference<>() {
            });
            return accounts.stream().filter(a -> a.getStatus() == 1).collect(Collectors.toList());
        } catch (JsonProcessingException e) {
            log.error("Error while converting JSON string to accounts, Redis data:{}.", obj, e);
            metrics.updateErrors();
            return null;
        } catch (Exception e) {
            log.error("Error occurred while getting accounts.", e);
            metrics.updateErrors();
            return null;
        }
    }

    public Account getAccountDetails(String accIdentifier) {
        Object obj = null;
        try {
            obj = redisTemplate.opsForHash().entries("/accounts/" + accIdentifier)
                    .get("ACCOUNT_DATA_" + accIdentifier);
            if (obj == null) {
                log.error("Account details unavailable in redis cache for account identifier {}.", accIdentifier);
                metrics.updateRedisKeysNotFound();
                metrics.updateSnapshots("/accounts/" + accIdentifier, 1);
                return null;
            }

            Account account = objectMapper.readValue(obj.toString(), new TypeReference<>() {
            });
            if (account.getStatus() == 0) {
                log.error("Account is disabled for name:{}, identifier:{}.", account.getName(), accIdentifier);
                return null;
            }
            return account;
        } catch (JsonProcessingException e) {
            log.error("Error while converting JSON string to account details for account identifier {}, Redis data:{}.",
                    accIdentifier, obj, e);
            metrics.updateErrors();
            return null;
        } catch (Exception e) {
            log.error("Error occurred while getting account details, account identifier:{}.", accIdentifier, e);
            metrics.updateErrors();
            return null;
        }
    }

    public Application getApplicationDetails(String accIdentifier, String appIdentifier) {
        Object obj = null;
        try {
            obj = redisTemplate.opsForHash().entries("/accounts/" + accIdentifier + "/applications/" + appIdentifier)
                    .get("ACCOUNTS_" + accIdentifier + "_APPLICATIONS_" + appIdentifier);
            if (obj == null) {
                log.error("Application details unavailable in redis cache for account identifier {}, application identifier {}.",
                        accIdentifier, appIdentifier);
                metrics.updateRedisKeysNotFound();
                metrics.updateSnapshots("/accounts/" + accIdentifier + "/applications/" + appIdentifier, 1);
                return null;
            }

            Application application = objectMapper.readValue(obj.toString(), new TypeReference<>() {
            });
            if (application.getStatus() == 0) {
                log.error("Application is disabled for name:{}, identifier:{}.", application.getName(), appIdentifier);
                return null;
            }
            return application;
        } catch (JsonProcessingException e) {
            log.error("Error while converting JSON string to service details for account identifier {}, " +
                    "application identifier {}, Redis data:{}.", accIdentifier, appIdentifier, obj, e);
            metrics.updateErrors();
            return null;
        } catch (Exception e) {
            log.error("Error occurred while getting service details, account identifier:{}, application identifier:{}.",
                    accIdentifier, appIdentifier, e);
            metrics.updateErrors();
            return null;
        }
    }

    public BasicKpiEntity getKPIDetails(String accIdentifier, String componentIdentifier, String kpiIdentifier) {
        Object obj = null;
        try {
            obj = redisTemplate.opsForHash().entries("/accounts/" + accIdentifier + "/components/" + componentIdentifier + "/kpis")
                    .get("ACCOUNTS_" + accIdentifier + "_COMPONENTS_" + componentIdentifier + "_KPIS");
            if (obj == null) {
                log.error("Component details unavailable in redis cache for account identifier {}, component Identifier identifier {}.",
                        accIdentifier, componentIdentifier);
                metrics.updateRedisKeysNotFound();
                metrics.updateSnapshots("/accounts/" + accIdentifier + "/components/" + componentIdentifier, 1);
                return null;
            }

            List<BasicKpiEntity> componentKpisList = objectMapper.readValue(obj.toString(), new TypeReference<>() {
            });
            BasicKpiEntity kpiEntity = componentKpisList.parallelStream()
                    .filter(k -> k.getIdentifier().equalsIgnoreCase(kpiIdentifier) || kpiIdentifier.equalsIgnoreCase(k.getId() + ""))
                    .findAny()
                    .orElse(null);
            if (kpiEntity == null || kpiEntity.getStatus() == 0) {
                log.error("KPI is disabled or does not exists for identifier:{}, component:{}, kpiEntity:{}.", kpiIdentifier, componentIdentifier, kpiEntity);
                return null;
            }
            return kpiEntity;
        } catch (JsonProcessingException e) {
            log.error("Error while converting JSON string to component details for account identifier {}, " +
                    "component identifier {}, Redis data:{}.", accIdentifier, componentIdentifier, obj, e);
            metrics.updateErrors();
            return null;
        } catch (Exception e) {
            log.error("Error occurred while getting component details, account identifier:{}, component identifier:{}, " +
                    "Kpi identifier:{}.", accIdentifier, componentIdentifier, kpiIdentifier, e);
            metrics.updateErrors();
            return null;
        }
    }

    public List<BasicKpiEntity> getComponentKPIs(String accIdentifier, String componentIdentifier) {
        Object obj = null;
        try {
            obj = redisTemplate.opsForHash().entries("/accounts/" + accIdentifier + "/components/" + componentIdentifier + "/kpis")
                    .get("ACCOUNTS_" + accIdentifier + "_COMPONENTS_" + componentIdentifier + "_KPIS");
            if (obj == null) {
                log.error("Component details unavailable in redis cache for account identifier {}, component Identifier identifier {}.",
                        accIdentifier, componentIdentifier);
                metrics.updateRedisKeysNotFound();
                metrics.updateSnapshots("/accounts/" + accIdentifier + "/components/" + componentIdentifier, 1);
                return null;
            }

            List<BasicKpiEntity> kpiEntities = objectMapper.readValue(obj.toString(), new TypeReference<>() {
            });
            return kpiEntities.parallelStream().filter(k -> k.getStatus() == 1).collect(Collectors.toList());
        } catch (JsonProcessingException e) {
            log.error("Error while converting JSON string to component details for account identifier {}, " +
                    "component identifier {}, Redis data:{}.", accIdentifier, componentIdentifier, obj, e);
            metrics.updateErrors();
            return null;
        } catch (Exception e) {
            metrics.updateErrors();
            log.error("Error occurred while getting component details, account identifier:{}, component identifier:{}.", accIdentifier, componentIdentifier, e);
            return null;
        }
    }

    public Service getServiceDetails(String accIdentifier, String serviceIdentifier) {
        Object obj = null;
        try {
            obj = redisTemplate.opsForHash().entries("/accounts/" + accIdentifier + "/services/" + serviceIdentifier)
                    .get("ACCOUNTS_" + accIdentifier + "_SERVICES_" + serviceIdentifier);
            if (obj == null) {
                log.error("Service details unavailable in redis cache for account identifier {}, service identifier {}.",
                        accIdentifier, serviceIdentifier);
                metrics.updateRedisKeysNotFound();
                metrics.updateSnapshots("/accounts/" + accIdentifier + "/services/" + serviceIdentifier, 1);
                return null;
            }

            Service service = objectMapper.readValue(obj.toString(), new TypeReference<>() {
            });
            if (service.getStatus() == 0) {
                log.error("Service is disabled for name:{}, identifier:{}, account identifier:{}", service.getName(), serviceIdentifier, accIdentifier);
                return null;
            }
            return service;
        } catch (JsonProcessingException e) {
            log.error("Error while converting JSON string to service details for account identifier {}, " +
                    "service identifier {}, Redis data:{}.", accIdentifier, serviceIdentifier, obj, e);
            metrics.updateErrors();
            return null;
        } catch (Exception e) {
            metrics.updateErrors();
            log.error("Error occurred while getting service details, account identifier:{}, service identifier:{}.", accIdentifier, serviceIdentifier, e);
            return null;
        }
    }

    public List<BasicEntity> getAccountServices(String accIdentifier) {
        Object obj = null;
        try {
            obj = redisTemplate.opsForHash().entries("/accounts/" + accIdentifier + "/services").get("ACCOUNTS_" + accIdentifier + "_SERVICES");
            if (obj == null) {
                log.error("Service list unavailable in redis cache for account identifier {}.", accIdentifier);
                metrics.updateRedisKeysNotFound();
                metrics.updateSnapshots("/accounts/" + accIdentifier + "/services", 1);
                return new ArrayList<>();
            }

            return objectMapper.readValue(obj.toString(), new TypeReference<>() {
            });
        } catch (JsonProcessingException e) {
            log.error("Error while converting JSON string to service list for account identifier {}, Redis data:{}.",
                    accIdentifier, obj, e);
            metrics.updateErrors();
            return null;
        } catch (Exception e) {
            metrics.updateErrors();
            log.error("Error occurred while getting service details, account identifier:{}.", accIdentifier, e);
            return null;
        }
    }

    public List<MaintenanceDetails> getServiceMaintenanceDetails(String accountIdentifier, String serviceIdentifier) {

        Object obj = null;
        try {
            obj = redisTemplate.opsForHash().get("/accounts/" + accountIdentifier + "/services/" + serviceIdentifier + "/maintenanceDetails",
                    "ACCOUNTS_" + accountIdentifier + "_SERVICES_" + serviceIdentifier + "_MAINTENANCE_DETAILS");

            if (obj == null) {
                log.debug("Maintenance details information unavailable for account identifier:{}, service identifier:{} ",
                        accountIdentifier, serviceIdentifier);
                metrics.updateRedisKeysNotFound();
                metrics.updateSnapshots("/accounts/" + accountIdentifier +
                        "/services/" + serviceIdentifier + "/maintenanceDetails", 1);
                return Collections.emptyList();
            }

            return objectMapper.readValue(obj.toString(), new TypeReference<>() {
            });
        } catch (JsonProcessingException e) {
            log.error("Error while converting JSON string to maintenance details for account identifier:{}, " +
                    "service identifier:{}, Redis data:{}: ", serviceIdentifier, accountIdentifier, obj, e);
            metrics.updateErrors();
            return Collections.emptyList();
        } catch (Exception e) {
            metrics.updateErrors();
            log.error("Error occurred while getting service maintenance details, account identifier:{}, service identifier:{}.",
                    accountIdentifier, serviceIdentifier, e);
            return null;
        }
    }

    public List<MaintenanceDetails> getInstanceMaintenanceDetails(String accountIdentifier, String instanceIdentifier) {
        Object obj = null;
        try {
            obj = redisTemplate.opsForHash().get("/accounts/" + accountIdentifier +
                            "/instances/" + instanceIdentifier + "/maintenanceDetails",
                    "ACCOUNTS_" + accountIdentifier + "_INSTANCES_" + instanceIdentifier + "_MAINTENANCE_DETAILS");

            if (obj == null) {
                log.debug("Maintenance details information unavailable for account identifier:{}, instance identifier:{}",
                        accountIdentifier, instanceIdentifier);
                metrics.updateRedisKeysNotFound();
                metrics.updateSnapshots("/accounts/" + accountIdentifier +
                        "/instances/" + instanceIdentifier + "/maintenanceDetails", 1);
                return Collections.emptyList();
            }

            return objectMapper.readValue(obj.toString(), new TypeReference<>() {
            });
        } catch (JsonProcessingException e) {
            log.error("Error while converting JSON string to maintenance details for account identifier:{}, " +
                    "instance identifier:{}, Redis data:{}", accountIdentifier, instanceIdentifier, obj, e);
            metrics.updateErrors();
            return Collections.emptyList();
        } catch (Exception e) {
            log.error("Error occurred while getting instance maintenance details, account identifier:{}, instance identifier:{}.",
                    accountIdentifier, instanceIdentifier, e);
            metrics.updateErrors();
            return null;
        }
    }

    public CompInstKpiEntity getInstanceKPIDetails(String accountIdentifier, String instanceIdentifier, int kpiId) {
        Object obj = null;
        try {
            obj = redisTemplate.opsForHash().get("/accounts/" + accountIdentifier
                            + "/instances/" + instanceIdentifier + "/kpis/" + kpiId,
                    "ACCOUNTS_" + accountIdentifier + "_INSTANCES_" + instanceIdentifier +
                            "_KPIS_" + kpiId);

            if (obj == null) {
                log.warn("KPI [{}] information unavailable for instance [{}] and account [{}].", kpiId,
                        instanceIdentifier, accountIdentifier);
                metrics.updateRedisKeysNotFound();
                metrics.updateSnapshots("/accounts/" + accountIdentifier
                        + "/instances/" + instanceIdentifier + "/kpis/" + kpiId, 1);
                return null;
            }
            CompInstKpiEntity compInstKpiEntity = objectMapper.readValue(obj.toString(), new TypeReference<>() {
            });
            if (compInstKpiEntity.getStatus() == 0) {
                log.error("KPI is disabled for instance:{}, kpi:{}, account:{}", instanceIdentifier, kpiId, accountIdentifier);
                return null;
            }
            return compInstKpiEntity;
        } catch (JsonProcessingException e) {
            log.error("Error while converting JSON string to KPI for Account identifier:{}, instance identifier:{} and KPI:{}. " +
                    "Proceeding for Service level threshold check, Redis data:{}.", accountIdentifier, instanceIdentifier, kpiId, obj, e);
            metrics.updateErrors();
            return null;
        } catch (Exception e) {
            log.error("Error occurred while getting instance kpi details, account identifier:{}, instance identifier:{}, kpi id:{}.",
                    accountIdentifier, instanceIdentifier, kpiId, e);
            metrics.updateErrors();
            return null;
        }
    }

    public int getSeverityId(String name) {

        List<ViewTypes> viewTypesList = new ArrayList<>();
        Object obj = null;
        try {
            obj = redisTemplate.opsForHash().get("/heal/types", "HEAL_TYPES");

            if (obj == null) {
                log.debug("Severity Id are unavailable for name:{}.", name);
                metrics.updateRedisKeysNotFound();
                metrics.updateSnapshots("/heal/types", 1);
            } else {
                viewTypesList = objectMapper.readValue(obj.toString(), new TypeReference<>() {
                });
            }
        } catch (JsonProcessingException e) {
            log.error("Error while converting JSON string for severity name:{}. Redis data:{}.", name, obj, e);
            metrics.updateErrors();
        } catch (Exception e) {
            log.error("Error occurred while getting severity name:{}.", name, e);
            metrics.updateErrors();
        }

        ViewTypes signalSeverity = viewTypesList.parallelStream().filter(v -> v.getTypeName().equalsIgnoreCase("SignalSeverity"))
                .filter(v -> v.getSubTypeName().equalsIgnoreCase(name)).findAny().orElse(null);

        if (signalSeverity != null) {
            return signalSeverity.getSubTypeId();
        }

        if ("Severe".equalsIgnoreCase(name)) {
            return 295;
        } else {
            return 296;
        }
    }

    public Set<BasicEntity> getNeighbours(String accountIdentifier, String serviceIdentifier) {
        Object obj = null;
        try {
            obj = redisTemplate.opsForHash().get("/accounts/" + accountIdentifier + "/services/" + serviceIdentifier + "/neighbours",
                    "ACCOUNTS_" + accountIdentifier + "_SERVICES_" + serviceIdentifier + "_NEIGHBOURS");

            if (obj == null) {
                log.debug("Neighbours are unavailable for service [{}] and account [{}].", serviceIdentifier, accountIdentifier);
                metrics.updateRedisKeysNotFound();
                metrics.updateSnapshots("/accounts/" + accountIdentifier
                        + "/services/" + serviceIdentifier + "/neighbour", 1);
                return Collections.emptySet();
            }
            return objectMapper.readValue(obj.toString(), new TypeReference<>() {
            });
        } catch (JsonProcessingException e) {
            log.error("Error while converting JSON string for Account identifier:{}, service identifier:{}. " +
                    "Redis data:{}.", accountIdentifier, serviceIdentifier, obj, e);
            metrics.updateErrors();
            return Collections.emptySet();
        } catch (Exception e) {
            metrics.updateErrors();
            log.error("Error occurred while getting neighbours account identifier:{}, service identifier:{}.",
                    accountIdentifier, serviceIdentifier, e);
            return Collections.emptySet();
        }
    }

    public Set<ControllerAlias> getServiceAliases() {
        Object obj = null;
        try {
            obj = redisTemplate.opsForHash().get("/accounts/services", "ACCOUNTS_SERVICES");
            if (obj == null) {
                log.debug("Service aliases list unavailable in redis.");
                metrics.updateRedisKeysNotFound();
                metrics.updateSnapshots("/accounts/services", 1);
                return Collections.emptySet();
            }

            return objectMapper.readValue(obj.toString(), new TypeReference<>() {
            });
        } catch (JsonProcessingException e) {
            log.error("Error while converting JSON string for service aliases list. Redis data:{}.", obj, e);
            metrics.updateErrors();
            return Collections.emptySet();
        } catch (Exception e) {
            metrics.updateErrors();
            log.error("Error occurred while getting service aliases list.", e);
            return Collections.emptySet();
        }
    }

    public void updateServiceSignal(String signalId, long signalStartTime, Set<String> serviceIds, String signalStatus) {
        String key = "/signals/open";
        String hashKey = "SIGNALS_OPEN";
        try {
            AtomicReference<Map<String, Long>> signalMap = new AtomicReference<>(new HashMap<>());
            Map<String, Map<String, Long>> serviceSignals = getOpenSignalsInRedis();

            serviceIds.forEach(serviceId -> {
                signalMap.set(serviceSignals.getOrDefault(serviceId, new HashMap<>()));
                log.trace("Before update serviceId:{}, signalMap:{}", serviceId, signalMap);

                if (signalMap.get().containsKey(signalId) && (signalStatus.equalsIgnoreCase(SignalStatus.UPGRADED.name()) || signalStatus.equalsIgnoreCase(SignalStatus.DOWNGRADED.name()) || signalStatus.equalsIgnoreCase(SignalStatus.CLOSED.name()))) {
                    signalMap.get().remove(signalId);
                    log.debug("Service signal will be removed from redis cache. serviceId:{}, signalId:{}, signalStatus:{}", serviceId, signalId, signalStatus);
                } else if (!signalMap.get().containsKey(signalId)) {
                    signalMap.get().put(signalId, signalStartTime);
                    log.debug("Service signal will be added to redis cache. serviceId:{}, signalId:{}, signalStatus:{}", serviceId, signalId, signalStatus);
                }
                log.trace("After update serviceId:{}, signalMap:{}", serviceIds, signalMap);
                serviceSignals.put(serviceId, signalMap.get());
            });

            String dataFromDBInJson = objectMapper.writeValueAsString(serviceSignals);
            redisTemplate.opsForHash().put(key, hashKey, dataFromDBInJson);
        } catch (Exception e) {
            metrics.updateErrors();
            log.error("Error occurred while updating service signals for service:{}, signalId:{}.", serviceIds, signalId, e);
        }
    }


    public Map<String, Map<String, Long>> getOpenSignalsInRedis() {
        String key = "/signals/open";
        String hashKey = "SIGNALS_OPEN";
        try {

            Object obj = redisTemplate.opsForHash().get(key, hashKey);
            if (obj == null) {
                log.debug("Open signals is unavailable in redis.");
                metrics.updateRedisKeysNotFound();
                metrics.updateSnapshots(key, 1);
                return new HashMap<>();
            }

            return objectMapper.readValue(obj.toString(), new TypeReference<>() {
            });
        } catch (Exception e) {
            metrics.updateErrors();
            log.error("Error occurred while getting open signals from redis.", e);
            return new HashMap<>();
        }
    }

    /**
     * Updates the open signals for a specific application in Redis.
     *
     * @param signalId        The ID of the signal to update.
     * @param signalStartTime The start time of the signal.
     * @param serviceIds      The set of service IDs associated with the signal.
     * @param signalStatus    The status of the signal (e.g., UPGRADED, CLOSED).
     * @param appIdentifier   The identifier of the application.
     */
    public void updateServiceOpenSignalsForApp(String signalId, long signalStartTime, Set<String> serviceIds, String signalStatus, String appIdentifier) {
        String key = "/applications/" + appIdentifier + "/signals/open";
        String hashKey = "APPLICATIONS_" + appIdentifier + "_SIGNALS_OPEN";
        try {
            AtomicReference<Map<String, Long>> signalMap = new AtomicReference<>(new HashMap<>());
            Map<String, Map<String, Long>> serviceSignals = getOpenSignalsForApp(appIdentifier);

            serviceIds.forEach(serviceId -> {
                signalMap.set(serviceSignals.getOrDefault(serviceId, new HashMap<>()));
                log.trace("Before update serviceId:{}, signalMap:{}", serviceId, signalMap);

                if (signalMap.get().containsKey(signalId) && (signalStatus.equalsIgnoreCase(SignalStatus.UPGRADED.name()) || signalStatus.equalsIgnoreCase(SignalStatus.CLOSED.name()))) {
                    signalMap.get().remove(signalId);
                    log.debug("Service signal will be removed from redis cache. serviceId:{}, signalId:{}, signalStatus:{}", serviceId, signalId, signalStatus);
                } else if (!signalMap.get().containsKey(signalId)) {
                    signalMap.get().put(signalId, signalStartTime);
                    log.debug("Service signal will be added to redis cache. serviceId:{}, signalId:{}, signalStatus:{}", serviceId, signalId, signalStatus);
                }
                log.trace("After update serviceId:{}, signalMap:{}", serviceIds, signalMap);
                serviceSignals.put(serviceId, signalMap.get());
            });

            String dataFromDBInJson = objectMapper.writeValueAsString(serviceSignals);
            redisTemplate.opsForHash().put(key, hashKey, dataFromDBInJson);
        } catch (Exception e) {
            metrics.updateErrors();
            log.error("Error occurred while updating service signals for service:{}, signalId:{}.", serviceIds, signalId, e);
        }
    }

    /**
     * Retrieves the open signals for a specific application from Redis.
     *
     * @param appIdentifier The identifier of the application.
     * @return A map where keys are service IDs and values are maps of signal IDs and their start times.
     */
    public Map<String, Map<String, Long>> getOpenSignalsForApp(String appIdentifier) {
        String key = "/applications/" + appIdentifier + "/signals/open";
        String hashKey = "APPLICATIONS_" + appIdentifier + "_SIGNALS_OPEN";
        try {

            Object obj = redisTemplate.opsForHash().get(key, hashKey);
            if (obj == null) {
                log.debug("Open signals is unavailable in redis.");
                metrics.updateRedisKeysNotFound();
                metrics.updateSnapshots(key, 1);
                return new HashMap<>();
            }

            return objectMapper.readValue(obj.toString(), new TypeReference<>() {
            });
        } catch (Exception e) {
            metrics.updateErrors();
            log.error("Error occurred while getting open signals from redis.", e);
            return new HashMap<>();
        }
    }

    public void updateAnomalyForSignal(String signalId, Set<AnomalySummary> anomalySummaries) {
        String key = "/signals/" + signalId + "/anomalies";
        String hashKey = "SIGNALS_" + signalId + "_ANOMALIES";
        try {
            String dataFromDBInJson = objectMapper.writeValueAsString(anomalySummaries);
            redisTemplate.opsForHash().put(key, hashKey, dataFromDBInJson);
        } catch (Exception e) {
            metrics.updateErrors();
            log.error("Error occurred while updating latest anomaly event for signalId:{}.", signalId, e);
        }
    }

    public Set<AnomalySummary> getSignalAnomalies(String signalId) {
        String key = "/signals/" + signalId + "/anomalies";
        String hashKey = "SIGNALS_" + signalId + "_ANOMALIES";
        try {
            Object obj = redisTemplate.opsForHash().get(key, hashKey);
            if (obj == null) {
                log.debug("Signal anomalies are unavailable for signalId:{}.", signalId);
                metrics.updateRedisKeysNotFound();
                metrics.updateSnapshots(key, 1);
                return new HashSet<>();
            }

            return objectMapper.readValue(obj.toString(), new TypeReference<>() {
            });
        } catch (Exception e) {
            metrics.updateErrors();
            log.error("Error occurred while getting latest anomalies for signalId:{}.", signalId, e);
            return new HashSet<>();
        }
    }

    public SignalSummary getSignalSummary(String signalId) {
        String key = "/signals/" + signalId + "/summary";
        String hashKey = "SIGNALS_" + signalId + "_SUMMARY";
        try {
            Object obj = redisTemplate.opsForHash().get(key, hashKey);
            if (obj == null) {
                log.debug("Signal summary detail unavailable for signalId:{}.", signalId);
                metrics.updateRedisKeysNotFound();
                metrics.updateSnapshots(key, 1);
                return null;
            }

            return objectMapper.readValue(obj.toString(), new TypeReference<>() {
            });
        } catch (Exception e) {
            metrics.updateErrors();
            log.error("Error occurred while getting signal summary detail for signalId:{}.",
                    signalId, e);
            return null;
        }
    }

    public void updateSignalSummary(String signalId, SignalSummary signalSummary) {
        String key = "/signals/" + signalId + "/summary";
        String hashKey = "SIGNALS_" + signalId + "_SUMMARY";
        try {
            String dataFromDBInJson = objectMapper.writeValueAsString(signalSummary);
            redisTemplate.opsForHash().put(key, hashKey, dataFromDBInJson);
        } catch (Exception e) {
            metrics.updateErrors();
            log.error("Error occurred while updating signal detail for signalId:{}", signalId, e);
        }
    }

    public Set<SignalServiceSummary> getSignalServiceSummary(String signalId) {
        Object obj = null;
        try {
            obj = redisTemplate.opsForHash().get("/signals/" + signalId + "/services/summary",
                    "SIGNALS_" + signalId + "_SERVICES_SUMMARY");

            if (obj == null) {
                log.debug("Signal services summary is unavailable for signalId:{}.", signalId);
                metrics.updateRedisKeysNotFound();
                metrics.updateSnapshots("/signals/" + signalId + "/summary", 1);
                return Collections.emptySet();
            }
            return objectMapper.readValue(obj.toString(), new TypeReference<>() {
            });
        } catch (JsonProcessingException e) {
            log.error("Error while converting signal service summary JSON string for signalId:{}. " +
                    "Redis data:{}.", signalId, obj, e);
            metrics.updateErrors();
            return Collections.emptySet();
        } catch (Exception e) {
            metrics.updateErrors();
            log.error("Error occurred while getting signal service summary for signalId:{}.", signalId, e);
            return Collections.emptySet();
        }
    }

    public void updateServicesSummaryForSignal(String signalId, Set<SignalServiceSummary> serviceSummary) {
        try {
            String dataFromDBInJson = objectMapper.writeValueAsString(serviceSummary);
            redisTemplate.opsForHash().put("/signals/" + signalId + "/services/summary",
                    "SIGNALS_" + signalId + "_SERVICES_SUMMARY", dataFromDBInJson);
        } catch (Exception e) {
            metrics.updateErrors();
            log.error("Error occurred while updating services summary for signalId:{}, anomaly:{}.",
                    signalId, serviceSummary, e);
        }
    }

    public Set<SignalInstanceSummary> getSignalInstanceSummary(String signalId) {
        Object obj = null;
        try {
            obj = redisTemplate.opsForHash().get("/signals/" + signalId + "/instances/summary",
                    "SIGNALS_" + signalId + "_INSTANCES_SUMMARY");

            if (obj == null) {
                log.debug("Signal instances summary is unavailable for signalId:{}.", signalId);
                metrics.updateRedisKeysNotFound();
                metrics.updateSnapshots("/signals/" + signalId + "/instances/summary", 1);
                return Collections.emptySet();
            }
            return objectMapper.readValue(obj.toString(), new TypeReference<>() {
            });
        } catch (JsonProcessingException e) {
            log.error("Error while converting signal instances summary JSON string for signalId:{}. " +
                    "Redis data:{}.", signalId, obj, e);
            metrics.updateErrors();
            return Collections.emptySet();
        } catch (Exception e) {
            metrics.updateErrors();
            log.error("Error occurred while getting signal instances summary for signalId:{}.", signalId, e);
            return Collections.emptySet();
        }
    }

    public void updateInstancesSummaryForSignal(String signalId, Set<SignalInstanceSummary> instancesSummary) {
        try {
            String dataFromDBInJson = objectMapper.writeValueAsString(instancesSummary);
            redisTemplate.opsForHash().put("/signals/" + signalId + "/instances/summary",
                    "SIGNALS_" + signalId + "_INSTANCES_SUMMARY", dataFromDBInJson);
        } catch (Exception e) {
            metrics.updateErrors();
            log.error("Error occurred while updating instances summary for signalId:{}, anomaly:{}.",
                    signalId, instancesSummary, e);
        }
    }

    public Set<SignalTransactionSummary> getSignalTransactionSummary(String signalId) {
        Object obj = null;
        try {
            obj = redisTemplate.opsForHash().get("/signals/" + signalId + "/transactions/summary",
                    "SIGNALS_" + signalId + "_TRANSACTIONS_SUMMARY");

            if (obj == null) {
                log.debug("Signal transaction summary is unavailable for signalId:{}.", signalId);
                metrics.updateRedisKeysNotFound();
                metrics.updateSnapshots("/signals/" + signalId + "/transactions/summary", 1);
                return Collections.emptySet();
            }
            return objectMapper.readValue(obj.toString(), new TypeReference<>() {
            });
        } catch (JsonProcessingException e) {
            log.error("Error while converting signal transactions summary JSON string for signalId:{}. " +
                    "Redis data:{}.", signalId, obj, e);
            metrics.updateErrors();
            return Collections.emptySet();
        } catch (Exception e) {
            metrics.updateErrors();
            log.error("Error occurred while getting signal transactions summary for signalId:{}.", signalId, e);
            return Collections.emptySet();
        }
    }

    public void updateTransactionsSummaryForSignal(String signalId, Set<SignalTransactionSummary> txnsSummary) {
        try {
            String dataFromDBInJson = objectMapper.writeValueAsString(txnsSummary);
            redisTemplate.opsForHash().put("/signals/" + signalId + "/transactions/summary",
                    "SIGNALS_" + signalId + "_TRANSACTIONS_SUMMARY", dataFromDBInJson);
        } catch (Exception e) {
            metrics.updateErrors();
            log.error("Error occurred while updating transactions summary for signalId:{}, anomaly:{}.",
                    signalId, txnsSummary, e);
        }
    }

    public Map<Integer, List<BasicEntity>> getAccountOutbounds(String accountIdentifier) {
        String key = "/accounts/" + accountIdentifier + "/outbounds";
        String hashKey = "ACCOUNTS_" + accountIdentifier + "_OUTBOUNDS";
        try {
            Object obj = redisTemplate.opsForHash().get(key, hashKey);
            if (obj == null) {
                log.debug("Service outbounds are unavailable for accountId:{}.",accountIdentifier);
                metrics.updateRedisKeysNotFound();
                metrics.updateSnapshots(key, 1);
                return new HashMap<>();
            }

            return objectMapper.readValue(obj.toString(), new TypeReference<>() {});
        } catch (Exception e) {
            metrics.updateErrors();
            log.error("Error occurred while getting outbounds for accountId:{}.", accountIdentifier, e);
            return new HashMap<>();
        }
    }


    public List<TenantOpenSearchDetails> getTenantOpenSearchDetails(String tenantIdentifier) {
        try {
            String key = "/tenants/" + tenantIdentifier + "/opensearch";
            String hashKey = "TENANTS_" + tenantIdentifier + "_OPENSEARCH";

            Object obj = redisTemplate.opsForHash().entries(key).get(hashKey);
            if (obj == null) {
                log.error("Tenant details unavailable for tenant identifier [{}]", tenantIdentifier);
                metrics.updateRedisKeysNotFound();
                metrics.updateSnapshots(key, 1);
                return new ArrayList<>();
            }

            return objectMapper.readValue(obj.toString(), new TypeReference<>() {
            });
        } catch (Exception e) {
            log.error("Error occurred while getting to tenant opensearch mapping details for tenant identifier [{}]. ", tenantIdentifier, e);
            return new ArrayList<>();
        }
    }

    public List<OSIndexZoneDetails> getHealIndexZones() {
        try {
            String key = "/heal/index/zones";
            String hashKey = "HEAL_INDEX_ZONES";

            Object obj = redisTemplate.opsForHash().entries(key).get(hashKey);
            if (obj == null) {
                log.error("Heal opensearch index to zone mapping unavailable");
                metrics.updateRedisKeysNotFound();
                metrics.updateSnapshots(key, 1);
                return Collections.emptyList();
            }

            return objectMapper.readValue(obj.toString(), new TypeReference<>() {
            });
        } catch (Exception e) {
            log.error("Error occurred while getting Heal opensearch index to zone mapping.", e);
            return Collections.emptyList();
        }
    }

    public SignalDetails getSignalDetails(String signalId) {
        try {
            String key = "/signals/" + signalId + "/details";
            String hashKey = "SIGNALS_" + signalId + "_DETAILS";

            Object obj = redisTemplate.opsForHash().entries(key).get(hashKey);
            if (obj == null) {
                log.error("No signal details for signal identifier [{}] available", signalId);
                metrics.updateRedisKeysNotFound();
                metrics.updateSnapshots(key, 1);
                return null;
            }

            return objectMapper.readValue(obj.toString(), new TypeReference<>() {
            });
        } catch (Exception e) {
            metrics.updateSignalDetailsKeyErrorsGET();
            log.error("Error occurred while getting signal details for signal identifier [{}].", signalId, e);
            return null;
        }
    }

    public void updateSignalDetails(SignalDetails signalDetail) {
        try {
            String dataFromDBInJson = objectMapper.writeValueAsString(signalDetail);
            redisTemplate.opsForHash().put("/signals/" + signalDetail.getSignalId() + "/details",
                    "SIGNALS_" + signalDetail.getSignalId() + "_DETAILS", dataFromDBInJson);
        } catch (Exception e) {
            metrics.updateSignalDetailsKeyErrorsPUT();
            metrics.updateErrors();
            log.error("Error occurred while updating signal details for signalId:{}.", signalDetail.getSignalId(), e);
        }
    }

    public void deleteSignalDetails(String signalId) {
        try {
            redisTemplate.opsForHash().delete("/signals/" + signalId + "/details", "SIGNALS_" + signalId + "_DETAILS");
        } catch (Exception e) {
            metrics.updateSignalDetailsKeyErrorsDELETE();
            metrics.updateErrors();
            log.error("Error occurred while deleting signal details key for signalId:{}.", signalId, e);
        }
    }

    /**
     * Retrieves a list of applications mapped to a specific service in a given account.
     *
     * @param accIdentifier      The account identifier.
     * @param serviceIdentifier  The service identifier.
     * @return A list of BasicEntity representing the applications mapped to the service.
     */
    public List<BasicEntity> getApplicationsMappedToService(String accIdentifier, String serviceIdentifier) {
        try {
            String key = "/accounts/" + accIdentifier + "/services/" + serviceIdentifier + "/applications";
            String hashKey = "ACCOUNTS_" + accIdentifier + "_SERVICES_" + serviceIdentifier + "_APPLICATIONS";

            Object obj = redisTemplate.opsForHash().entries(key).get(hashKey);
            if (obj == null) {
                log.error("Mapped Application details unavailable for service identifier [{}] mapped to account [{}]", serviceIdentifier, accIdentifier);
                metrics.updateRedisKeysNotFound();
                metrics.updateSnapshots("/accounts/" + accIdentifier + "/services/" + serviceIdentifier + "/applications", 1);
                return new ArrayList<>();
            }
            return objectMapper.readValue(obj.toString(), new TypeReference<>() {
            });
        } catch (Exception e) {
            log.error("Error while getting mapped application details for service identifier [{}] mapped to account [{}]. Details: ", serviceIdentifier, accIdentifier, e);
            return new ArrayList<>();
        }
    }
}
