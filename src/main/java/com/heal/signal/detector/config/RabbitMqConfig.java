package com.heal.signal.detector.config;

import com.heal.signal.detector.service.ReceiverFromQueue;
import com.heal.signal.detector.util.Constants;
import com.heal.signal.detector.util.HealthMetrics;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.*;
import org.springframework.amqp.rabbit.connection.CachingConnectionFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.connection.RabbitConnectionFactoryBean;
import org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer;
import org.springframework.amqp.rabbit.listener.adapter.MessageListenerAdapter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.amqp.RabbitProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;

import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.Objects;

@Configuration
@Slf4j
public class RabbitMqConfig {

    //this queue used to read the event messages
    @Value("${anomaly.messages.queue.name:anomaly-event-signal-messages}")
    public String anomalyMessages;

    //this queue used to send the signal messages to notification processor
    @Value("${signal.messages.queue.name:signal-messages}")
    public String signalMessagesQueue;

    @Value("${spring.rabbitmq.verify.host.name:false}")
    public boolean verifyHostName;

    @Value("${spring.rabbitmq.anomalySignalMessages.prefetchCount:100}")
    public int prefetchCountAnomalySignalMessages;

    @Value("${spring.rabbitmq.anomalySignalMessages.acknowledgementMode:AUTO}")
    public String ackModeAnomalySignalMessages;

    @Value("${spring.rabbitmq.anomalySignalMessages.concurrentConsumerSize:1}")
    public int consumerSizeAnomalySignalMessages;

    @Value("${mle.signal.messages.queue.name:mle-signal-messages}")
    public String mleSignalMessages;

    @Value("${aiops.signal.messages.queue.name:signal-messages-aiops}")
    public String signalMessagesQueueAIOPS;


    @Autowired
    private HealthMetrics metrics;

    @Lazy
    @Autowired
    ReceiverFromQueue receiver;

    @Qualifier("signalInputQueue")
    @Bean
    public Queue createSignalInputQueue() {
        metrics.setReadSignalQueueName(anomalyMessages);
        return new Queue(anomalyMessages, Constants.IS_QUEUE_DURABLE, Constants.IS_QUEUE_EXCLUSIVE, Constants.QUEUE_AUTO_DELETE, null) ;
    }

    @Bean
    @Qualifier("mleSignalQueue")
    public Queue createExternalAnomalyQueue() {
        metrics.setExternalSignalInputQueueName(mleSignalMessages);
        return new Queue(mleSignalMessages, Constants.IS_QUEUE_DURABLE, Constants.IS_QUEUE_EXCLUSIVE, Constants.QUEUE_AUTO_DELETE, null);
    }

    @Qualifier("aiOpsSignalOutputQueue")
    @Bean
    public Queue createAIOpsSignalOutputQueue() {
        metrics.setWriteAIOPsSignalQueueName(signalMessagesQueueAIOPS);
        return new Queue(signalMessagesQueueAIOPS, Constants.IS_QUEUE_DURABLE, Constants.IS_QUEUE_EXCLUSIVE, Constants.QUEUE_AUTO_DELETE, null) ;
    }

    @Qualifier("signalOutputQueue")
    @Bean
    public Queue createSignalOutputQueue() {
        metrics.setWriteSignalQueueName(signalMessagesQueue);
        return new Queue(signalMessagesQueue, Constants.IS_QUEUE_DURABLE, Constants.IS_QUEUE_EXCLUSIVE, Constants.QUEUE_AUTO_DELETE, null) ;
    }

    @Bean
    @Qualifier("signalFanoutExchange")
    FanoutExchange exchange() {
        return new FanoutExchange("SIGNAL_MESSAGES");
    }

    @Bean
    public Binding bindingSignalQueue(@Qualifier("signalFanoutExchange") FanoutExchange exchange, @Qualifier(
            "aiOpsSignalOutputQueue") Queue queue) {
        return BindingBuilder.bind(queue).to(exchange);
    }

    @Bean
    public Binding bindingAIOPsSignalQueue(@Qualifier("signalFanoutExchange") FanoutExchange exchange, @Qualifier(
            "signalOutputQueue") Queue queue) {
        return BindingBuilder.bind(queue).to(exchange);
    }

    @Bean
    public ConnectionFactory createSslConnectionFactory(RabbitProperties rabbitProperties) {
        RabbitConnectionFactoryBean factory = new RabbitConnectionFactoryBean();
        if (rabbitProperties.getUsername() != null) {
            factory.setUsername(rabbitProperties.getUsername());
        }
        if (rabbitProperties.getPassword() != null && !rabbitProperties.getPassword().trim().isEmpty()) {
            factory.setPassword(new String(Base64.getDecoder().decode(rabbitProperties.getPassword().trim()), StandardCharsets.UTF_8));
        }

        RabbitProperties.Ssl ssl = rabbitProperties.getSsl();
        if (ssl.getEnabled()) {
            factory.setUseSSL(true);
            factory.setEnableHostnameVerification(verifyHostName);
            factory.setSslAlgorithm(ssl.getAlgorithm());
        }
        factory.setAutomaticRecoveryEnabled(true);
        factory.afterPropertiesSet();

        CachingConnectionFactory connectionFactory = null;
        try {
            connectionFactory = new CachingConnectionFactory(Objects.requireNonNull(factory.getRabbitConnectionFactory()));
            connectionFactory.setAddresses(rabbitProperties.getAddresses());
        } catch (Exception e) {
            metrics.updateErrors();
            log.error("Exception occurred while creating ConnectionFactory. RabbitMQ Addresses:{}, Username:{}, IsSSL:{}, Algorithm:{}",
                    rabbitProperties.getAddresses(), rabbitProperties.getUsername(),
                    rabbitProperties.getSsl().getEnabled(), rabbitProperties.getSsl().getAlgorithm(), e);
        }

        return connectionFactory;
    }

    @Qualifier("anomalyEventSignalMessagesDataContainer")
    @Bean
    SimpleMessageListenerContainer aggregatedKpiContainer(ConnectionFactory connectionFactory) {
        SimpleMessageListenerContainer container = new SimpleMessageListenerContainer();
        container.setConnectionFactory(connectionFactory);
        container.setQueueNames(anomalyMessages);
        container.setMessageListener(new MessageListenerAdapter(receiver, "receiveAnomalyEventData"));
        container.setAcknowledgeMode(AcknowledgeMode.valueOf(ackModeAnomalySignalMessages));
        container.setConcurrentConsumers(consumerSizeAnomalySignalMessages);
        container.setPrefetchCount(prefetchCountAnomalySignalMessages);
        container.afterPropertiesSet();
        return container;
    }

    @Qualifier("mleIncidentMessagesDataContainer")
    @Bean
    SimpleMessageListenerContainer mleIncidentsContainer(ConnectionFactory connectionFactory) {
        SimpleMessageListenerContainer container = new SimpleMessageListenerContainer();
        container.setConnectionFactory(connectionFactory);
        container.setQueueNames(mleSignalMessages);
        container.setMessageListener(new MessageListenerAdapter(receiver, "receiveIncidentFromMLE"));
        container.setAcknowledgeMode(AcknowledgeMode.valueOf(ackModeAnomalySignalMessages));
        container.setConcurrentConsumers(consumerSizeAnomalySignalMessages);
        container.setPrefetchCount(prefetchCountAnomalySignalMessages);
        container.afterPropertiesSet();
        return container;
    }
}