package com.heal.signal.detector.config;

import com.github.benmanes.caffeine.cache.Caffeine;
import com.heal.signal.detector.util.Constants;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.CacheManager;
import org.springframework.cache.caffeine.CaffeineCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.TimeUnit;

@Configuration
public class CacheConfig {

    @Value("${accounts.configuration.cache.expire.interval.minutes:5}")
    private int accountsCacheExpireTime;

    @Value("${accounts.configuration.cache.max.size:100}")
    private int accountsCacheMaxSize;

    @Value("${application.configuration.cache.expire.interval.minutes:5}")
    private int applicationCacheExpireTime;

    @Value("${application.configuration.cache.max.size:1000}")
    private int applicationCacheMaxSize;

    @Value("${component.kpis.configuration.cache.expire.interval.minutes:5}")
    private int componentKPIsCacheExpireTime;

    @Value("${component.kpis.configuration.cache.max.size:5000}")
    private int componentKPIsCacheMaxSize;

    @Value("${component.kpi.configuration.cache.expire.interval.minutes:5}")
    private int componentKPICacheExpireTime;

    @Value("${component.kpi.configuration.cache.max.size:5000}")
    private int componentKPICacheMaxSize;

    @Value("${service.configuration.cache.expire.interval.minutes:5}")
    private int serviceCacheExpireTime;

    @Value("${service.configuration.cache.max.size:5000}")
    private int serviceCacheMaxSize;

    @Value("${service.maintenance.configuration.cache.expire.interval.minutes:5}")
    private int serviceMaintenanceCacheExpireTime;

    @Value("${service.maintenance.configuration.cache.max.size:5000}")
    private int serviceMaintenanceCacheMaxSize;

    @Value("${instance.maintenance.configuration.cache.expire.interval.minutes:5}")
    private int instanceMaintenanceCacheExpireTime;

    @Value("${instance.maintenance.configuration.cache.max.size:5000}")
    private int instanceMaintenanceCacheMaxSize;

    @Value("${instance.kpis.configuration.cache.expire.interval.minutes:5}")
    private int instanceKpisCacheExpireTime;

    @Value("${instance.kpis.configuration.cache.max.size:5000}")
    private int instanceKpisCacheMaxSize;

    @Value("${service.neighbours.configuration.cache.expire.interval.minutes:5}")
    private int serviceNeighboursCacheExpireTime;

    @Value("${service.neighbours.configuration.cache.max.size:5000}")
    private int serviceNeighboursCacheMaxSize;

    @Value("${heal.types.configuration.cache.expire.interval.minutes:5}")
    private int healTypesCacheExpireTime;

    @Value("${heal.types.configuration.cache.max.size:5000}")
    private int healTypesCacheMaxSize;

    @Value("${tenants.configuration.cache.expire.interval.minutes:5}")
    private int tenantsCacheExpireTime;

    @Value("${tenants.configuration.cache.max.size:5000}")
    private int tenantsCacheMaxSize;

    @Bean
    public CacheManager cacheManager() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager();
        cacheManager.registerCustomCache(Constants.ACCOUNTS, createCacheConfig(accountsCacheMaxSize, accountsCacheExpireTime).build());
        cacheManager.registerCustomCache(Constants.ACCOUNT_OUTBOUNDS, createCacheConfig(accountsCacheMaxSize, accountsCacheExpireTime).build());
        cacheManager.registerCustomCache(Constants.APPLICATION_BY_IDENTIFIER, createCacheConfig(applicationCacheMaxSize, applicationCacheExpireTime).build());
        cacheManager.registerCustomCache(Constants.COMPONENT_KPIS, createCacheConfig(componentKPIsCacheMaxSize, componentKPIsCacheExpireTime).build());
        cacheManager.registerCustomCache(Constants.COMPONENT_KPIS_BY_IDENTIFIER, createCacheConfig(componentKPICacheMaxSize, componentKPICacheExpireTime).build());
        cacheManager.registerCustomCache(Constants.SERVICE_BY_IDENTIFIER, createCacheConfig(serviceCacheMaxSize, serviceCacheExpireTime).build());
        cacheManager.registerCustomCache(Constants.SERVICE_MAINTENANCE_BY_IDENTIFIER, createCacheConfig(serviceMaintenanceCacheMaxSize, serviceMaintenanceCacheExpireTime).build());
        cacheManager.registerCustomCache(Constants.INSTANCE_MAINTENANCE_BY_IDENTIFIER, createCacheConfig(instanceMaintenanceCacheMaxSize, instanceMaintenanceCacheExpireTime).build());
        cacheManager.registerCustomCache(Constants.INSTANCE_KPIS_BY_IDENTIFIER, createCacheConfig(instanceKpisCacheMaxSize, instanceKpisCacheExpireTime).build());
        cacheManager.registerCustomCache(Constants.SERVICE_NEIGHBOURS_BY_IDENTIFIER, createCacheConfig(serviceNeighboursCacheMaxSize, serviceNeighboursCacheExpireTime).build());
        cacheManager.registerCustomCache(Constants.HEAL_TYPES, createCacheConfig(healTypesCacheMaxSize, healTypesCacheExpireTime).build());
        cacheManager.registerCustomCache(Constants.TENANTS, createCacheConfig(tenantsCacheMaxSize, tenantsCacheExpireTime).build());
        cacheManager.registerCustomCache(Constants.ACCOUNT_SERVICES, createCacheConfig(accountsCacheMaxSize, accountsCacheExpireTime).build());
        cacheManager.setAllowNullValues(false);
        return cacheManager;
    }

    private Caffeine<Object, Object> createCacheConfig(int maxSize, int expireTime) {
        return Caffeine.newBuilder()
                .initialCapacity(100)
                .maximumSize(maxSize)
                .expireAfterWrite(expireTime, TimeUnit.MINUTES);
    }
}
