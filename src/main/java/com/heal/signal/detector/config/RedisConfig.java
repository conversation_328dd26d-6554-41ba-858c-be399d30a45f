package com.heal.signal.detector.config;

import io.lettuce.core.ClientOptions;
import io.lettuce.core.cluster.ClusterClientOptions;
import io.lettuce.core.cluster.ClusterTopologyRefreshOptions;
import io.lettuce.core.protocol.ProtocolVersion;
import io.lettuce.core.resource.DefaultClientResources;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.connection.RedisClusterConfiguration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceClientConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import javax.annotation.PreDestroy;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.Base64;
import java.util.List;

@Configuration
@Slf4j
public class RedisConfig {

    @Value("${spring.redis.cluster.nodes:localhost:6379}")
    private List<String> clusterNodes;

    @Value("${spring.redis.ssl:true}")
    private boolean sslEnabled;

    @Value("${spring.redis.username:}")
    private String username;

    @Value("${spring.redis.password:}")
    private String password;

    @Value("${spring.redis.cluster.mode:true}")
    private boolean redisClusterMode;

    @Lazy
    @Autowired
    RedisConnectionFactory redisConnectionFactory;

    @Autowired
    DefaultClientResources defaultClientResources;
    @Bean
    public RedisConnectionFactory redisConnectionFactory() {
        log.info("Factory method called with cluster mode as {}", redisClusterMode);

        LettuceConnectionFactory factory;
        if (redisClusterMode) {
            RedisClusterConfiguration configuration = new RedisClusterConfiguration(clusterNodes);

            if (username != null && !username.trim().isEmpty()) {
                configuration.setUsername(username);
            }

            if (password != null && !password.trim().isEmpty()) {
                configuration.setPassword(new String(Base64.getDecoder().decode(password), StandardCharsets.UTF_8));
            }

            ClientOptions clientOptions = ClusterClientOptions.builder()
                    .autoReconnect(true)
                    .protocolVersion(ProtocolVersion.RESP3)
                    .validateClusterNodeMembership(true)
                    .maxRedirects(5)
                    .cancelCommandsOnReconnectFailure(true)
                    .pingBeforeActivateConnection(true)
                    .topologyRefreshOptions(ClusterTopologyRefreshOptions.builder()
                            .enablePeriodicRefresh()
                            .build())
                    .build();

            if (sslEnabled) {
                LettuceClientConfiguration clientConfig = LettuceClientConfiguration.builder()
                        .commandTimeout(Duration.ofSeconds(5))
                        .clientOptions(clientOptions)
                        .useSsl()
                        .build();

                factory = new LettuceConnectionFactory(configuration, clientConfig);
                factory.isUseSsl();
            } else {
                LettuceClientConfiguration clientConfig = LettuceClientConfiguration.builder()
                        .commandTimeout(Duration.ofSeconds(5))
                        .clientOptions(clientOptions)
                        .build();

                factory = new LettuceConnectionFactory(configuration, clientConfig);
            }

        } else {
            RedisStandaloneConfiguration configuration = new RedisStandaloneConfiguration();

            if (username != null && !username.trim().isEmpty()) {
                configuration.setUsername(username);
            }

            if (password != null && !password.trim().isEmpty()) {
                configuration.setPassword(new String(Base64.getDecoder().decode(password), StandardCharsets.UTF_8));
            }

            String[] hostAndPort = clusterNodes.get(0).split(":");

            configuration.setHostName(hostAndPort[0]);
            configuration.setPort(Integer.parseInt(hostAndPort[1]));

            ClientOptions clientOptions = ClusterClientOptions.builder()
                    .autoReconnect(true)
                    .protocolVersion(ProtocolVersion.RESP2)
                    .cancelCommandsOnReconnectFailure(true)
                    .pingBeforeActivateConnection(true)
                    .build();

            if (sslEnabled) {
                LettuceClientConfiguration clientConfig = LettuceClientConfiguration.builder()
                        .commandTimeout(Duration.ofSeconds(5))
                        .clientOptions(clientOptions)
                        .useSsl()
                        .build();

                factory = new LettuceConnectionFactory(configuration, clientConfig);
                factory.isUseSsl();
            } else {
                LettuceClientConfiguration clientConfig = LettuceClientConfiguration.builder()
                        .commandTimeout(Duration.ofSeconds(5))
                        .clientOptions(clientOptions)
                        .build();

                factory = new LettuceConnectionFactory(configuration, clientConfig);
            }
        }

        factory.setValidateConnection(false);
        log.debug("Redis configuration factory for nodes:{}, username:{}.", clusterNodes, username);

        this.redisConnectionFactory = factory;
        return factory;
    }

    @Bean
    public RedisTemplate<String, Object> redisTemplate() {
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(redisConnectionFactory);
        template.setKeySerializer(new StringRedisSerializer());
        template.setHashKeySerializer(new StringRedisSerializer());
        template.setValueSerializer(new StringRedisSerializer());
        template.setHashValueSerializer(new StringRedisSerializer());
        template.setEnableTransactionSupport(true);
        template.afterPropertiesSet();
        return template;
    }

    @PreDestroy
    public void shutdown() {
        if (redisConnectionFactory != null) {
            if (redisClusterMode)
                redisConnectionFactory.getClusterConnection().shutdown();
            else
                redisConnectionFactory.getConnection().shutdown();
        }
        defaultClientResources.shutdown();
    }
}
