package com.heal.signal.detector.config;

import org.jetbrains.annotations.NotNull;

import java.util.concurrent.ThreadFactory;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 */
public class OpenSearchClientThreadFactory implements ThreadFactory {

    private final String namePrefix;
    private final AtomicInteger threadNumber = new AtomicInteger(1);

    public OpenSearchClientThreadFactory(String namePrefix) {
        this.namePrefix = namePrefix;
    }

    @Override
    public Thread newThread(@NotNull Runnable runnable) {
        Thread t = new Thread(runnable, namePrefix + threadNumber.getAndIncrement());
        t.setDaemon(true);  // Mark the thread as a daemon
        return t;
    }
}
