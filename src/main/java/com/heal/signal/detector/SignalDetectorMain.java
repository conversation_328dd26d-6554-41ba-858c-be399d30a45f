package com.heal.signal.detector;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Profile;
import org.springframework.context.annotation.PropertySource;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

@SpringBootApplication
@ComponentScan
@PropertySource(value= "classpath:conf.properties")
@EnableScheduling
@Profile("!test")
@EnableAsync
@EnableCaching
@Slf4j
public class SignalDetectorMain {
    public static void main(String[] args) {
        log.info("Starting signal-detector.....");
        System.setProperty("logging.config", "classpath:logback.xml");
        SpringApplication.run(SignalDetectorMain.class, args);
        log.info("Signal-detector started.....");
    }
}