package com.heal.signal.detector.opensearch;


import com.appnomic.appsone.opeasearchquery.helper.OpenSearchQueryHelper;
import com.appnomic.appsone.opeasearchquery.queryopts.Documents;
import com.appnomic.appsone.opeasearchquery.queryopts.QueryOptions;
import com.appnomic.appsone.opeasearchquery.results.NameValuePair;
import com.appnomic.appsone.opeasearchquery.results.RawDocumentResults;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.heal.configuration.enums.SignalType;
import com.heal.configuration.pojos.AnomalySummary;
import com.heal.configuration.pojos.opensearch.Anomalies;
import com.heal.configuration.pojos.opensearch.SignalDetails;
import com.heal.configuration.util.DateHelper;
import com.heal.signal.detector.config.OpenSearchConfig;
import com.heal.signal.detector.pojos.AnomalyHelper;
import com.heal.signal.detector.pojos.SignalStatus;
import com.heal.signal.detector.process.SignalDetailProcessor;
import com.heal.signal.detector.scheduler.OSDataPushScheduler;
import com.heal.signal.detector.util.Commons;
import com.heal.signal.detector.util.HealthMetrics;
import com.heal.signal.detector.util.RedisUtilities;
import lombok.extern.slf4j.Slf4j;
import org.opensearch.client.opensearch.OpenSearchClient;
import org.opensearch.client.opensearch._types.OpenSearchException;
import org.opensearch.client.opensearch._types.Result;
import org.opensearch.client.opensearch.core.IndexRequest;
import org.opensearch.client.opensearch.core.IndexResponse;
import org.opensearch.client.opensearch.core.UpdateRequest;
import org.opensearch.client.opensearch.core.UpdateResponse;
import org.opensearch.client.opensearch.indices.RefreshRequest;
import org.opensearch.client.opensearch.indices.RefreshResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Repository;

import java.util.*;
import java.util.concurrent.TimeUnit;


@Repository
@Slf4j
public class SignalRepo {
    @Autowired
    private HealthMetrics healthMetrics;

    @Autowired
    private SignalDetailProcessor signalDetailProcessor;

    @Value("${opensearch.anomalies.index:heal_anomalies}")
    public String anomaliesIndex;

    @Value("${opensearch.signals.index:heal_signals}")
    public String signalsIndex;

    @Value("${opensearch.index.search.retry:3}")
    public int maxRetry;

    @Value("${opensearch.index.insert.retry:3}")
    public int insertRetry;

    @Value("${open.signal.from.time.offset.days:30}")
    public int openSignalOffset;

    @Autowired
    public OSDataPushScheduler scheduler;

    @Autowired
    public OpenSearchConfig openSearchConfig;

    @Autowired
    public RedisUtilities redisUtilities;

    @Autowired
    @Lazy
    public Commons commons;

    public Set<SignalDetails> getOpenSignals(String accountIdentifier, boolean isAddAccountFilter, boolean isMLEExclude) throws Exception {

        List<String> indexNames = new ArrayList<>();

        List<NameValuePair> matchFields = new ArrayList<>();
        matchFields.add(new NameValuePair("currentStatus", "OPEN"));
        if (isAddAccountFilter) {
            matchFields.add(new NameValuePair("accountIdentifiers", accountIdentifier));
        }

        QueryOptions.QueryOptionsBuilder queryOptionsBuilder;
        if (openSignalOffset == 0) {
            indexNames.add(signalsIndex + "_*");

            queryOptionsBuilder = QueryOptions.builder()
                    .indexNames(indexNames)
                    .isTimeSeriesData(false)
                    .matchAllFields(Optional.of(matchFields))
                    .fetchAllRecords(true);
//                    .allIndex(true);

        } else {
            long toTime = System.currentTimeMillis();
            long fromTime = toTime - TimeUnit.DAYS.toMillis(openSignalOffset);
            DateHelper.getWeeksAsString(fromTime, toTime).forEach(date ->
                    indexNames.add(signalsIndex + "_" + date));

            queryOptionsBuilder = QueryOptions.builder()
                    .indexNames(indexNames)
                    .epochFieldName("updatedTime")
                    .epochFromDate(fromTime)
                    .epochToDate(toTime)
                    .isTimeSeriesData(false)
                    .matchAllFields(Optional.of(matchFields))
                    .fetchAllRecords(true);
        }

        QueryOptions queryOptions;

        if (isMLEExclude) {
            List<NameValuePair> matchNoneFields = new ArrayList<>();
            matchNoneFields.add(new NameValuePair("metadata.Source", "MLE"));
            queryOptionsBuilder.matchNoneOfFields(Optional.of(new ArrayList<>() {{
                add(matchNoneFields);
            }}));
        }
        queryOptions = queryOptionsBuilder.build();

        try {
            log.debug("OS query for fetching open signals data: {}", queryOptions);
            Set<SignalDetails> signalDetails = new HashSet<>();
            ObjectMapper objectMapper = commons.getObjectMapperWithHtmlEncoder();
            for (int retry = insertRetry; retry > 0; retry--) {
                try {
                    OpenSearchClient openSearchClient = openSearchConfig.getOpenSearchClient(accountIdentifier, signalsIndex);
                    if (openSearchClient == null) {
                        log.error("Could not get open signal details because of open search connection issue. Retry:{}, accountIdentifier:{}", retry, accountIdentifier);
                        continue;
                    }

                    RawDocumentResults rawDocuments = OpenSearchQueryHelper.getRawDocuments(queryOptions, openSearchClient);
                    if (rawDocuments == null || rawDocuments.getDocuments() == null || rawDocuments.getDocuments().isEmpty()) {
                        return Collections.emptySet();
                    }

                    for (Documents hit : rawDocuments.getDocuments())
                        signalDetails.add(commons.getObjectMapperWithHtmlEncoder().readValue(hit.getSource(), SignalDetails.class));
                    break;
                } catch (OpenSearchException sto) {
                    log.error("OpenSearchException occurred while getting open signal details. Index:{}, retry:{}, accountIdentifier:{}", indexNames, retry, accountIdentifier, sto);
                    throw sto;
                } catch (Exception e) {
                    log.error("General exception occurred. Retrying. Index:{}, retry:{}, accountIdentifier:{}", indexNames, retry, accountIdentifier, e);
                }
            }
            return signalDetails;
        } catch (OpenSearchException e) {
            healthMetrics.updateErrors();
            log.error("Error in fetching data from index {}. Details: accountId:{}", indexNames, accountIdentifier, e);
            throw e;
        }
    }

    public boolean insertSignal(SignalDetails signalDetails, String accountIdentifier, AnomalySummary anomalySummary, boolean isAddSignalDetails, Set<String> appIds, boolean isWorkload, int anomalitySeverityId) {
        try {
            OpenSearchClient openSearchClient = openSearchConfig.getOpenSearchClient(accountIdentifier, signalsIndex);
            if (openSearchClient == null) {
                log.error("Could not insert into signal index because of open search connection issue. SignalId:{}, anomalyId:{}", signalDetails.getSignalId(), anomalySummary.getAnomalyId());
                return false;
            }

            signalDetails.setTimestamp(DateHelper.getDate(anomalySummary.getEventTime()));

            List<String> indexDates = DateHelper.getWeeksAsString(signalDetails.getStartedTime(), signalDetails.getStartedTime());
            if (indexDates == null || indexDates.isEmpty()) {
                log.error("Could not get the index names for signalId:{}, signalTime:{}, anomalyId:{}.", signalDetails.getSignalId(), signalDetails.getStartedTime(), anomalySummary.getAnomalyId());
                healthMetrics.updateErrors();
                return false;
            }

            String indexName = signalsIndex + "_" + indexDates.get(0);
            IndexRequest<Object> indexRequest = new IndexRequest.Builder<>()
                    .index(indexName)
                    .id(signalDetails.getSignalId())
                    .document(signalDetails)
                    .build();

            log.debug("severity id before pushing to os for signal id {} is {}", signalDetails.getSignalId(),
                    signalDetails.getSeverityId());
            log.debug("Query for inserting signal detail : {}", indexRequest);

            boolean result = false;

            for (int retry = insertRetry; retry > 0; retry--) {
                try {
                    openSearchClient = openSearchConfig.getOpenSearchClient(accountIdentifier, signalsIndex);
                    if (openSearchClient == null) {
                        log.error("Could not insert into signal index because of open search connection issue. Retry:{}, signalId:{}, anomalyId:{}", retry, signalDetails.getSignalId(), anomalySummary.getAnomalyId());
                        continue;
                    }

                    IndexResponse response = openSearchClient.index(indexRequest);
                    if (response.result() == Result.Created) {
                        result = true;
                    }
                    break;
                } catch (Exception e) {
                    log.error("Failed to insert into open search document for index:{}, retry:{}, signal:{}, anomalyId:{}", indexName, retry, signalDetails, anomalySummary.getAnomalyId(), e);
                }
            }

            if (result) {
                appIds.forEach(appId -> {
                    signalDetailProcessor.updateServiceSignals(signalDetails.getSignalId(), anomalySummary.getServiceId(),
                            signalDetails.getStartedTime(), signalDetails.getCurrentStatus(), appId);
                });
                signalDetailProcessor.updateSignalDetailsIntoRedis(signalDetails.getSignalId(), anomalySummary, appIds, isWorkload, anomalitySeverityId);
                if (isAddSignalDetails) {
                    signalDetailProcessor.updateSignalDetails(signalDetails, false);
                }

                return true;
            } else {
                log.error("Got error while inserting document into open search. Index:{}, signalId:{}, response:{}, max retried:{}, anomalyId:{}", indexName, signalDetails.getSignalId(), result, insertRetry, anomalySummary.getAnomalyId());
                healthMetrics.updateErrors();
                return false;
            }

        } catch (Exception e) {
            log.error("Error occurred while creating signal index create. signalDetails:{}, anomalyId:{}", signalDetails, anomalySummary.getAnomalyId(), e);
            healthMetrics.updateErrors();
            return false;
        }
    }

    public boolean updateSignal(long updatedTime, String currentStatus, String statusDetails,
                                Map<String, String> metaDataMap, String anomalyId, String signalId,
                                String accountIdentifier, int severityId, AnomalySummary anomalySummary,
                                long signalStartTime, boolean isAddSignalDetails, Set<String> appIds, boolean isWorkload, int anomalitySeverityId) {
        String indexName = null;
        try {
            OpenSearchClient openSearchClient = openSearchConfig.getOpenSearchClient(accountIdentifier, signalsIndex);
            if (openSearchClient == null) {
                log.error("Could not insert into signal index because of open search connection issue. SignalId:{}, anomalyId:{}", signalId, anomalyId);
                return false;
            }

            List<SignalDetails> signalDetailsList = new ArrayList<>();
            if (isAddSignalDetails) {
                SignalDetails signalDetails = redisUtilities.getSignalDetails(signalId);
                if (signalDetails != null) {
                    signalDetailsList.add(signalDetails);
                }
            } else {
                signalDetailsList = getSignalById(Collections.singleton(signalId), signalStartTime, System.currentTimeMillis(), accountIdentifier);
            }
            if (signalDetailsList == null || signalDetailsList.isEmpty()) {
                log.error("Error occurred while getting the signal details. signalId:{}, anomalyId:{}, startTime:{}, accountId:{}", signalId, anomalyId, signalStartTime, accountIdentifier);
                healthMetrics.updateErrors();
                return false;
            }

            SignalDetails signalDetails = signalDetailsList.get(0);
            signalDetails.getAnomalies().add(anomalyId);
            signalDetails.getStatusDetails().add(currentStatus);
            signalDetails.setUpdatedTime(updatedTime);
            signalDetails.setSeverityId(severityId);

            signalDetails.setAccountIdentifiers(new HashSet<>() {{
                add(accountIdentifier);
                if (signalDetails.getAccountIdentifiers() != null) {
                    addAll(signalDetails.getAccountIdentifiers());
                }
            }});

            signalDetails.getMetadata().putAll(metaDataMap);

            signalDetails.setTimestamp(DateHelper.getDate(anomalySummary.getEventTime()));
            signalDetails.getServiceIds().addAll(anomalySummary.getServiceId());

            List<String> indexDates = DateHelper.getWeeksAsString(signalDetails.getStartedTime(), signalDetails.getStartedTime());
            if (indexDates == null || indexDates.isEmpty()) {
                log.error("Could not get the index for signalId:{}, signalTime:{}, anomalyId:{}.", signalDetails.getSignalId(), signalDetails.getStartedTime(), anomalyId);
                healthMetrics.updateErrors();
                return false;
            }
            indexName = signalsIndex + "_" + indexDates.get(0);

            UpdateRequest<Object, Object> updateRequest = new UpdateRequest.Builder<>()
                    .index(indexName)
                    .id(signalId)
                    .doc(signalDetails)
                    .retryOnConflict(3)
                    .build();

            log.debug("Query for updating signal detail : {}", updateRequest);

            boolean queryResult = false;
            for (int retry = insertRetry; retry > 0; retry--) {
                try {
                    openSearchClient = openSearchConfig.getOpenSearchClient(accountIdentifier, signalsIndex);
                    if (openSearchClient == null) {
                        log.error("Could not insert into signal index because of open search connection issue. Retry:{}, signalId:{}, anomalyId:{}", retry, signalDetails.getSignalId(), anomalyId);
                        continue;
                    }

                    UpdateResponse<Object> response = openSearchClient.update(updateRequest, Object.class);

                    if (response.result() == Result.Updated) {
                        queryResult = true;
                    }

                    break;
                } catch (Exception e) {
                    log.error("Failed to update into open search document for index:{}, documentId:{}, retry:{}, signal:{}, anomalyId:{}", indexName, retry, signalId, signalDetails, anomalyId, e);
                }
            }

            if (queryResult) {
                appIds.forEach(appId -> {
                    signalDetailProcessor.updateServiceSignals(signalId, anomalySummary.getServiceId(), signalStartTime, currentStatus, appId);
                });
                signalDetailProcessor.updateSignalDetailsIntoRedis(signalId, anomalySummary, appIds, isWorkload, anomalitySeverityId);
                if (isAddSignalDetails) {
                    signalDetailProcessor.updateSignalDetails(signalDetails, false);
                }

                return true;
            } else {
                healthMetrics.updateErrors();
                log.error("Error occurred while updating document into open search for index:{}, documentId:{}, anomalyId:{}, max retried:{}", indexName, signalId, anomalyId, insertRetry);
                return false;
            }
        } catch (Exception e) {
            log.error("Error occurred while updating the signal details. indexNames:{}, signalId:{}, anomalyId:{}, accountId:{}, startTime:{}", indexName, signalId, anomalyId, accountIdentifier, signalStartTime, e);
            healthMetrics.updateErrors();
            return false;
        }
    }

    /**
     * Removes an anomaly from a signal's anomaly list and updates the signal in OpenSearch.
     * Returns true if update is successful, false otherwise.
     *
     * @param anomalyId The ID of the anomaly to be removed.
     * @param signalId The ID of the signal from which the anomaly is to be removed.
     * @param accountIdentifier The identifier for the account.
     * @param signalStartTime The start time of the signal.
     * @param currentStatus The current status of the signal.
     * @param serviceIdentifiers The set of service identifiers associated with the signal.
     * @param isAddSignalDetails Flag indicating whether to add signal details.
     * @param appIds The set of application IDs associated with the signal.
     * @return boolean indicating success or failure of the operation.
     */
    public boolean removeAnomalyFromSignal(String anomalyId, String signalId, String accountIdentifier, long signalStartTime, String currentStatus, Set<String> serviceIdentifiers, boolean isAddSignalDetails, Set<String> appIds) {
        String indexName = null;
        try {
            OpenSearchClient openSearchClient = openSearchConfig.getOpenSearchClient(accountIdentifier, signalsIndex);
            if (openSearchClient == null) {
                log.error("Could not update signal index because of open search connection issue. SignalId:{}, anomalyId:{}", signalId, anomalyId);
                return false;
            }

            List<SignalDetails> signalDetailsList = new ArrayList<>();
            if (isAddSignalDetails) {
                SignalDetails signalDetails = redisUtilities.getSignalDetails(signalId);
                if (signalDetails != null) {
                    signalDetailsList.add(signalDetails);
                }
            } else {
                signalDetailsList = getSignalById(Collections.singleton(signalId), signalStartTime, System.currentTimeMillis(), accountIdentifier);
            }
            if (signalDetailsList == null || signalDetailsList.isEmpty()) {
                log.error("Error occurred while getting the signal details. signalId:{}, anomalyId:{}, startTime:{}, accountId:{}", signalId, anomalyId, signalStartTime, accountIdentifier);
                healthMetrics.updateErrors();
                return false;
            }

            SignalDetails signalDetails = signalDetailsList.get(0);
            boolean removed = signalDetails.getAnomalies().remove(anomalyId);
            if (!removed) {
                log.warn("AnomalyId:{} not found in signalId:{} anomaly list.", anomalyId, signalId);
            }
            signalDetails.setUpdatedTime(System.currentTimeMillis());

            List<String> indexDates = DateHelper.getWeeksAsString(signalDetails.getStartedTime(), signalDetails.getStartedTime());
            if (indexDates == null || indexDates.isEmpty()) {
                log.error("Could not get the index for signalId:{}, signalTime:{}, anomalyId:{}.", signalDetails.getSignalId(), signalDetails.getStartedTime(), anomalyId);
                healthMetrics.updateErrors();
                return false;
            }
            indexName = signalsIndex + "_" + indexDates.get(0);

            UpdateRequest<Object, Object> updateRequest = new UpdateRequest.Builder<>()
                    .index(indexName)
                    .id(signalId)
                    .doc(signalDetails)
                    .retryOnConflict(3)
                    .build();

            log.debug("Query for removing anomaly from signal detail : {}", updateRequest);

            boolean queryResult = false;
            for (int retry = insertRetry; retry > 0; retry--) {
                try {
                    openSearchClient = openSearchConfig.getOpenSearchClient(accountIdentifier, signalsIndex);
                    if (openSearchClient == null) {
                        log.error("Could not update signal index because of open search connection issue. Retry:{}, signalId:{}, anomalyId:{}", retry, signalDetails.getSignalId(), anomalyId);
                        continue;
                    }

                    UpdateResponse<Object> response = openSearchClient.update(updateRequest, Object.class);
                    if (response.result() == Result.Updated) {
                        queryResult = true;
                    }
                    break;
                } catch (Exception e) {
                    log.error("Failed to update signal to remove anomaly for index:{}, documentId:{}, retry:{}, signal:{}, anomalyId:{}", indexName, retry, signalId, signalDetails, anomalyId, e);
                }
            }

            if (queryResult) {
                appIds.forEach(appId -> {
                    signalDetailProcessor.updateServiceSignals(signalId, serviceIdentifiers, signalStartTime, currentStatus, appId);
                });
                signalDetailProcessor.removeAnomalyFromSignalInRedis(signalId, anomalyId);
                if (isAddSignalDetails) {
                    signalDetailProcessor.updateSignalDetails(signalDetails, false);
                }
                return true;
            } else {
                healthMetrics.updateErrors();
                log.error("Error occurred while updating document to remove anomaly from signal. responseStatus:{}, signalId:{}, anomalyId:{}, max retried:{}", queryResult, signalDetails.getSignalId(), anomalyId, insertRetry);
                return false;
            }
        } catch (Exception e) {
            log.error("Error occurred while removing anomaly from signal. indexName:{}, signalId:{}, anomalyId:{}, accountId:{}, startTime:{}", indexName, signalId, anomalyId, accountIdentifier, signalStartTime, e);
            healthMetrics.updateErrors();
            return false;
        }
    }

    public boolean signalDetailsUpdateStatus(long updatedTime, String updatedStatus, Map<String, String> metaData, String signalId,
                                             Set<String> relatedSignals, String accountIdentifier, long signalStartTime, boolean anomalyUpdateReq) {
        List<String> indexNames = new ArrayList<>();
        OpenSearchClient openSearchClient = openSearchConfig.getOpenSearchClient(accountIdentifier, signalsIndex);
        if (openSearchClient == null) {
            log.error("Could not insert into signal index because of open search connection issue. SignalId:{}", signalId);
            return false;
        }

        SignalDetails signalDetails = redisUtilities.getSignalDetails(signalId);
        if (signalDetails == null) {
            log.error("Error occurred while getting the signal details. signalId:{}, startTime:{}, accountId:{}", signalId, signalStartTime, accountIdentifier);
            healthMetrics.updateErrors();
            return false;
        }

        if (anomalyUpdateReq) {
            //Update Related Anomalies SignalIds List
            log.debug("Anomalies list size [{}]. Early Warning signal id {}, Problem signal ids {}", signalDetails.getAnomalies().size(), signalId,
                    relatedSignals);
            signalDetails.getAnomalies().forEach(c -> {
                String[] anomalyIdArr = c.split("-");
                String anomalyTime = anomalyIdArr[anomalyIdArr.length - 1];
                updateAnomaly(c, Long.parseLong(anomalyTime) * 60000, relatedSignals, accountIdentifier);
            });
        }

        signalDetails.setAccountIdentifiers(new HashSet<>() {{
            add(accountIdentifier);
            if (signalDetails.getAccountIdentifiers() != null) {
                addAll(signalDetails.getAccountIdentifiers());
            }
        }});

        signalDetails.getMetadata().putAll(metaData);

        signalDetails.setUpdatedTime(updatedTime);
        if (!signalDetails.getCurrentStatus().equalsIgnoreCase(updatedStatus)) {
            signalDetails.setCurrentStatus(updatedStatus);
        }
        signalDetails.getStatusDetails().add(updatedStatus);
        if (relatedSignals != null && !relatedSignals.isEmpty()) {
            if (signalDetails.getRelatedSignals() != null && !signalDetails.getRelatedSignals().isEmpty()) {
                signalDetails.getRelatedSignals().addAll(relatedSignals);
            } else {
                signalDetails.setRelatedSignals(relatedSignals);
            }
        }

        DateHelper.getWeeksAsString(signalStartTime, signalStartTime).forEach(date ->
                indexNames.add(signalsIndex + "_" + date));

        String indexName = indexNames.get(0);
        UpdateRequest<Object, Object> updateRequest = new UpdateRequest.Builder<>()
                .index(indexName)
                .id(signalId)
                .doc(signalDetails)
                .retryOnConflict(3)
                .build();

        log.debug("Query for updating signal status detail : {}", updateRequest);

        boolean queryResult = false;
        for (int retry = insertRetry; retry > 0; retry--) {
            try {
                openSearchClient = openSearchConfig.getOpenSearchClient(accountIdentifier, signalsIndex);
                if (openSearchClient == null) {
                    log.error("Error occurred while updating signal because of open search connection issue. Retry:{}, SignalId:{}", retry, signalDetails.getSignalId());
                    continue;
                }

                UpdateResponse<Object> response = openSearchClient.update(updateRequest, Object.class);
                if (response.result() == Result.Updated) {
                    queryResult = true;
                }
                break;
            } catch (Exception e) {
                log.error("Error occurred update into open search document for index:{}, documentId:{}, retry:{}, signal:{}", indexName, signalDetails.getSignalId(), retry, signalDetails);
            }
        }

        if (queryResult) {
            signalDetailProcessor.updateSignalDetails(signalDetails, false);
            return true;
        } else {
            healthMetrics.updateErrors();
            log.error("Error occurred in updating the document into open search for closing signal. responseStatus:{}, signalId:{}, max retried:{}", queryResult, signalDetails.getSignalId(), insertRetry);
            return false;
        }
    }

    public Set<Anomalies> getAnomalyById(Set<String> anomalyIdSet, Set<String> accountIdentifiers, long anomalyTime) {
        Set<Anomalies> result = new HashSet<>();

        List<String> indexNames = new ArrayList<>();
        try {
            ObjectMapper objectMapper = commons.getObjectMapperWithHtmlEncoder();
            List<NameValuePair> matchAnyFieldsAnomalyId = new ArrayList<>();
            anomalyIdSet.forEach(anomalyId -> matchAnyFieldsAnomalyId.add(new NameValuePair("anomalyId", anomalyId)));

            accountIdentifiers.forEach(accountIdentifier ->
                    DateHelper.getWeeksAsString(anomalyTime, anomalyTime).forEach(date ->
                            indexNames.add(anomaliesIndex + "_" + accountIdentifier.toLowerCase() + "_" + date)));

            QueryOptions queryOptions = QueryOptions.builder()
                    .indexNames(indexNames)
                    .isTimeSeriesData(false)
                    .matchAnyOfFields(Optional.of(List.of(matchAnyFieldsAnomalyId)))
                    .numberOfRawRecords(anomalyIdSet.size())
                    .build();

            log.debug("Query for getting anomaly detail : {}", queryOptions);

            for (int retry = insertRetry; retry > 0; retry--) {
                try {
                    OpenSearchClient elasticClient = openSearchConfig.getOpenSearchClient(accountIdentifiers.iterator().next(), anomaliesIndex);
                    if (elasticClient == null) {
                        log.error("Could not get anomaly details because of OpenSearch connection issue. Retry:{}, anomalyIds:{}", retry, anomalyIdSet);
                        continue;
                    }

                    RawDocumentResults rawDocuments = OpenSearchQueryHelper.getRawDocuments(queryOptions, elasticClient);
                    if (rawDocuments == null || rawDocuments.getDocuments() == null || rawDocuments.getDocuments().isEmpty()) {
                        return null;
                    }

                    for (Documents hit : rawDocuments.getDocuments())
                        result.add(objectMapper.readValue(hit.getSource(), Anomalies.class));
                } catch (Exception e) {
                    log.error("Failed to get anomaly detail because of OpenSearch issue. Index:{}, retry:{}, anomalyIds:{}", indexNames, retry, anomalyIdSet, e);
                }
            }

        } catch (Exception e) {
            healthMetrics.updateErrors();
            log.error("Error in getting doc from indexNames:{}, anomalyIds:{}, accountIds:{}.", indexNames, anomalyIdSet, accountIdentifiers, e);
        }

        return result;
    }

    public boolean updateAnomaly(String anomalyId, long anomalyTime, Set<String> signalIds, String accountId) {
        return scheduler.addToAnomalyHelper(AnomalyHelper.builder()
                .anomalyId(anomalyId)
                .anomalyTime(anomalyTime)
                .signalIds(signalIds)
                .accountId(accountId.toLowerCase())
                .retry(maxRetry).build());
    }

    public boolean updateAnomalyIndex(String anomalyId, long anomalyTime, Set<String> signalIds, String accountId, Anomalies anomalyDetail) {
        String indexPrefix = anomaliesIndex + "_" + accountId.toLowerCase();
        List<String> indexNames = new ArrayList<>();
        try {
            Anomalies anomalies = getAnomalyById(anomalyId, accountId, anomalyTime);
            if (anomalies == null) {
                log.error("Could not get the anomaly details for accountId:{}, anomalyId:{}, signalIds:{}, anomalyTime:{}",
                        accountId, anomalyId, signalIds, anomalyTime);
                healthMetrics.updateErrors();
                return false;
            }

            DateHelper.getWeeksAsString(anomalyTime, anomalyTime).forEach(date ->
                    indexNames.add(indexPrefix + "_" + date));
            if (indexNames.isEmpty()) {
                log.error("Could not get the index for signalIds:{}, anomalyId:{}, anomalyTime:{}.", signalIds, anomalyId, anomalyTime);
                healthMetrics.updateErrors();
                return false;
            }

            Set<String> existingSignalIds = anomalyDetail.getSignalIds() == null ? new HashSet<>() : anomalyDetail.getSignalIds();
            existingSignalIds.addAll(signalIds);
            anomalyDetail.setSignalIds(existingSignalIds);

            UpdateRequest<Object, Object> updateRequest = new UpdateRequest.Builder<>()
                    .index(indexNames.get(0))
                    .id(anomalyId)
                    .doc(anomalies)
                    .retryOnConflict(3)
                    .build();

            log.trace("Anomaly found for update the signal, existing signalsIds:{}, indexNames:{}, anomalyId:{}, signalIds:{}, accountId:{}", signalIds, indexNames, anomalyId, signalIds, accountId);
            // TODO: Temp fix as anomaly was not getting updated
//            boolean isAnomalyAddedToQueue = scheduler.addToQueue(RequestHelper.builder().accountIdentifier(accountId).updateRequest(updateRequest).indexName(anomaliesIndex).build());
//            if (!isAnomalyAddedToQueue) {
//                log.error("Anomaly is not added to scheduler queue for anomaly update, indexNames:{}, anomalyId:{}, signalIds:{}, accountId:{}", indexNames, anomalyId, signalIds, accountId);
//            }
//            return isAnomalyAddedToQueue;

            OpenSearchClient openSearchClient = openSearchConfig.getOpenSearchClient(accountId, indexPrefix);
            if (openSearchClient == null) {
                log.error("Could not update anomaly index because of open search connection issue. AnomalyId:{}", anomalyId);
                return false;
            }
            IndexRequest<Object> indexRequest = new IndexRequest.Builder<>()
                    .index(indexNames.get(0))
                    .id(anomalyId)
                    .document(anomalyDetail)
                    .build();
            IndexResponse response = openSearchClient.index(indexRequest);
            return response.result() == Result.Updated;
        } catch (Exception e) {
            log.error("Error occurred while preparing anomaly update request. IndexNames:{}, AnomalyId: [{}], SignalIds to add: [{}], Account: [{}]",
                    indexNames, anomalyId, signalIds, accountId, e);
            healthMetrics.updateErrors();
            return false;
        }
    }

    public boolean isSignalsIndexPresent(String accountIdentifier, long startTime, long endTime) {
        String indexPrefix = signalsIndex + "_" + accountIdentifier.toLowerCase();

        List<String> indexNames = new ArrayList<>();
        DateHelper.getWeeksAsString(startTime, endTime).forEach(date ->
                indexNames.add(indexPrefix + "_" + date));

        QueryOptions queryOptions = QueryOptions.builder()
                .indexNames(indexNames)
                .build();

        log.trace("Query for getting index list: {}", queryOptions);

        OpenSearchClient openSearchClient = openSearchConfig.getOpenSearchClient(accountIdentifier, signalsIndex);
        if (openSearchClient == null) {
            log.error("Could not get index list because of openSearch connection issue. Skipping index signal status update check in Redis." +
                    " Account Identifier:{}, Index: {}", accountIdentifier, signalsIndex);
            return true;
        }

        return !OpenSearchQueryHelper.checkAndGetExistingIndex(queryOptions, openSearchClient).isEmpty();
    }

    public List<String> getExistingSignalsIndexNames(String accountIdentifier) throws Exception {
        try {
            List<String> signalIndexList = new ArrayList<>();
            OpenSearchClient client = openSearchConfig.getOpenSearchClient(accountIdentifier, signalsIndex);
            if (client == null) {
                log.error("Could not get index list because of openSearch connection issue. Skipping index signal status update check in Redis." +
                        " Account Identifier:{}, Index: {}", accountIdentifier, signalsIndex);
                throw new Exception("Couldn't establish OpenSearch connection");
            }

            List<String> indexNames = new ArrayList<>();
            indexNames.add(signalsIndex + "_*");
            QueryOptions queryOptions = QueryOptions.builder()
                    .indexNames(indexNames)
                    .build();

            RawDocumentResults rawDocuments = OpenSearchQueryHelper.getRawDocuments(queryOptions, client);
            if (rawDocuments == null || rawDocuments.getDocuments() == null || rawDocuments.getDocuments().isEmpty()) {
                return signalIndexList;
            }

            for (Documents hit : rawDocuments.getDocuments())
                signalIndexList.add(hit.getIndexName());

            return signalIndexList;
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new Exception(e.getMessage());
        }
    }

    public List<SignalDetails> getSignalById(Set<String> signalIdSet, long startTime, long endTime, String accountIdentifier) {
        List<SignalDetails> signalDetailsList = new ArrayList<>();
        List<String> indexNames = new ArrayList<>();
        try {
            ObjectMapper objectMapper = commons.getObjectMapperWithHtmlEncoder();
            DateHelper.getWeeksAsString(startTime, endTime).forEach(date ->
                    indexNames.add(signalsIndex + "_" + date));

            List<NameValuePair> matchAnyFieldsSignalId = new ArrayList<>();
            signalIdSet.forEach(signalId -> matchAnyFieldsSignalId.add(new NameValuePair("_id", signalId)));

            QueryOptions queryOptions = QueryOptions.builder()
                    .indexNames(indexNames)
                    .isTimeSeriesData(false)
                    .numberOfRawRecords(signalIdSet.size())
                    .matchAnyOfFields(Optional.of(new ArrayList<>() {{
                        add(matchAnyFieldsSignalId);
                    }}))
                    .build();

            log.debug("Query for getting signal detail : {}", queryOptions);

            OpenSearchClient client = openSearchConfig.getOpenSearchClient(accountIdentifier, signalsIndex);
            if (client == null) {
                log.error("Could not get signal details because of open search connection issue. SignalId:{}", signalIdSet);
                return null;
            }

            RefreshRequest refreshRequest = new RefreshRequest.Builder().index(indexNames.get(0)).build();

            for (int retry = insertRetry; retry > 0; retry--) {
                try {
                    OpenSearchClient openSearchClient = openSearchConfig.getOpenSearchClient(accountIdentifier, signalsIndex);
                    if (openSearchClient == null) {
                        log.error("Could not get signal details because of open search connection issue. Retry:{}, signalId:{}", retry, signalIdSet);
                        continue;
                    }

                    RefreshResponse refreshResponse = client.indices().refresh(refreshRequest);
                    log.trace("Refresh response:{}", refreshResponse);

                    RawDocumentResults rawDocuments = OpenSearchQueryHelper.getRawDocuments(queryOptions, openSearchClient);
                    if (rawDocuments == null || rawDocuments.getDocuments() == null || rawDocuments.getDocuments().isEmpty()) {
                        return null;
                    }

                    for (Documents hit : rawDocuments.getDocuments())
                        signalDetailsList.add(objectMapper.readValue(hit.getSource(), SignalDetails.class));

                    return signalDetailsList;
                } catch (Exception e) {
                    log.error("Failed to get signal details because of open search issue. Index:{}, retry:{}, signalId:{}", indexNames, retry, signalIdSet, e);
                }
            }

        } catch (Exception e) {
            healthMetrics.updateErrors();
            log.error("Error while getting signal details from indexNames:{}, signalId:{}, accountId:{}.", indexNames, signalIdSet, accountIdentifier, e);
        }
        return null;
    }

    /**
     * Fetches signal details by their IDs from OpenSearch.
     *
     * @param signalIdSet Set of signal IDs to fetch.
     * @param accountIdentifier Identifier for the account.
     * @return List of SignalDetails objects or null if an error occurs.
     */
    public List<SignalDetails> getSignalById(Set<String> signalIdSet, String accountIdentifier) {
        List<SignalDetails> signalDetailsList = new ArrayList<>();
        String indexPrefix = signalsIndex + "_*";

        try {
            OpenSearchClient client = openSearchConfig.getOpenSearchClient(accountIdentifier, signalsIndex);
            if (client == null) {
                log.error("Could not get signal details because of open search connection issue. SignalId:{}", signalIdSet);
                return null;
            }

            ObjectMapper objectMapper = commons.getObjectMapperWithHtmlEncoder();

            List<NameValuePair> matchAnyFieldsSignalId = new ArrayList<>();
            signalIdSet.forEach(signalId -> matchAnyFieldsSignalId.add(new NameValuePair("_id", signalId)));

            QueryOptions queryOptions = QueryOptions.builder()
                    .indexNames(Collections.singletonList(indexPrefix))
                    .isTimeSeriesData(false)
                    .numberOfRawRecords(signalIdSet.size())
                    .matchAnyOfFields(Optional.of(new ArrayList<>() {{
                        add(matchAnyFieldsSignalId);
                    }}))
                    .build();

            log.debug("Query for getting signal detail : {}", queryOptions);

            RawDocumentResults rawDocuments = OpenSearchQueryHelper.getRawDocuments(queryOptions, client);
            if (rawDocuments == null || rawDocuments.getDocuments() == null || rawDocuments.getDocuments().isEmpty()) {
                return signalDetailsList;
            }

            for (Documents hit : rawDocuments.getDocuments())
                signalDetailsList.add(objectMapper.readValue(hit.getSource(), SignalDetails.class));

            return signalDetailsList;
        } catch (Exception e) {
            healthMetrics.updateErrors();
            log.error("Error while getting signal details from indexPrefix:{}, signalId:{}, accountId:{}.", indexPrefix, signalIdSet, accountIdentifier, e);
        }
        return null;
    }

    public Anomalies getAnomalyById(String anomalyId, String accountIdentifier, long anomalyTime) {
        String indexPrefix = anomaliesIndex + "_" + accountIdentifier.toLowerCase();
        List<String> indexNames = new ArrayList<>();
        try {
            ObjectMapper objectMapper = commons.getObjectMapperWithHtmlEncoder();
            List<NameValuePair> matchFields = new ArrayList<>();
            matchFields.add(new NameValuePair("_id", anomalyId));

            DateHelper.getWeeksAsString(anomalyTime, anomalyTime).forEach(date ->
                    indexNames.add(indexPrefix + "_" + date));

            QueryOptions queryOptions = QueryOptions.builder()
                    .indexNames(indexNames)
                    .isTimeSeriesData(false)
                    .matchAllFields(Optional.of(matchFields))
                    .numberOfRawRecords(1)
                    .build();

            log.debug("Query for getting anomaly detail : {}", queryOptions);

            for (int retry = insertRetry; retry > 0; retry--) {
                try {
                    OpenSearchClient openSearchClient = openSearchConfig.getOpenSearchClient(accountIdentifier, anomaliesIndex);
                    if (openSearchClient == null) {
                        log.error("Could not get anomaly details because of open search connection issue. Retry:{}, anomalyId:{}", retry, anomalyId);
                        continue;
                    }

                    RawDocumentResults rawDocuments = OpenSearchQueryHelper.getRawDocuments(queryOptions, openSearchClient);
                    if (rawDocuments == null || rawDocuments.getDocuments() == null || rawDocuments.getDocuments().isEmpty()) {
                        return null;
                    }

                    String doc = rawDocuments.getDocuments().get(0).getSource();
                    return objectMapper.readValue(doc, Anomalies.class);
                } catch (Exception e) {
                    log.error("Failed to get anomaly detail because of open search issue. Index:{}, retry:{}, anomalyId:{}", indexNames, retry, anomalyId, e);
                }
            }

        } catch (Exception e) {
            healthMetrics.updateErrors();
            log.error("Error in getting doc from indexNames:{}, anomalyId:{}, accountId:{}.", indexNames, anomalyId, accountIdentifier, e);
        }
        return null;
    }

    public boolean closeSignal(SignalDetails signalDetails, String accountIdentifier, String appIdentifier) {
        String indexName = null;
        try {

            OpenSearchClient openSearchClient = openSearchConfig.getOpenSearchClient(accountIdentifier, signalsIndex);
            if (openSearchClient == null) {
                log.error("Could not insert into signal index because of OpenSearch connection issue. SignalId:{}", signalDetails.getSignalId());
                return false;
            }

            List<String> indexDates = DateHelper.getWeeksAsString(signalDetails.getStartedTime(), signalDetails.getStartedTime());
            if (indexDates == null || indexDates.isEmpty()) {
                log.error("Could not get the index for signalId:{}, signalTime:{}.", signalDetails.getSignalId(), signalDetails.getStartedTime());
                healthMetrics.updateErrors();
                return false;
            }
            indexName = signalsIndex + "_" + indexDates.get(0);
            UpdateRequest<Object, Object> updateRequest = new UpdateRequest.Builder<>()
                    .index(indexName)
                    .id(signalDetails.getSignalId())
                    .doc(signalDetails)
                    .retryOnConflict(3)
                    .build();

            log.debug("Signal Closing request to OpenSearch for signal id : {}, Details : {}", signalDetails.getSignalId(), updateRequest);

            boolean queryResult = false;
            for (int retry = insertRetry; retry > 0; retry--) {
                try {
                    openSearchClient = openSearchConfig.getOpenSearchClient(accountIdentifier, signalsIndex);
                    if (openSearchClient == null) {
                        log.error("Error occurred while closing signal because of open search connection issue. Retry:{}, SignalId:{}", retry, signalDetails.getSignalId());
                        continue;
                    }

                    UpdateResponse<Object> response = openSearchClient.update(updateRequest, Object.class);
                    if (response.result() == Result.Updated) {
                        queryResult = true;
                    }
                    break;
                } catch (Exception e) {
                    if (retry == 1) {
                        log.error("Error occurred while update into open search document for index:{}, documentId:{}, retry:{}, signal:{}, exception:{}",
                                indexName, signalDetails.getSignalId(), retry, signalDetails, e.getMessage());
                    } else {
                        log.error("Error occurred while update into open search document for index:{}, documentId:{}, retry:{}, signal:{}, exception: ",
                                indexName, signalDetails.getSignalId(), retry, signalDetails, e);
                    }
                }
            }

            if (queryResult) {
                signalDetailProcessor.updateServiceSignals(signalDetails.getSignalId(), signalDetails.getServiceIds(), signalDetails.getStartedTime(), SignalStatus.CLOSED.name(), appIdentifier);
                if (signalDetails.getSignalType().equals(SignalType.EARLY_WARNING.name()) || signalDetails.getSignalType().equals(SignalType.PROBLEM.name())) {
                    signalDetailProcessor.updateSignalDetails(signalDetails, true);
                }
                return true;
            } else {
                healthMetrics.updateErrors();
                log.error("Error occurred in updating the document into open search for closing signal. responseStatus:{}, signalId:{}, max retried:{}", queryResult, signalDetails.getSignalId(), insertRetry);
                return false;
            }

        } catch (Exception e) {
            healthMetrics.updateErrors();
            log.error("Error occurred in closing signal from index {}, signalId:{}, accountId:{} : ", indexName, signalDetails.getSignalId(), accountIdentifier, e);
            return false;
        }
    }

//    public boolean signalDetailsUpdateStatus(long updatedTime, String updatedStatus, Map<String, String> mapData, String signalId,
//                                             Set<String> relatedSignals, String accountIdentifier, long signalStartTime, boolean anomalyUpdateReq) {
//        String indexPrefix = signalsIndex + "_" + accountIdentifier.toLowerCase();
//
//        List<String> indexNames = new ArrayList<>();
//        OpenSearchClient openSearchClient = openSearchConfig.getOpenSearchClient(accountIdentifier, signalsIndex);
//        if (openSearchClient == null) {
//            log.error("Could not insert into signal index because of open search connection issue. SignalId:{}", signalId);
//            return false;
//        }
//        SignalDetails signalDetails = getSignalById(signalId, signalStartTime, accountIdentifier);
//
//        if (anomalyUpdateReq) {
//            //Update Related Anomalies SignalIds List
//            log.debug("Anomalies list size [{}]. Early Warning signal id {}, Problem signal ids {}", signalDetails.getAnomalies().size(), signalId,
//                    relatedSignals);
//            signalDetails.getAnomalies().forEach(c -> {
//                String[] anomalyIdArr = c.split("-");
//                String anomalyTime = anomalyIdArr[anomalyIdArr.length - 1];
//                updateAnomaly(c, Long.parseLong(anomalyTime) * 60000, relatedSignals, accountIdentifier);
//            });
//        }
//
//        signalDetails.getMetadata().putAll(mapData);
//        signalDetails.setUpdatedTime(updatedTime);
//        if (!signalDetails.getCurrentStatus().equalsIgnoreCase(updatedStatus)) {
//            signalDetails.setCurrentStatus(updatedStatus);
//        }
//        signalDetails.getStatusDetails().add(updatedStatus);
//        if (relatedSignals != null && !relatedSignals.isEmpty()) {
//            if (signalDetails.getRelatedSignals() != null && !signalDetails.getRelatedSignals().isEmpty()) {
//                signalDetails.getRelatedSignals().addAll(relatedSignals);
//            } else {
//                signalDetails.setRelatedSignals(relatedSignals);
//            }
//        }
//
//        DateHelper.getWeeksAsString(signalStartTime, signalStartTime).forEach(date ->
//                indexNames.add(indexPrefix + "_" + date));
//
//        String indexName = indexNames.get(0);
//        UpdateRequest<Object, Object> updateRequest = new UpdateRequest.Builder<>()
//                .index(indexName)
//                .id(signalId)
//                .doc(signalDetails)
//                .build();
//
//        boolean queryResult = false;
//        for (int retry = insertRetry; retry > 0; retry--) {
//            try {
//                openSearchClient = openSearchConfig.getOpenSearchClient(accountIdentifier, signalsIndex);
//                if (openSearchClient == null) {
//                    log.error("Error occurred while updating signal because of open search connection issue. Retry:{}, SignalId:{}", retry, signalDetails.getSignalId());
//                    continue;
//                }
//
//                UpdateResponse<Object> response = openSearchClient.update(updateRequest, Object.class);
//                if (response.result() == Result.Updated) {
//                    queryResult = true;
//                }
//                break;
//            } catch (Exception e) {
//                log.error("Error occurred update into open search document for index:{}, documentId:{}, retry:{}, signal:{}", indexName, signalDetails.getSignalId(), retry, signalDetails);
//            }
//        }
//
//        if (queryResult) {
//            return true;
//        } else {
//            healthMetrics.updateErrors();
//            log.error("Error occurred in updating the document into open search for closing signal. responseStatus:{}, signalId:{}, max retried:{}", queryResult, signalDetails.getSignalId(), insertRetry);
//            return false;
//        }
//
//    }
//
//    public Set<SignalDetails> getOpenSignals(String accountIdentifier, boolean isMLEExclude) throws Exception {
//
//        List<String> indexNames = new ArrayList<>();
//
//        List<NameValuePair> matchFields = new ArrayList<>();
//        matchFields.add(new NameValuePair("currentStatus", "OPEN"));
//
//        QueryOptions.QueryOptionsBuilder queryOptionsBuilder;
//        if (openSignalOffset == 0) {
//            String indexName = signalsIndex + "_" + accountIdentifier.toLowerCase() + "_*";
//            indexNames.add(indexName);
//
//            queryOptionsBuilder = QueryOptions.builder()
//                    .indexNames(Collections.singletonList(indexName))
//                    .isTimeSeriesData(false)
//                    .matchAllFields(Optional.of(matchFields))
//                    .fetchAllRecords(true);
//
//        } else {
//            String indexPrefix = signalsIndex + "_" + accountIdentifier.toLowerCase();
//
//            long toTime = System.currentTimeMillis();
//            long fromTime = toTime - TimeUnit.DAYS.toMillis(openSignalOffset);
//            DateHelper.getWeeksAsString(fromTime, toTime).forEach(date ->
//                    indexNames.add(indexPrefix + "_" + date));
//
//            queryOptionsBuilder = QueryOptions.builder()
//                    .indexNames(indexNames)
//                    .epochFieldName("updatedTime")
//                    .epochFromDate(fromTime)
//                    .epochToDate(toTime)
//                    .isTimeSeriesData(false)
//                    .matchAllFields(Optional.of(matchFields))
//                    .fetchAllRecords(true);
//        }
//
//        QueryOptions queryOptions;
//
//        if (isMLEExclude) {
//            List<NameValuePair> matchNoneFields = new ArrayList<>();
//            matchNoneFields.add(new NameValuePair("metadata.Source", "MLE"));
//            queryOptionsBuilder.matchNoneOfFields(Optional.of(new ArrayList<>() {{
//                add(matchNoneFields);
//            }}));
//        }
//        queryOptions = queryOptionsBuilder.build();
//
//        try {
//            log.debug("OS query for fetching anomaly data: {}", queryOptions);
//            Set<SignalDetails> signalDetails = new HashSet<>();
//            for (int retry = insertRetry; retry > 0; retry--) {
//                try {
//                    OpenSearchClient openSearchClient = openSearchConfig.getOpenSearchClient(accountIdentifier, signalsIndex);
//                    if (openSearchClient == null) {
//                        log.error("Could not get open signal details because of open search connection issue. Retry:{}, accountIdentifier:{}", retry, accountIdentifier);
//                        continue;
//                    }
//
//                    RawDocumentResults rawDocuments = OpenSearchQueryHelper.getRawDocuments(queryOptions, openSearchClient);
//                    if (rawDocuments == null || rawDocuments.getDocuments() == null || rawDocuments.getDocuments().isEmpty()) {
//                        return Collections.emptySet();
//                    }
//
//                    for (Documents hit : rawDocuments.getDocuments())
//                        signalDetails.add(commons.getObjectMapperWithHtmlEncoder().readValue(hit.getSource(), SignalDetails.class));
//                    break;
//
//                } catch (OpenSearchException ose) {
//                    log.error("OpenSearchException occurred while getting open signal details. Index:{}, retry:{}, accountIdentifier:{}", indexNames, retry, accountIdentifier, ose);
//                    throw ose;
//                } catch (Exception e) {
//                    log.error("General exception occurred. Retrying. Index:{}, retry:{}, accountIdentifier:{}", indexNames, retry, accountIdentifier, e);
//                }
//            }
//
//            return signalDetails;
//
//        } catch (OpenSearchException ose) {
//            healthMetrics.updateErrors();
//            log.error("OpenSearchException bubbled up while fetching data from index {}. Details: accountId:{}", indexNames, accountIdentifier, ose);
//            throw ose;
//        } catch (Exception e) {
//            healthMetrics.updateErrors();
//            log.error("Unexpected error while fetching data from index {}. Details: accountId:{}", indexNames, accountIdentifier, e);
//            return Collections.emptySet();
//        }
//    }

    public SignalDetails getMLESignalById(String signalId, long startTime, String accountIdentifier) {
        List<String> indexNames = new ArrayList<>();
        try {
            OpenSearchClient client = openSearchConfig.getOpenSearchClient(accountIdentifier, signalsIndex);
            if (client == null) {
                log.error("Could not get signal details because of open search connection issue. SignalId:{}", signalId);
                return null;
            }

            ObjectMapper objectMapper = commons.getObjectMapperWithHtmlEncoder();
            DateHelper.getWeeksAsString(startTime, startTime).forEach(date ->
                    indexNames.add(signalsIndex + "_" + date));

            List<NameValuePair> matchFields = new ArrayList<>();
            matchFields.add(new NameValuePair("_id", signalId));
            matchFields.add(new NameValuePair("metadata.Source", "MLE"));

            QueryOptions queryOptions = QueryOptions.builder()
                    .indexNames(indexNames)
                    .isTimeSeriesData(false)
                    .numberOfRawRecords(1)
                    .matchAllFields(Optional.of(matchFields))
                    .build();

            log.debug("Query for getting signal detail : {}", queryOptions);

            RefreshRequest refreshRequest = new RefreshRequest.Builder().index(indexNames.get(0)).build();

            for (int retry = insertRetry; retry > 0; retry--) {
                try {
                    OpenSearchClient openSearchClient = openSearchConfig.getOpenSearchClient(accountIdentifier, signalsIndex);
                    if (openSearchClient == null) {
                        log.error("Could not get signal details because of open search connection issue. Retry:{}, signalId:{}", retry, signalId);
                        continue;
                    }

                    RefreshResponse refreshResponse = client.indices().refresh(refreshRequest);
                    log.trace("Refresh response:{}", refreshResponse);

                    RawDocumentResults rawDocuments = OpenSearchQueryHelper.getRawDocuments(queryOptions, openSearchClient);
                    if (rawDocuments == null || rawDocuments.getDocuments() == null || rawDocuments.getDocuments().isEmpty()) {
                        return null;
                    }

                    String doc = rawDocuments.getDocuments().get(0).getSource();
                    return objectMapper.readValue(doc, SignalDetails.class);
                } catch (Exception e) {
                    log.error("Failed to get signal details because of open search issue. Index:{}, retry:{}, signalId:{}", indexNames, retry, signalId, e);
                }
            }
        } catch (Exception e) {
            healthMetrics.updateErrors();
            log.error("Error while getting signal details from indexNames:{}, signalId:{}, accountId:{}.", indexNames, signalId, accountIdentifier, e);
        }

        return null;
    }

    public List<SignalDetails> getMLERelatedSignalsById(Set<String> signalIdSet, String accountIdentifier) {
        List<SignalDetails> signalDetailsList = new ArrayList<>();
        String indexPrefix = signalsIndex + "_*";

        try {
            OpenSearchClient openSearchClient = openSearchConfig.getOpenSearchClient(accountIdentifier, signalsIndex);
            if (openSearchClient == null) {
                log.error("Could not get signal details because of open search connection issue. SignalId:{}", signalIdSet);
                return null;
            }

            ObjectMapper objectMapper = commons.getObjectMapperWithHtmlEncoder();

            List<NameValuePair> matchAnyFieldsSignalId = new ArrayList<>();
            signalIdSet.forEach(signalId -> matchAnyFieldsSignalId.add(new NameValuePair("_id", signalId)));

            QueryOptions queryOptions = QueryOptions.builder()
                    .indexNames(Collections.singletonList(indexPrefix))
                    .isTimeSeriesData(false)
                    .numberOfRawRecords(signalIdSet.size())
                    .matchAnyOfFields(Optional.of(new ArrayList<>() {{
                        add(matchAnyFieldsSignalId);
                    }}))
                    .build();

            log.debug("Query for getting signal detail : {}", queryOptions);

            RawDocumentResults rawDocuments = OpenSearchQueryHelper.getRawDocuments(queryOptions, openSearchClient);
            if (rawDocuments == null || rawDocuments.getDocuments() == null || rawDocuments.getDocuments().isEmpty()) {
                return signalDetailsList;
            }

            for (Documents hit : rawDocuments.getDocuments())
                signalDetailsList.add(objectMapper.readValue(hit.getSource(), SignalDetails.class));

            return signalDetailsList;
        } catch (Exception e) {
            healthMetrics.updateErrors();
            log.error("Error while getting signal details from indexPrefix:{}, signalId:{}, accountId:{}.", indexPrefix, signalIdSet, accountIdentifier, e);
        }
        return null;
    }

    public boolean updateMLESignal(long updatedTime, String currentStatus, Map<String, String> metaDataMap, String anomalyId,
                                   String signalId, String accountIdentifier, int severityId, AnomalySummary anomalySummary, Set<String> relatedSignal,
                                   SignalDetails mleSignal, Set<String> appIds, boolean isWorkload, int anomalitySeverityId) {
        String indexName = null;
        try {
            OpenSearchClient openSearchClient = openSearchConfig.getOpenSearchClient(accountIdentifier, signalsIndex);
            if (openSearchClient == null) {
                log.error("Could not insert into signal index because of open search connection issue. SignalId:{}, anomalyId:{}", signalId, anomalyId);
                return false;
            }

            mleSignal.getAnomalies().add(anomalyId);
            if (!mleSignal.getCurrentStatus().equals(currentStatus)) {
                mleSignal.setCurrentStatus(currentStatus);
            }
            mleSignal.getStatusDetails().add(currentStatus);
            mleSignal.setUpdatedTime(updatedTime);
            mleSignal.setSeverityId(severityId);

            mleSignal.getAccountIdentifiers().add(accountIdentifier);

            if (currentStatus.equals(SignalStatus.CLOSED.name()) || currentStatus.equals(SignalStatus.UPGRADED.name())) {
                metaDataMap.put("end_time", String.valueOf(updatedTime));
            }
            mleSignal.getMetadata().putAll(metaDataMap);
            mleSignal.setTimestamp(DateHelper.getDate(anomalySummary.getEventTime()));
            mleSignal.getServiceIds().addAll(anomalySummary.getServiceId());
            if (!relatedSignal.isEmpty()) {
                Set<String> osRelatedSignal = mleSignal.getRelatedSignals();
                if (osRelatedSignal == null) {
                    mleSignal.setRelatedSignals(new HashSet<>(relatedSignal));
                } else {
                    osRelatedSignal.addAll(relatedSignal);
                }
            }

            List<String> indexDates = DateHelper.getWeeksAsString(mleSignal.getStartedTime(), mleSignal.getStartedTime());
            if (indexDates == null || indexDates.isEmpty()) {
                log.error("Could not get the index for signalId:{}, signalTime:{}, anomalyId:{}.", mleSignal.getSignalId(), mleSignal.getStartedTime(), anomalyId);
                healthMetrics.updateErrors();
                return false;
            }
            indexName = signalsIndex + "_" + indexDates.get(0);

            UpdateRequest<Object, Object> updateRequest = new UpdateRequest.Builder<>()
                    .index(indexName)
                    .id(signalId)
                    .doc(mleSignal)
                    .retryOnConflict(3)
                    .build();

            log.info("severity id found for signal details while updating. severityId {}, signalId {}", severityId,
                    signalId);

            boolean queryResult = false;
            for (int retry = insertRetry; retry > 0; retry--) {
                try {
                    openSearchClient = openSearchConfig.getOpenSearchClient(accountIdentifier, signalsIndex);
                    if (openSearchClient == null) {
                        log.error("Could not insert into signal index because of open search connection issue. Retry:{}, signalId:{}, anomalyId:{}", retry, mleSignal.getSignalId(), anomalyId);
                        continue;
                    }

                    UpdateResponse<Object> response = openSearchClient.update(updateRequest, Object.class);
                    if (response.result() == Result.Updated) {
                        queryResult = true;
                    }
                    break;
                } catch (Exception e) {
                    log.error("Failed to update into open search document for index:{}, documentId:{}, retry:{}, signal:{}, anomalyId:{}", indexName, retry, signalId, mleSignal, anomalyId, e);
                }
            }


            if (queryResult) {
                appIds.forEach(appId -> {
                    signalDetailProcessor.updateServiceSignals(signalId, anomalySummary.getServiceId(), mleSignal.getStartedTime(), currentStatus, appId);
                });
                signalDetailProcessor.updateSignalDetailsIntoRedis(signalId, anomalySummary, appIds, isWorkload, anomalitySeverityId);
//                signalDetailProcessor.updateSignalDetails(mleSignal);

                return true;
            } else {
                healthMetrics.updateErrors();
                log.error("Error occurred while updating document into open search for index:{}, documentId:{}, anomalyId:{}, max retried:{}", indexName, signalId, anomalyId, insertRetry);
                return false;
            }
        } catch (Exception e) {
            log.error("Error occurred while updating the signal details. indexNames:{}, signalId:{}, anomalyId:{}, accountId:{}, startTime:{}", indexName, signalId, anomalyId, accountIdentifier, mleSignal.getStartedTime(), e);
            healthMetrics.updateErrors();
            return false;
        }
    }
}