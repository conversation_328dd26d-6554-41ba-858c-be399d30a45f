package com.heal.signal.detector.service;

import com.appnomic.appsone.common.protbuf.AnomalyEventProtos;
import com.appnomic.appsone.common.protbuf.SignalProtos;
import com.heal.signal.detector.config.RabbitMqConfig;
import com.heal.signal.detector.process.ProtoValidation;
import com.heal.signal.detector.util.HealthMetrics;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.IOException;

@Slf4j
@Service
public class ReceiverFromQueue {

    @Autowired
    private ProtoValidation protoValidation;

    @Autowired
    RabbitMqConfig rabbitMqConfig;

    @Autowired
    private HealthMetrics metrics;

    public void receiveAnomalyEventData(byte[] aggregatedKpiStream) {

        metrics.updateSignalInputQueueReadData();
        metrics.updateSnapshots(rabbitMqConfig.anomalyMessages, 1);

        try {
            AnomalyEventProtos.AnomalyEvent anomalyEvent;
            try {
                anomalyEvent = AnomalyEventProtos.AnomalyEvent.parseDelimitedFrom(new ByteArrayInputStream(aggregatedKpiStream));
            } catch (IOException e) {
                anomalyEvent = AnomalyEventProtos.AnomalyEvent.parseFrom(aggregatedKpiStream);
            }

            log.trace("Anomaly event data received. Details: {}", anomalyEvent);

            protoValidation.validateAndProcessInputAnomalyEvent(anomalyEvent);
        } catch (Exception e) {
            log.error("Exception in receiving event from {} queue. Reason: ", rabbitMqConfig.anomalyMessages, e);
            metrics.updateErrors();
        }
    }

    public void receiveIncidentFromMLE(byte[] aggregatedKpiStream) {
        metrics.updateExternalSignalInputQueueReadData();
        metrics.updateSnapshots(rabbitMqConfig.mleSignalMessages, 1);

        try {
            log.debug("AI-OPS: inside:method name receiveIncidentFromMLE");
            SignalProtos.SignalDetails signalProtos;
            try {
                signalProtos =
                        SignalProtos.SignalDetails.parseDelimitedFrom(new ByteArrayInputStream(aggregatedKpiStream));
            } catch (IOException e) {
                signalProtos = SignalProtos.SignalDetails.parseFrom(aggregatedKpiStream);
            }

            log.trace("AI-OPS: Signal data received from mle. Details: {}", signalProtos);

            protoValidation.validateAndProcessMLESignals(signalProtos);
        } catch (Exception e) {
            log.error("AI-OPS: Exception in receiving incidents/signals from {} queue. Reason: ",
                    rabbitMqConfig.mleSignalMessages, e);
            metrics.updateErrors();
        }
    }
}