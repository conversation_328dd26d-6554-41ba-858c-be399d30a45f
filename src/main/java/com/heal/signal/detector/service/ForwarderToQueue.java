package com.heal.signal.detector.service;

import com.appnomic.appsone.common.protbuf.SignalProtos;
import com.heal.signal.detector.util.HealthMetrics;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.FanoutExchange;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class ForwarderToQueue {

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Qualifier("signalOutputQueue")
    @Autowired
    private Queue signalOutputQueue;

    @Qualifier("aiOpsSignalOutputQueue")
    @Autowired
    private Queue signalMessagesQueueAIOPS;

    @Autowired
    @Qualifier("signalFanoutExchange")
    private FanoutExchange fanoutExchange;

    @Autowired
    private HealthMetrics metrics;

    public void sendSignalMessages(SignalProtos.SignalDetails signalDetailsProto) {
        log.trace("Sending signal messages to queue [{}, {}]. Data:{}", signalOutputQueue.getName(),
                signalMessagesQueueAIOPS.getName(),
                signalDetailsProto.getSignalId());

        metrics.updateSignalOutputQueueWriteData();
        metrics.updateSnapshots(signalOutputQueue.getName(), 1);
        metrics.updateAIOPsSignalOutputQueueWriteData();
        metrics.updateSnapshots(signalMessagesQueueAIOPS.getName(), 1);

//        this.rabbitTemplate.convertAndSend(signalOutputQueue.getName(), signalDetailsProto.toByteArray());
        this.rabbitTemplate.convertAndSend(fanoutExchange.getName(), "", signalDetailsProto.toByteArray());
    }
}