package com.heal.signal.detector.cache;

import com.heal.configuration.pojos.*;
import com.heal.signal.detector.util.RedisUtilities;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Repository;

import java.util.*;

@Slf4j
@Component
@Repository
public class CacheWrapper {

    @Value("${redis.cache.mode:0}")
    int mode;

    @Autowired
    LocalCache cache;

    @Autowired
    RedisUtilities redisUtilities;

    public List<Account> getAccounts() {
        try {
            log.info("The mode value is {}", mode);
            if (mode == 1) {
                log.info("Mode is one, Getting the data from redis");
                return redisUtilities.getAccounts();
            }
            return cache.getAccounts();
        } catch (Exception e) {
            log.error("Error occurred while getting account details");
            return Collections.emptyList();
        }
    }

    public Application getApplicationByIdentifier(String accIdentifier, String appIdentifier) {
        try {
            log.info("The mode value is {}", mode);
            if (mode == 1) {
                log.info("Mode is one, Getting the data from redis");
                return redisUtilities.getApplicationDetails(accIdentifier, appIdentifier);
            }
            return cache.getApplicationByIdentifier(accIdentifier, appIdentifier);
        } catch (Exception e) {
            log.error("Error occurred while getting application details for application {} of account {}", appIdentifier, accIdentifier);
            return null;
        }
    }

    public List<BasicKpiEntity> getComponentKpis(String accIdentifier, String componentIdentifier) {
        try {
            log.info("The mode value is {}", mode);
            if (mode == 1) {
                log.info("Mode is one, Getting the data from redis");
                return redisUtilities.getComponentKPIs(accIdentifier, componentIdentifier);
            }
            return cache.getComponentKpis(accIdentifier, componentIdentifier);
        } catch (Exception e) {
            log.error("Error occurred while getting kpis for component {} of account {}", accIdentifier, componentIdentifier);
            return Collections.emptyList();
        }
    }

    public BasicKpiEntity getComponentKpiByIdentifier(String accIdentifier, String componentIdentifier, String kpiIdentifier) {
        try {
            log.info("The mode value is {}", mode);
            if (mode == 1) {
                log.info("Mode is one, Getting the data from redis");
                return redisUtilities.getKPIDetails(accIdentifier, componentIdentifier, kpiIdentifier);
            }
            return cache.getComponentKpiByIdentifier(accIdentifier, componentIdentifier, kpiIdentifier);
        } catch (Exception e) {
            log.error("Error occurred while getting kpi detail for kpi {} of component {} of account {}", kpiIdentifier, componentIdentifier, accIdentifier);
            return null;
        }
    }

    public Service getServiceDetailsByIdentifier(String accIdentifier, String serviceIdentifier) {
        try {
            log.info("The mode value is {}", mode);
            if (mode == 1) {
                log.info("Mode is one, Getting the data from redis");
                return redisUtilities.getServiceDetails(accIdentifier, serviceIdentifier);
            }
            return cache.getServiceDetailsByIdentifier(accIdentifier, serviceIdentifier);
        } catch (Exception e) {
            log.error("Error occurred while getting service detail for service {} of account {}", serviceIdentifier, accIdentifier);
            return null;
        }
    }

    public List<MaintenanceDetails> getServiceMaintenanceDetails(String accIdentifier, String serviceIdentifier) {
        try {
            log.info("The mode value is {}", mode);
            if (mode == 1) {
                log.info("Mode is one, Getting the data from redis");
                return redisUtilities.getServiceMaintenanceDetails(accIdentifier, serviceIdentifier);
            }
            return cache.getServiceMaintenanceDetails(accIdentifier, serviceIdentifier);
        } catch (Exception e) {
            log.error("Error occurred while getting service maintenance detail for service {} of account {}", serviceIdentifier, accIdentifier);
            return Collections.emptyList();
        }
    }

    public List<BasicEntity> getAccountServices(String accIdentifier) {
        try {
            log.info("The mode value is {}", mode);
            if (mode == 1) {
                log.info("Mode is one, Getting the data from redis");
                return redisUtilities.getAccountServices(accIdentifier);
            }
            return cache.getAccountServices(accIdentifier);
        } catch (Exception e) {
            log.error("Error occurred while getting service list for account {}", accIdentifier);
            return Collections.emptyList();
        }
    }

    public List<MaintenanceDetails> getInstanceMaintenanceDetails(String accIdentifier, String instanceIdentifier) {
        try {
            log.info("The mode value is {}", mode);
            if (mode == 1) {
                log.info("Mode is one, Getting the data from redis");
                return redisUtilities.getInstanceMaintenanceDetails(accIdentifier, instanceIdentifier);
            }
            return cache.getInstanceMaintenanceDetails(accIdentifier, instanceIdentifier);
        } catch (Exception e) {
            log.error("Error occurred while getting instance maintenance detail for instance {} of account {}", instanceIdentifier, accIdentifier);
            return Collections.emptyList();
        }
    }

    public CompInstKpiEntity getInstanceKPIDetails(String accIdentifier, String instanceIdentifier, int kpiId) {
        try {
            log.info("The mode value is {}", mode);
            if (mode == 1) {
                log.info("Mode is one, Getting the data from redis");
                return redisUtilities.getInstanceKPIDetails(accIdentifier, instanceIdentifier, kpiId);
            }
            return cache.getInstanceKPIDetails(accIdentifier, instanceIdentifier, kpiId);
        } catch (Exception e) {
            log.error("Error occurred while getting instance kpi detail for kpiId {} of instance {} of account {}", kpiId, instanceIdentifier, accIdentifier);
            return null;
        }
    }

    public Set<BasicEntity> getNeighbours(String accIdentifier, String serviceIdentifier) {
        try {
            log.info("The mode value is {}", mode);
            if (mode == 1) {
                log.info("Mode is one, Getting the data from redis");
                return redisUtilities.getNeighbours(accIdentifier, serviceIdentifier);
            }
            return cache.getNeighbours(accIdentifier, serviceIdentifier);
        } catch (Exception e) {
            log.error("Error occurred while getting neighbours for service {} of account {}", serviceIdentifier, accIdentifier);
            return Collections.emptySet();
        }
    }

    public Map<Integer, List<BasicEntity>> getAccountOutbounds(String accIdentifier) {
        try {
            log.info("The mode value is {}", mode);
            if (mode == 1) {
                log.info("Mode is one, Getting the data from redis");
                return redisUtilities.getAccountOutbounds(accIdentifier);
            }
            return cache.getAccountOutbounds(accIdentifier);
        } catch (Exception e) {
            log.error("Error occurred while getting outbounds for account:{}", accIdentifier);
            return Collections.emptyMap();
        }
    }

    public int getSeverityId(String name) {
        try {
            log.info("The mode value is {}", mode);
            if (mode == 1) {
                log.info("Mode is one, Getting the data from redis");
                return redisUtilities.getSeverityId(name);
            }
            return cache.getSeverityId(name);
        } catch (Exception e) {
            log.error("Error occurred while getting severity id who's subTypeName is {}", name);
            return 0;
        }
    }

    public List<TenantOpenSearchDetails> getTenantOpenSearchDetails(String tenantIdentifier) {
        try {
            log.trace("The mode value is {}", mode);
            if (mode == 1) {
                log.trace("Mode is one, Getting the data from redis");
                return redisUtilities.getTenantOpenSearchDetails(tenantIdentifier);
            }
            return cache.getTenantOpenSearchDetails(tenantIdentifier);
        } catch (Exception e) {
            log.error("Error occurred while getting tenant opensearch mapping for tenant identifier {}", tenantIdentifier);
            return new ArrayList<>();
        }
    }

    public Account getAccountDetails(String accountIdentifier) {
        try {
            log.trace("The mode value is {}", mode);
            if (mode == 1) {
                log.trace("Mode is one, Getting the data from redis");
                return redisUtilities.getAccountDetails(accountIdentifier);
            }
            return cache.getAccountDetails(accountIdentifier);
        } catch (Exception e) {
            log.error("Error occurred while getting account details for accountIdentifier : {}", accountIdentifier, e);
            return null;
        }
    }

    public List<OSIndexZoneDetails> getHealIndexZones() {
        try {
            log.trace("The mode value is {}", mode);
            if (mode == 1) {
                log.trace("Mode is one, Getting the data from redis");
                return redisUtilities.getHealIndexZones();
            }
            return cache.getHealIndexZones();
        } catch (Exception e) {
            log.error("Error occurred while getting heal opensearch index zone mapping. ", e);
            return null;
        }
    }

    /**
     * This method retrieves the list of applications mapped to a specific service.
     *
     * @param accountIdentifier The identifier for the account.
     * @param serviceIdentifier The identifier for the service.
     * @return A list of BasicEntity objects representing the applications mapped to the service.
     */
    public List<BasicEntity> getApplicationsMappedToService(String accountIdentifier, String serviceIdentifier) {
        try {
            log.trace("The mode value is {}", mode);
            if (mode == 1) {
                log.trace("Mode is one, Getting the data from redis");
                return redisUtilities.getApplicationsMappedToService(accountIdentifier, serviceIdentifier);
            }
            return cache.getApplicationsMappedToService(accountIdentifier, serviceIdentifier);
        } catch (Exception e) {
            log.error("Error occurred while getting applications mapped to service for accountIdentifier : {}, service identifier : {}", accountIdentifier, serviceIdentifier, e);
            return Collections.emptyList();
        }
    }

}
