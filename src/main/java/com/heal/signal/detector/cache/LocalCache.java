package com.heal.signal.detector.cache;

import com.heal.configuration.pojos.*;
import com.heal.signal.detector.util.Constants;
import com.heal.signal.detector.util.RedisUtilities;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Set;

@Slf4j
@Component
public class LocalCache {

    @Autowired
    RedisUtilities redisUtilities;

    @Cacheable(value = Constants.ACCOUNTS, unless = "#result == null")
    public List<Account> getAccounts() {
        log.info("Account details are being fetched from Redis for the first time and will be stored in cache before it expires");
        return redisUtilities.getAccounts();
    }

    @Cacheable(value = Constants.APPLICATION_BY_IDENTIFIER, key = "#accIdentifier + ':' + #appIdentifier")
    public Application getApplicationByIdentifier(String accIdentifier, String appIdentifier) {
        log.info("Application details for application {} of account {} are being fetched from Redis for the first time and will be stored in cache before it expires", appIdentifier, accIdentifier);
        return redisUtilities.getApplicationDetails(accIdentifier, appIdentifier);
    }

    @Cacheable(value = Constants.COMPONENT_KPIS, key = "#accIdentifier + ':' + #componentIdentifier")
    public List<BasicKpiEntity> getComponentKpis(String accIdentifier, String componentIdentifier) {
        log.info("Component kpis for component {} in account {} are being fetched from Redis for the first time and will be stored in cache before it expires", componentIdentifier, accIdentifier);
        return redisUtilities.getComponentKPIs(accIdentifier, componentIdentifier);
    }

    @Cacheable(value = Constants.COMPONENT_KPIS_BY_IDENTIFIER, key = "#accIdentifier + ':' + #componentIdentifier + ':' + #kpiIdentifier")
    public BasicKpiEntity getComponentKpiByIdentifier(String accIdentifier, String componentIdentifier, String kpiIdentifier) {
        log.info("Component kpi details for KpiID {} of component {} in account {} are being fetched from Redis for the first time and will be stored in cache before it expires", kpiIdentifier, componentIdentifier, accIdentifier);
        return redisUtilities.getKPIDetails(accIdentifier, componentIdentifier, kpiIdentifier);
    }

    @Cacheable(value = Constants.SERVICE_BY_IDENTIFIER, key = "#accIdentifier + ':' + #serviceIdentifier")
    public Service getServiceDetailsByIdentifier(String accIdentifier, String serviceIdentifier) {
        log.info("Service details are being fetched from Redis for service {} for the first time and will be stored in cache before it expires", serviceIdentifier);
        return redisUtilities.getServiceDetails(accIdentifier, serviceIdentifier);
    }

    @Cacheable(value = Constants.SERVICE_MAINTENANCE_BY_IDENTIFIER, key = "#accIdentifier + ':' + #serviceIdentifier")
    public List<MaintenanceDetails> getServiceMaintenanceDetails(String accIdentifier, String serviceIdentifier) {
        log.info("Service maintenance details are being fetched from Redis for service {} for the first time and will be stored in cache before it expires", serviceIdentifier);
        return redisUtilities.getServiceMaintenanceDetails(accIdentifier, serviceIdentifier);
    }

    @Cacheable(value = Constants.INSTANCE_MAINTENANCE_BY_IDENTIFIER, key = "#accIdentifier + ':' + #instanceIdentifier")
    public List<MaintenanceDetails> getInstanceMaintenanceDetails(String accIdentifier, String instanceIdentifier) {
        log.info("Instance maintenance details are being fetched from Redis for service {} for the first time and will be stored in cache before it expires", instanceIdentifier);
        return redisUtilities.getInstanceMaintenanceDetails(accIdentifier, instanceIdentifier);
    }

    @Cacheable(value = Constants.INSTANCE_KPIS_BY_IDENTIFIER, key = "#accIdentifier + ':' + #instanceIdentifier + ':' + #kpiId")
    public CompInstKpiEntity getInstanceKPIDetails(String accIdentifier, String instanceIdentifier, int kpiId) {
        log.info("Instance kpi details for KpiID {} of component {} in account {} are being fetched from Redis for the first time and will be stored in cache before it expires", kpiId, instanceIdentifier, accIdentifier);
        return redisUtilities.getInstanceKPIDetails(accIdentifier, instanceIdentifier, kpiId);
    }

    @Cacheable(value = Constants.SERVICE_NEIGHBOURS_BY_IDENTIFIER, key = "#accIdentifier + ':' + #serviceIdentifier")
    public Set<BasicEntity> getNeighbours(String accIdentifier, String serviceIdentifier) {
        log.info("Service neighbour details for service {} in account {} are being fetched from Redis for the first time and will be stored in cache before it expires", serviceIdentifier, accIdentifier);
        return redisUtilities.getNeighbours(accIdentifier, serviceIdentifier);
    }

    @Cacheable(value = Constants.HEAL_TYPES, key = "#name")
    public int getSeverityId(String name) {
        log.info("Heal types detail is being fetched from redis for sub type name {}", name);
        return redisUtilities.getSeverityId(name);
    }

    @Cacheable(value = Constants.TENANTS, key = "#tenantIdentifier", unless = "#result == null || #result.isEmpty()")
    public List<TenantOpenSearchDetails> getTenantOpenSearchDetails(String tenantIdentifier) {
        log.debug("Fetching tenant opensearch mapping details for tenantIdentifier {} for the first time from redis", tenantIdentifier);
        return redisUtilities.getTenantOpenSearchDetails(tenantIdentifier);
    }

    @Cacheable(value = Constants.ACCOUNTS, key = "#accountIdentifier", unless = "#result == null")
    public Account getAccountDetails(String accountIdentifier) {
        log.debug("Fetching account details for the accountIdentifier : {} for the first time from redis.", accountIdentifier);
        return redisUtilities.getAccountDetails(accountIdentifier);
    }

    @Cacheable(value = Constants.HEAL_TYPES, key = "'heal_index_zones'", unless = "#result == null || #result.isEmpty()")
    public List<OSIndexZoneDetails> getHealIndexZones() {
        log.debug("Fetching heal opensearch index to zone mapping details for the first time from redis");
        return redisUtilities.getHealIndexZones();
    }


    @Cacheable(value = Constants.ACCOUNT_OUTBOUNDS, key = "#accountIdentifier + ':outbounds'", unless = "#result == null")
    public Map<Integer, List<BasicEntity>> getAccountOutbounds(String accountIdentifier) {
        log.debug("Fetching outbound details for the accountIdentifier : {} for the first time from redis.", accountIdentifier);
        return redisUtilities.getAccountOutbounds(accountIdentifier);
    }

    @Cacheable(value = Constants.ACCOUNT_SERVICES, key = "#accountIdentifier + ':services'", unless = "#result == null")
    public List<BasicEntity> getAccountServices(String accountIdentifier) {
        log.debug("Fetching service list for the accountIdentifier : {} for the first time from redis.", accountIdentifier);
        return redisUtilities.getAccountServices(accountIdentifier);
    }

    /**
     * This method retrieves the list of applications mapped to a specific service.
     *
     * @param accountIdentifier The identifier for the account.
     * @param serviceIdentifier The identifier for the service.
     * @return A list of BasicEntity objects representing the applications mapped to the service.
     */
    @Cacheable(value = Constants.APPLICATIONS, key = "#accountIdentifier + ':' + #serviceIdentifier", unless = "#result == null || #result.isEmpty()")
    public List<BasicEntity> getApplicationsMappedToService(String accountIdentifier, String serviceIdentifier) {
        log.debug("Fetching applications mapped to service for the accountIdentifier : {}, service : {} for the first time from redis", accountIdentifier, serviceIdentifier);
        return redisUtilities.getApplicationsMappedToService(accountIdentifier, serviceIdentifier);
    }
}
