package com.heal.signal.detector.scheduler;

import com.heal.signal.detector.util.HealthMetrics;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class MetricsScheduler {

    @Autowired
    private HealthMetrics healthMetrics;

    @Async(value = "ThreadPoolTaskExecutorHealthMetrics")
    @Scheduled(initialDelay = 1000, fixedRateString = "${health.metrics.update.interval.milliseconds:10000}")
    public void updateSnapshots() {
        healthMetrics.resetSnapshots();
        log.debug("Metrics scheduler method called.");
    }
}
