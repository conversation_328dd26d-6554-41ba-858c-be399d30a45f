package com.heal.signal.detector.scheduler;

import com.heal.configuration.pojos.opensearch.Anomalies;
import com.heal.signal.detector.config.OpenSearchConfig;
import com.heal.signal.detector.opensearch.SignalRepo;
import com.heal.signal.detector.pojos.AnomalyHelper;
import com.heal.signal.detector.pojos.ClientBulkRequestPojo;
import com.heal.signal.detector.pojos.RequestHelper;
import com.heal.signal.detector.util.HealthMetrics;
import lombok.extern.slf4j.Slf4j;
import org.opensearch.client.opensearch.OpenSearchClient;
import org.opensearch.client.opensearch.core.BulkRequest;
import org.opensearch.client.opensearch.core.BulkResponse;
import org.opensearch.client.opensearch.core.IndexRequest;
import org.opensearch.client.opensearch.core.UpdateRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Prasad - 05-04-2022
 */
@Slf4j
@Service
public class OSDataPushScheduler {

    private final Queue<RequestHelper> indexRequestListQueue = new ConcurrentLinkedQueue<>();
    private final Queue<RequestHelper> updateRequestListQueue = new ConcurrentLinkedQueue<>();
    private final Queue<AnomalyHelper> anomalyHelperQueue = new ConcurrentLinkedQueue<>();

    @Value("${opensearch.batch.size:50}")
    private int osBatchSize;

    @Value("${opensearch.batch.queue.max.size:10000}")
    private int maxQueueSize;

    @Autowired
    OpenSearchConfig openSearchConfig;
    @Autowired
    @Lazy
    SignalRepo signalRepo;
    @Autowired
    HealthMetrics metrics;

    @Value("${opensearch.index.search.retry:3}")
    public int maxRetry;

    public boolean addToQueue(RequestHelper updateRequest) {
        if (updateRequestListQueue.size() > maxQueueSize) {
            log.error("Maximum queue limit {} breached for updateRequests", maxQueueSize);
            metrics.updateErrors();
            return false;
        }
        return updateRequestListQueue.offer(updateRequest);
    }

    public boolean addToAnomalyHelper(AnomalyHelper anomalyHelper) {
        if (anomalyHelperQueue.size() > maxQueueSize) {
            log.error("Maximum queue limit {} breached for anomalyUpdateRequests", maxQueueSize);
            metrics.updateErrors();
            return false;
        }
        return anomalyHelperQueue.offer(anomalyHelper);
    }

    @Async(value = "ThreadPoolTaskExecutorPushToOS")
    @Scheduled(initialDelayString = "${opensearch.data.push.schedule.initial.delay:2}", fixedRateString = "${opensearch.data.push.schedule.interval:5}", timeUnit = TimeUnit.SECONDS)
    public void pushToOS() {
        long st = System.currentTimeMillis();
        int indexRequestListSize = 0;
        int updateRequestListSize = 0;

        Map<String, ClientBulkRequestPojo> accIndexRestClientMap = new HashMap<>();
        try {
            log.debug("Total number of index add request queue size:{}, OS batch:{}", indexRequestListQueue.size(), osBatchSize);
            int dataCounter = -1;
            while (indexRequestListQueue.peek() != null && ++dataCounter < osBatchSize) {
                RequestHelper requestHelper = indexRequestListQueue.poll();
                indexRequestListSize++;

                String accIndexKey = requestHelper.getAccountIdentifier() + "_" + requestHelper.getIndexName();
                IndexRequest<?> indexRequest = requestHelper.getIndexRequest();

                if (!accIndexRestClientMap.containsKey(accIndexKey)) {
                    OpenSearchClient openSearchClient = openSearchConfig.getOpenSearchClient(requestHelper.getAccountIdentifier(), requestHelper.getIndexName());
                    if (openSearchClient == null) {
                        log.error("NULL OpenSearch client for account {}, index {}. Skipping the index request.", requestHelper.getAccountIdentifier(), requestHelper.getIndexName());
                        return;
                    }

                    BulkRequest bulkRequest = new BulkRequest.Builder()
                            .operations(op ->
                                    op.index(indexReq -> indexReq
                                            .index(indexRequest.index())
                                            .id(indexRequest.id())
                                            .document(indexRequest.document())
                                    )).build();

                    accIndexRestClientMap.put(requestHelper.getAccountIdentifier() + "_" + requestHelper.getIndexName(), ClientBulkRequestPojo.builder()
                            .bulkRequest(bulkRequest)
                            .openSearchClient(openSearchClient)
                            .build());
                } else {
                    BulkRequest bulkRequest = accIndexRestClientMap.get(accIndexKey).getBulkRequest().toBuilder().operations(op ->
                                    op.index(indexReq -> indexReq
                                            .index(indexRequest.index())
                                            .id(indexRequest.id())
                                            .document(indexRequest.document())
                                    ))
                            .build();
                    accIndexRestClientMap.get(accIndexKey).setBulkRequest(bulkRequest);
                }
            }

            log.debug("Total number of index update request queue size:{}, OS batch:{}", updateRequestListQueue.size(), osBatchSize);
            dataCounter = -1;
            while (updateRequestListQueue.peek() != null && ++dataCounter < osBatchSize) {
                RequestHelper requestHelper = updateRequestListQueue.poll();
                updateRequestListSize++;

                String accIndexKey = requestHelper.getAccountIdentifier() + "_" + requestHelper.getIndexName();
                UpdateRequest<?,?> updateRequest = requestHelper.getUpdateRequest();
                if (!accIndexRestClientMap.containsKey(accIndexKey)) {
                    OpenSearchClient openSearchClient = openSearchConfig.getOpenSearchClient(requestHelper.getAccountIdentifier(), requestHelper.getIndexName());
                    if (openSearchClient == null) {
                        log.error("NULL OpenSearch client for account {}, zone {}. Skipping the update request.", requestHelper.getAccountIdentifier(), requestHelper.getIndexName());
                        return;
                    }

                    BulkRequest bulkRequest = new BulkRequest.Builder()
                            .operations(op ->
                                    op.update(updateReq -> updateReq
                                            .index(updateRequest.index())
                                            .id(updateRequest.id())
                                            .document(updateRequest.doc())
                                            .docAsUpsert(false)
                                    )).build();

                    accIndexRestClientMap.put(requestHelper.getAccountIdentifier() + "_" + requestHelper.getIndexName(), ClientBulkRequestPojo.builder()
                            .bulkRequest(bulkRequest)
                            .openSearchClient(openSearchClient)
                            .build());
                } else {
                    BulkRequest bulkRequest = accIndexRestClientMap.get(accIndexKey).getBulkRequest().toBuilder().operations(op ->
                                    op.update(updateReq -> updateReq
                                            .index(updateRequest.index())
                                            .id(updateRequest.id())
                                            .document(updateRequest.doc())
                                            .docAsUpsert(false)
                                    ))
                            .build();
                    accIndexRestClientMap.get(accIndexKey).setBulkRequest(bulkRequest);
                }
            }

            try {
                accIndexRestClientMap.values().forEach(c ->
                        // TODO: Temp fix as anomaly was not getting updated
//                        CompletableFuture.runAsync(() ->
                        {
                            try {
                                BulkResponse response = c.getOpenSearchClient().bulk(c.getBulkRequest());
                                if (response.errors()) {
                                    response.items().forEach(item -> {
                                        if (item.error() != null) {
                                            log.error("Failures during bulk indexing operation. Exception type: [{}], Reason: [{}]", item.error().type(),
                                                    item.error().causedBy() != null ? item.error().causedBy().reason() : item.error().reason());
                                            metrics.updateErrors();
                                        }
                                    });
                                } else {
                                    log.debug("Bulk indexing operation successful!");
                                }
                            } catch (Exception e) {
                                log.error("Exception during bulk indexing operation: ", e);
                                metrics.updateErrors();
                            }
                        });
            } catch (Exception e) {
                log.error("Exception while bulk operation in OpenSearch.", e);
                metrics.updateErrors();
            }
        } catch (Exception e) {
            metrics.updateErrors();
            log.error("Exception while scheduling data to push into OpenSearch", e);
        } finally {
            long t = System.currentTimeMillis() - st;
            log.debug("Pushed data of size {} to OpenSearch, time taken {} ms.", indexRequestListSize, t);
            log.debug("Updated data of size {} in OpenSearch, time taken {} ms.", updateRequestListSize, t);
        }
    }

    @Async(value = "ThreadPoolTaskExecutorPushUpdateQueriesToOS")
    @Scheduled(initialDelayString = "${opensearch.data.push.schedule.initial.delay:2}", fixedRateString = "${opensearch.data.push.schedule.interval:5}", timeUnit = TimeUnit.SECONDS)
    public void pushUpdateQueriesToOS() {
        long st = System.currentTimeMillis();
        int size = anomalyHelperQueue.size();
        try {
            List<AnomalyHelper> notUpdatedAnomalies = new ArrayList<>();
            List<AnomalyHelper> anomalyHelperList = new ArrayList<>();
            int dataCounter = -1;
            while (anomalyHelperQueue.peek() != null && ++dataCounter < osBatchSize) {
                anomalyHelperList.add(anomalyHelperQueue.poll());
            }

            if (!anomalyHelperList.isEmpty()) {
                Set<String> anomalyIds = new HashSet<>();
                Set<String> accountIds = new HashSet<>();
                final long[] initialAnomalyTime = {System.currentTimeMillis()};
                anomalyHelperList.forEach(anomalyHelper -> {
                    anomalyIds.add(anomalyHelper.getAnomalyId());
                    accountIds.add(anomalyHelper.getAccountId());
                    initialAnomalyTime[0] = Math.min(initialAnomalyTime[0], anomalyHelper.getAnomalyTime());
                });

                Set<Anomalies> anomaliesSet = signalRepo.getAnomalyById(anomalyIds, accountIds, initialAnomalyTime[0]);
                if (anomaliesSet == null || anomaliesSet.isEmpty()) {
                    log.error("No anomaly details found for anomalies {}", anomaliesSet);

                    anomalyHelperList.forEach(anomalyHelper -> {
                        if (anomalyHelper.getRetry() > 0) {
                            anomalyHelper.setRetry(anomalyHelper.getRetry() - 1);
                            notUpdatedAnomalies.add(anomalyHelper);
                        } else {
                            metrics.updateErrors();
                            log.error("Tried to get anomaly details for the for anomaly id:{}, signal id:{}, attempted {} times, could not get.",
                                    anomalyHelper.getAnomalyId(), anomalyHelper.getSignalIds(), maxRetry);
                        }
                    });
                } else {

                    Map<String, Anomalies> anomalyMap = anomaliesSet.stream().collect(Collectors.toMap(Anomalies::getAnomalyId, c -> c, (a, b) -> a));

                    for (AnomalyHelper helper : anomalyHelperList) {
                        Anomalies anomalyDetail = anomalyMap.get(helper.getAnomalyId());
                        if (anomalyDetail == null) {
                            log.error("Could not get the anomaly details for accountId:{}, anomalyId:{}, signalIds:{}, anomalyTime:{}",
                                    helper.getAccountId(), helper.getAnomalyId(), helper.getSignalIds(), helper.getAnomalyTime());
                            metrics.updateErrors();

                            if (helper.getRetry() > 0) {
                                helper.setRetry(helper.getRetry() - 1);
                                notUpdatedAnomalies.add(helper);
                            } else {
                                metrics.updateErrors();
                                log.error("Tried to get anomaly details for the for anomaly id:{}, signal id:{}, attempted {} times, could not get.",
                                        helper.getAnomalyId(), helper.getSignalIds(), maxRetry);
                            }
                        } else {
                            //Add multi anomaly get query here
                            boolean isUpdated = signalRepo.updateAnomalyIndex(helper.getAnomalyId(), helper.getAnomalyTime(), helper.getSignalIds(), helper.getAccountId(), anomalyDetail);
                            if (!isUpdated && helper.getRetry() > 0) {
                                helper.setRetry(helper.getRetry() - 1);
                                notUpdatedAnomalies.add(helper);
                            } else if (helper.getRetry() <= 0) {
                                metrics.updateErrors();
                                log.error("Tried to update the signal id:{} for anomaly id:{}, attempted {} times, could not update.", helper.getSignalIds(), helper.getAnomalyId(), maxRetry);
                            }
                        }
                    }
                }
            }

            notUpdatedAnomalies.forEach(this::addToAnomalyHelper);
            log.debug("No. of not updated anomalies:{}, anomaly helper queue size:{}, osBatchSize:{}", notUpdatedAnomalies.size(), size, osBatchSize);
        } catch (Exception e) {
            metrics.updateErrors();
            log.error("Exception while scheduling data to push into OpenSearch. Size:{}", size, e);
        } finally {
            log.debug("Pushed anomalies update requests of size {} to OpenSearch, time taken {} ms.", size, (System.currentTimeMillis() - st));
        }
    }
}