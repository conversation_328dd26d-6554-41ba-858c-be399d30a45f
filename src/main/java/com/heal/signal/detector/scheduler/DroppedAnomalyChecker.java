package com.heal.signal.detector.scheduler;

import com.heal.configuration.enums.SignalType;
import com.heal.signal.detector.pojos.FailedOpenSignalPojo;
import com.heal.signal.detector.process.BatchSignalProcessor;
import com.heal.signal.detector.process.GenericSignalProcessor;
import com.heal.signal.detector.process.InfoSignalProcessor;
import com.heal.signal.detector.util.LocalQueues;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class DroppedAnomalyChecker {

    @Autowired
    private BatchSignalProcessor batchSignalProcessor;

    @Autowired
    private InfoSignalProcessor infoSignalProcessor;

    @Autowired
    GenericSignalProcessor genericSignalProcessor;

    @Autowired
    private LocalQueues localQueues;

    @Async("ThreadPoolTaskDroppedAnomalyChecker")
    @Scheduled(initialDelay = 1, fixedRateString = "${dropped.anomaly.checker.scheduler.seconds:60}", timeUnit = TimeUnit.SECONDS)
    public void checkDroppedAnomaly() {

        log.info("Scheduler checkDroppedAnomaly() method called. Current Queue Size: {}", localQueues.getFailedQueue().size());
        while (localQueues.getFailedQueue().peek() != null) {

            FailedOpenSignalPojo failedSignals = localQueues.getFailedQueue().poll();
            if (failedSignals == null) {
                return;
            }

            log.debug("Received anomaly data from failed queue anomalyId: {}, signalType: {}, accountIdentifier: {}",
                    failedSignals.getAnomalyEvent().getAnomalyId(), failedSignals.getSignalType(), failedSignals.getAccount().getIdentifier());

            if (SignalType.EARLY_WARNING == failedSignals.getSignalType() || SignalType.PROBLEM == failedSignals.getSignalType()) {
                genericSignalProcessor.processSignalEvent(failedSignals.getSignalType(), failedSignals.getAnomalyEvent(), failedSignals.getKpiEntity(), failedSignals.getAccount(), failedSignals.getAppIds(), failedSignals.getSignalIds());
            } else if (SignalType.BATCH_JOB == failedSignals.getSignalType()) {
                batchSignalProcessor.processBatchJobEvent(failedSignals.getAnomalyEvent(), failedSignals.getAccount());
            } else if (SignalType.INFO == failedSignals.getSignalType()) {
                infoSignalProcessor.processInfoEvent(failedSignals.getAnomalyEvent(), failedSignals.getCompInstKpiEntity(), failedSignals.getAccount());
            }

        }

    }

}
