package com.heal.signal.detector.scheduler;

import com.heal.configuration.pojos.opensearch.SignalDetails;
import com.heal.configuration.util.DateHelper;
import com.heal.signal.detector.opensearch.SignalRepo;
import com.heal.signal.detector.pojos.SignalStatus;
import com.heal.signal.detector.service.ForwarderToQueue;
import com.heal.signal.detector.util.Commons;
import com.heal.signal.detector.util.HealthMetrics;
import com.heal.signal.detector.util.RedisUtilities;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

@Slf4j
@Component
public class SignalChecker {

    private final ConcurrentHashMap<String, Integer> signalRetryCountMap = new ConcurrentHashMap<>();

    @Autowired
    HealthMetrics healthMetrics;

    @Autowired
    RedisUtilities redisUtilities;

    @Autowired
    Commons commons;

    @Autowired
    SignalRepo signalRepo;

    @Autowired
    ForwarderToQueue forwarder;

    @Value("${opensearch.signals.index:heal_signals}")
    public String signalsIndex;

    @Value("${info.signal.close.window.time.minutes:5}")
    private int infoSignalIdleTimeInMin;

    @Value("${batch.job.signal.close.window.time.minutes:5}")
    private int batchSignalIdleTimeInMin;

    @Value("${signal.close.window.time.minutes:15}")
    private int signalIdleTimeInMin;

    @Value(("${heal.global.account.identifier:e573f852-5057-11e9-8fd2-b37b61e52317}"))
    private String globalAccountIdentifier;

    @Value("${signal.checker.retry.signal.details.from.os:3}")
    public int retryCountLimit;

//    @Async(value = "ThreadPoolTaskExecutorSignalChecker")
//    @Scheduled(initialDelay = 1000, fixedRateString = "${signal.cleaner.scheduler.milliseconds:60000}")
    public void checkSignals() {
        log.trace("Signal checker scheduler method called.");
        long stepStartTime = System.currentTimeMillis();
        long startTime = stepStartTime;

        try {
            // ServiceId, <SignalId, SignalTime>
            Map<String, Map<String, Long>> serviceSignals = redisUtilities.getOpenSignalsInRedis();
            log.debug("Total number of service signals from redis cache is {}", serviceSignals.size());
            log.debug("Time taken to get signals from Redis: {} ms", (System.currentTimeMillis() - stepStartTime));
            stepStartTime = System.currentTimeMillis();

            if (!serviceSignals.isEmpty()) {
                List<String> osIndexNames = signalRepo.getExistingSignalsIndexNames(globalAccountIdentifier);
                log.debug("Total number of signals index found from OpenSearch is {}", osIndexNames.size());
                log.debug("Time taken to get existing index names from OpenSearch: {} ms", (System.currentTimeMillis() - stepStartTime));
                stepStartTime = System.currentTimeMillis();

                Set<String> signalIds = new HashSet<>();
                AtomicLong minSignalTime = new AtomicLong(System.currentTimeMillis());
                serviceSignals.values().forEach(c -> {
                    c.values().forEach(signalTime -> minSignalTime.set(Math.min(minSignalTime.get(), signalTime)));
                    signalIds.addAll(c.keySet());
                });
                log.debug("Time taken to collect signal IDs from Redis data: {} ms", (System.currentTimeMillis() - stepStartTime));
                stepStartTime = System.currentTimeMillis();

                long et = System.currentTimeMillis();
                List<SignalDetails> signalDetailsList = signalRepo.getSignalById(signalIds, minSignalTime.get(), et, globalAccountIdentifier);
                log.debug("Time taken to get signal details by ID from OpenSearch: {} ms", (System.currentTimeMillis() - stepStartTime));
                if (signalDetailsList == null || signalDetailsList.isEmpty()) {
                    log.warn("No signal details found from OpenSearch for signalTime from {}, to {},  Signals {}.", minSignalTime.get(), et, signalIds);
                    signalDetailsList = new ArrayList<>();
                }
                Map<String, SignalDetails> signalDetailsMap = signalDetailsList.stream()
                        .collect(Collectors.toMap(SignalDetails::getSignalId, c -> c, (a, b) -> b));

                stepStartTime = System.currentTimeMillis();
                //Removing Closed/Upgraded/NoOpenSearchIndex signal details from redis in this loop
                serviceSignals.forEach((serviceId, signalIdToTimeMap) ->

                        signalIdToTimeMap.forEach((signalId, signalTime) -> {

                            boolean isSignalsIndexPresent = isSignalsIndexPresent(osIndexNames, signalTime);

                            if (isSignalsIndexPresent) {
                                SignalDetails signalDetails = signalDetailsMap.get(signalId);
                                log.trace("Checking SignalId:{}, startTime:{}, signal details:{}", signalId, signalTime, signalDetails);
                                if (signalDetails == null) {
                                    //If no signal details found from OS
                                    int currentRetryCount = signalRetryCountMap.getOrDefault(signalId, 0);
                                    if (currentRetryCount < retryCountLimit) {
                                        log.warn("No signal details found from OpenSearch for signal {}. Retrying (attempt {}/{}).", signalId, currentRetryCount + 1, retryCountLimit);
                                        signalRetryCountMap.put(signalId, currentRetryCount + 1);
                                    } else {
                                        log.warn("No signal details found from OpenSearch for signal {} after {} retries. Marking as closed in redis cache.", signalId, retryCountLimit);
                                        redisUtilities.updateServiceSignal(signalId, signalTime, Collections.singleton(serviceId), SignalStatus.CLOSED.name());
                                        redisUtilities.deleteSignalDetails(signalId);
                                        signalRetryCountMap.remove(signalId); // Remove from retry map once closed
                                    }
                                } else {
                                    // If signal detail is found, remove from retry map
                                    signalRetryCountMap.remove(signalId);
                                    if (!signalDetails.getCurrentStatus().equalsIgnoreCase(SignalStatus.OPEN.name())) {

                                        log.debug("Removing the closed signal from the redis cache. AccountId:{}, SignalId:{}, SignalTime:{}, ServiceId:{}",
                                                signalDetails.getAccountIdentifiers(), signalId, signalTime, serviceId);
                                        redisUtilities.updateServiceSignal(signalId, signalTime, Collections.singleton(serviceId), signalDetails.getCurrentStatus());
                                        redisUtilities.deleteSignalDetails(signalId);
                                    }
                                }
                            } else {
                                //If Signals Index already deleted from OS
                                log.debug("Removing the signal from the redis cache because it's corresponding OpenSearch Index is not present." +
                                        " SignalId:{}, SignalTime:{}, ServiceId:{}", signalId, signalTime, serviceId);
                                redisUtilities.updateServiceSignal(signalId, signalTime, Collections.singleton(serviceId), SignalStatus.CLOSED.name());
                                redisUtilities.deleteSignalDetails(signalId);
                            }
                        }));
                log.debug("Time taken for Redis reconciliation: {} ms", (System.currentTimeMillis() - stepStartTime));
                stepStartTime = System.currentTimeMillis();
            }
        } catch (Exception e) {
            healthMetrics.updateErrors();
            log.error("Error while checking the signal close details and remove the signals from redis cache.", e);
            log.info("Total time taken for checkSignals (ended in error): {} ms", (System.currentTimeMillis() - startTime));
            return;
        }

        Set<SignalDetails> openSignalDetailsSet = new HashSet<>();
        try {
            openSignalDetailsSet = new HashSet<>(signalRepo.getOpenSignals(globalAccountIdentifier, false, false));
            log.info("Time taken to get all open signals of size {} from OpenSearch: {} ms", openSignalDetailsSet.size(), (System.currentTimeMillis() - stepStartTime));
            if (openSignalDetailsSet.isEmpty()) {
                log.debug("No open signals found from OpenSearch.");
                return;
            }
            log.debug("No. of open signals:{}", openSignalDetailsSet.size());
        } catch (Exception e) {
            healthMetrics.updateErrors();
            log.error("Error while getting open signals from OpenSearch.", e);
            log.info("Total time taken for checkSignals (ended in error): {} ms", (System.currentTimeMillis() - startTime));
        }

        stepStartTime = System.currentTimeMillis();
        //Marking Under Maintenance/No Anomaly Update Signals' status to Closed in OpenSearch in this loop
        openSignalDetailsSet.forEach(signalDetails -> {
            try {
                if (signalDetails.getStartedTime() == null || signalDetails.getUpdatedTime() == null) {
                    log.error("Start time or update time is null for signalId:{}, startTime:{}, updateTime:{}.",
                            signalDetails.getSignalId(), signalDetails.getStartedTime(), signalDetails.getUpdatedTime());
                    healthMetrics.updateErrors();
                    return;
                }

                long idleTime = commons.getSignalIdealTime(signalDetails.getSignalType(), infoSignalIdleTimeInMin, batchSignalIdleTimeInMin, signalIdleTimeInMin);
                long latestTime = Math.max(signalDetails.getUpdatedTime(), signalDetails.getTxnAnomalyTime() == null ? 0 : signalDetails.getTxnAnomalyTime());
                long currentTime = Calendar.getInstance(TimeZone.getTimeZone("UTC")).getTimeInMillis();
                log.info("signal id : {}, updatedTime : {}, txnUpdatedTime : {}, currentTime:{} and meta data : {}", signalDetails.getSignalId(),
                        signalDetails.getUpdatedTime(), signalDetails.getTxnAnomalyTime(), currentTime, signalDetails.getMetadata());

                Map<String, String> newMetaData = new HashMap<>();
                if (!signalDetails.getMetadata().containsKey("end_time")) {
                    newMetaData.put("end_time", String.valueOf(latestTime + idleTime));
                }

                Set<String> accountIdentifierSet = signalDetails.getAccountIdentifiers();
                if (accountIdentifierSet == null) {
                    log.error("Account identifiers does not exist for signalId:{}.", signalDetails.getSignalId());
                    healthMetrics.updateErrors();
                    return;
                }

                boolean isMaintenanceShouldExclude = false;
                if (signalDetails.getMetadata().containsKey("lastMaintenanceExcluded")) {
                    long lastMaintenanceExcluded = Long.parseLong(signalDetails.getMetadata().getOrDefault("lastMaintenanceExcluded", "0"));
                    if (lastMaintenanceExcluded != 0L) {
                        log.info("currTime : {}, last maintenance exclude time :{}, SIGNAL_IDLE_TIME : {}", currentTime, lastMaintenanceExcluded, idleTime);
                        isMaintenanceShouldExclude = !((currentTime - idleTime) > lastMaintenanceExcluded);
                    }
                }

                boolean isMaintenance = false;
                if (!isMaintenanceShouldExclude) {
                    isMaintenance = commons.isServicesUnderMaintenance(accountIdentifierSet, signalDetails.getServiceIds(), signalDetails.getSignalId());
                }

                if (isMaintenance) {
                    newMetaData.put("closing_reason", "Signal closed as all impacted services are put under maintenance.");
                    newMetaData.put("end_time", String.valueOf(latestTime));
                } else {
                    newMetaData.put("closing_reason",
                            "Signal closed as no new event is received on any of the impacted services in last " + idleTime / 60000 + " minutes.");
                    newMetaData.put("end_time", String.valueOf(latestTime + idleTime));
                }

                if (((currentTime - latestTime) > idleTime)
                        || (signalDetails.getMetadata() != null && signalDetails.getMetadata().containsKey("end_time"))
                        || isMaintenance) {
                    signalDetails.setCurrentStatus(SignalStatus.CLOSED.name());
                    signalDetails.getMetadata().putAll(newMetaData);

                    //TODO:Can we introduce Async close here by CompletableFuture?
                    boolean isUpdated = signalRepo.closeSignal(signalDetails, accountIdentifierSet.stream().toList().get(0), null);
                    log.info("Dead signal : {} is successfully closed.", signalDetails.getSignalId());
                    healthMetrics.updateSignalCloseCount();
                    healthMetrics.updateSnapshots(signalDetails.getSignalId() + "_CLOSED", 1);

                    if (isUpdated) {
                        forwarder.sendSignalMessages(commons.getSignalProto(signalDetails, true,
                                false, false, true, null));
                    }
                } else {
                    forwarder.sendSignalMessages(commons.getSignalProto(signalDetails, true,
                            false, false, false, null));
                }
            } catch (Exception e) {
                healthMetrics.updateErrors();
                log.error("Error while checking the signal close details. signal id:{}", signalDetails.getSignalId(), e);
            }
        });
        log.debug("Time taken for processing and closing idle signals: {} ms", (System.currentTimeMillis() - stepStartTime));
        log.info("Total time taken for checkSignals: {} ms", (System.currentTimeMillis() - startTime));
    }

    private boolean isSignalsIndexPresent(List<String> indexNamesFromOS, Long signalTime) {
        List<String> indexNames = new ArrayList<>();
        DateHelper.getWeeksAsString(signalTime, signalTime).forEach(date ->
                indexNames.add(signalsIndex + "_" + date));

        return indexNamesFromOS.contains(indexNames.get(0));
    }

}
