package com.heal.signal.detector.process;

import com.appnomic.appsone.common.protbuf.AnomalyEventProtos;
import com.heal.configuration.enums.SignalType;
import com.heal.configuration.pojos.Account;
import com.heal.configuration.pojos.AnomalySummary;
import com.heal.configuration.pojos.Application;
import com.heal.configuration.pojos.BasicKpiEntity;
import com.heal.configuration.pojos.opensearch.SignalDetails;
import com.heal.signal.detector.cache.CacheWrapper;
import com.heal.signal.detector.opensearch.SignalRepo;
import com.heal.signal.detector.pojos.FailedOpenSignalPojo;
import com.heal.signal.detector.pojos.SignalStatus;
import com.heal.signal.detector.service.ForwarderToQueue;
import com.heal.signal.detector.util.Commons;
import com.heal.signal.detector.util.HealthMetrics;
import com.heal.signal.detector.util.LocalQueues;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

@Component
@Slf4j
public class BatchSignalProcessor {
    @Autowired
    Commons commons;

    @Autowired
    HealthMetrics metrics;

    @Autowired
    SignalRepo signalRepo;

    @Autowired
    CacheWrapper wrapper;

    @Value("${heal.batch.component.identifier:BatchProcess}")
    private String batchComponentIdentifier;

    @Value(("${heal.global.account.identifier:e573f852-5057-11e9-8fd2-b37b61e52317}"))
    private String globalAccountIdentifier;

    @Autowired
    ForwarderToQueue forwarder;

    @Autowired
    LocalQueues localQueues;

    public void processBatchJobEvent(AnomalyEventProtos.AnomalyEvent anomalyEvent, Account account) {
        long st = System.currentTimeMillis();

        Set<SignalDetails> openSignalsFromOS;
        try {

            try {
                openSignalsFromOS = signalRepo.getOpenSignals(account.getIdentifier(), true, true);
            } catch (Exception e) {
                log.error("Error occurred while fetching open signals. So pushing in failed queue. Will be processing it later " +
                                "for accountIdentifier: {}, anomalyId: {}",
                        account.getIdentifier(), anomalyEvent.getAnomalyId(), e);

                localQueues.addToFailedOpenSignalQueue(FailedOpenSignalPojo.builder()
                        .signalType(SignalType.BATCH_JOB)
                        .anomalyEvent(anomalyEvent)
                        .account(account)
                        .build());
                return;
            }

            String appId = anomalyEvent.getAppId(0);

            BasicKpiEntity kpiEntity = wrapper.getComponentKpiByIdentifier(globalAccountIdentifier, batchComponentIdentifier,
                    anomalyEvent.getBatchInfo().getKpiId());
            if (kpiEntity == null) {
                log.error("Invalid kpi identifier, account Identifier:{}, component identifier:{}, Anomaly event details:{}",
                        globalAccountIdentifier, batchComponentIdentifier, anomalyEvent);
                return;
            }

            SignalDetails signalDetails = openSignalsFromOS
                    .parallelStream()
                    .filter(s -> s.getSignalType().equalsIgnoreCase(SignalType.BATCH_JOB.name()))
                    .filter(s -> s.getServiceIds().contains(appId))
                    .findAny()
                    .orElse(null);

            Application application = wrapper.getApplicationByIdentifier(anomalyEvent.getAccountId(), appId);
            if (application == null) {
                log.error("Invalid application identifier, Anomaly event details:{}", anomalyEvent);
                return;
            }

            log.debug("Open batch anomaly id:{}, signals:{}", anomalyEvent.getAnomalyId(), signalDetails != null ? signalDetails.getSignalId() : null);

            Map<String, String> metaData = commons.getSignalMetaData(anomalyEvent, SignalType.BATCH_JOB);
            Map<String, String> anomalyEventMetadataMap = anomalyEvent.getBatchInfo().getMetadataMap();

            // No open signals for batch
            int severityId = Integer.parseInt(anomalyEvent.getBatchInfo().getThresholdSeverity());

            if (signalDetails == null) {

                String signalId = commons.createSignalId("B", account.getId(), kpiEntity.getId(), application.getId(), anomalyEvent.getEndTimeGMT() / 1000);

                signalDetails = commons.createSignal(signalId, anomalyEvent.getEndTimeGMT(), anomalyEvent.getEndTimeGMT(), 0L, SignalStatus.OPEN.name(),
                        Collections.singleton(anomalyEvent.getAnomalyId()), SignalType.BATCH_JOB, Collections.singleton(appId), Collections.singleton(appId),
                        Collections.singleton(anomalyEvent.getAnomalyId()), null, null, severityId, metaData, anomalyEvent.getAccountId());

                AnomalySummary anomalySummary = commons.getAnomalySummary(anomalyEvent.getKpis().getInstanceId(), kpiEntity.getIdentifier(),
                        kpiEntity.getCategoryDetails().getIdentifier(), Collections.singleton(application.getIdentifier()), SignalType.BATCH_JOB,
                        severityId, anomalyEvent.getEndTimeGMT(), false, anomalyEvent.getAnomalyId(), "ALL", "0",
                        anomalyEvent.getThresholdType(), anomalyEvent.getOperationType(), anomalyEventMetadataMap,
                        anomalyEvent.getBatchInfo().getValue(), anomalyEvent.getBatchInfo().getThresholdsMap(), anomalyEvent.getAccountId());

                boolean insertStatus = signalRepo.insertSignal(signalDetails, anomalyEvent.getAccountId(), anomalySummary, false, new HashSet<>(anomalyEvent.getAppIdList()), false, Integer.parseInt(anomalyEvent.getBatchInfo().getThresholdSeverity()));

                metrics.updateSignalOpenCount(1);
                metrics.updateSnapshots(signalDetails.getSignalId() + "_CREATED", 1);

                if (insertStatus) {
                    forwarder.sendSignalMessages(commons.getSignalProto(signalDetails, false,
                            false, false, false, anomalySummary));
                }
                log.info("Batch signal is created,anomaly id:{}, signal details:{}", anomalyEvent.getAnomalyId(), signalDetails);
            } else {
                if (signalDetails.getSeverityId() > severityId) {
                    severityId = signalDetails.getSeverityId();
                }

                AnomalySummary anomalySummary = commons.getAnomalySummary(anomalyEvent.getKpis().getInstanceId(), kpiEntity.getIdentifier(),
                        kpiEntity.getCategoryDetails().getIdentifier(), Collections.singleton(application.getIdentifier()), SignalType.BATCH_JOB,
                        severityId, anomalyEvent.getEndTimeGMT(), false, anomalyEvent.getAnomalyId(), "ALL", "0",
                        anomalyEvent.getThresholdType(), anomalyEvent.getOperationType(), anomalyEventMetadataMap,
                        anomalyEvent.getBatchInfo().getValue(), anomalyEvent.getBatchInfo().getThresholdsMap(), anomalyEvent.getAccountId());

                boolean updateStatus = signalRepo.updateSignal(anomalyEvent.getEndTimeGMT(), SignalStatus.OPEN.name(),
                        SignalStatus.OPEN.name(), metaData, anomalyEvent.getAnomalyId(), signalDetails.getSignalId(),
                        anomalyEvent.getAccountId(), severityId, anomalySummary, signalDetails.getStartedTime(), false, new HashSet<>(anomalyEvent.getAppIdList()), false, Integer.parseInt(anomalyEvent.getBatchInfo().getThresholdSeverity()));
                metrics.updateSignalUpdateCount(1);
                metrics.updateSnapshots(signalDetails.getSignalId() + "_UPDATED", 1);

                if (updateStatus) {
                    forwarder.sendSignalMessages(commons.getSignalProto(signalDetails, false,
                            severityId == signalDetails.getSeverityId(), false, false, anomalySummary));
                }
                log.info("Batch signal is updated,anomaly id:{}, signal details:{}", anomalyEvent.getAnomalyId(), signalDetails);
            }

            boolean queueUpdateStatus = signalRepo.updateAnomaly(anomalyEvent.getAnomalyId(), anomalyEvent.getEndTimeGMT(),
                    Collections.singleton(signalDetails.getSignalId()), anomalyEvent.getAccountId());
            if (queueUpdateStatus) {
                log.trace("Anomaly updated into scheduler queue for batch signal, signalId:{}, anomalyId:{}", signalDetails.getSignalId(), anomalyEvent.getAnomalyId());
            } else {
                log.error("Anomaly not updated into scheduler queue for batch signal, signalId:{}, anomalyId:{}, because of this anomaly wont be update with signal id.", signalDetails.getSignalId(), anomalyEvent.getAnomalyId());
            }
        } catch (Exception e) {
            metrics.updateErrors();
            log.error("Error occurred while processing batch signal for anomaly:{}", anomalyEvent, e);
        } finally {
            long processTime = System.currentTimeMillis() - st;
            log.debug("{}:Anomaly process time for batch signal is {} ms.", anomalyEvent.getAnomalyId(), processTime);
            metrics.updateProcessDetails("BatchAnomalyProcessTime", processTime);
            metrics.updateProcessDetailsCounter("BatchAnomalyProcessCounter", processTime);
        }
    }
}
