package com.heal.signal.detector.process;

import com.appnomic.appsone.common.protbuf.AnomalyEventProtos;
import com.appnomic.appsone.common.protbuf.SignalProtos;
import com.heal.signal.detector.util.CustomThreadExecutionService;
import com.heal.signal.detector.util.HealthMetrics;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.LinkedHashSet;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ProtoValidation {

    @Autowired
    SignalHandler signalHandler;

    @Autowired
    HealthMetrics metrics;

    @Autowired
    MLESignalProcessor mleSignalProcessor;

    @Value("${anomaly.delay.minutes:5}")
    public int anomalyDelayMinutes;
    @Autowired
    CustomThreadExecutionService customThreadExecutionService;

    public void validateAndProcessInputAnomalyEvent(AnomalyEventProtos.AnomalyEvent anomalyEvent) {
        try {
            if (anomalyEvent == null) {
                log.error("Anomaly event is null.");
                metrics.updateErrors();
                return;
            }

            log.trace("Anomaly Event Id:{}", anomalyEvent.getAnomalyId());
            if (anomalyEvent.getKpis().getKpiId().isEmpty() && anomalyEvent.getBatchInfo().getKpiId().isEmpty()) {
                log.error("Invalid anomaly event is received. Anomaly data received with out KPI details. Anomaly event details:{}", anomalyEvent);
                metrics.updateErrors();
                return;
            }

            if (anomalyEvent.getAccountId().trim().isEmpty()) {
                log.error("Invalid anomaly event is received. Anomaly data received empty account identifier. Anomaly event details:{}", anomalyEvent);
                metrics.updateErrors();
                return;
            }

            if (anomalyEvent.getAnomalyId().trim().isEmpty()) {
                log.error("Invalid anomaly event is received. Anomaly data received empty anomaly id. Anomaly event details:{}", anomalyEvent);
                metrics.updateErrors();
                return;
            }

            if (anomalyEvent.getEndTimeGMT() <= 0) {
                log.error("Invalid anomaly event is received. Anomaly data received with wrong end time. Anomaly event details:{}", anomalyEvent);
                metrics.updateErrors();
                return;
            }

            if (anomalyEvent.getStartTimeGMT() <= 0) {
                log.error("Invalid anomaly event is received. Anomaly data received with wrong start time. Anomaly event details:{}", anomalyEvent);
                metrics.updateErrors();
                return;
            }

            if (anomalyEvent.getEndTimeGMT() < anomalyEvent.getStartTimeGMT()) {
                log.error("Invalid anomaly event is received. Anomaly data received with start time is greater than end time. Anomaly event details:{}", anomalyEvent);
                metrics.updateErrors();
                return;
            }

            if (anomalyEvent.getAnomalyTriggerTimeGMT() <= 0) {
                log.error("Invalid anomaly event is received. Anomaly data received with wrong anomaly trigger time. Anomaly event details:{}", anomalyEvent);
                metrics.updateErrors();
                return;
            }

            long currentTime = OffsetDateTime.now(ZoneOffset.UTC).toEpochSecond() * 1000;
            long delayTimeRange = currentTime - anomalyDelayMinutes * 60L * 1000L;
            if (anomalyEvent.getAnomalyTriggerTimeGMT() <= delayTimeRange) {
                log.error("Invalid anomaly event is received. Anomaly data received is older than {} minutes will be dropped, delayRange:{}. Anomaly event details:{}", anomalyDelayMinutes, delayTimeRange, anomalyEvent);
                metrics.updateErrors();
                return;
            }

            if (anomalyEvent.getOperationType().trim().isEmpty()) {
                log.error("Invalid anomaly event is received. Anomaly data received with empty operation type. Anomaly event details:{}", anomalyEvent);
                metrics.updateErrors();
                return;
            }

            if (anomalyEvent.getThresholdType().trim().isEmpty()) {
                log.error("Invalid anomaly event is received. Anomaly data received with empty threshold type. Anomaly event details:{}", anomalyEvent);
                metrics.updateErrors();
                return;
            }

            String mapKey;
            if (anomalyEvent.getAppIdList().size() == 1) {
                mapKey = anomalyEvent.getAppId(0);
            } else {
                mapKey = String.valueOf(anomalyEvent.getAppIdList().stream().sorted().collect(Collectors.toCollection(LinkedHashSet::new)).hashCode());
            }

            if (customThreadExecutionService.getThreadPoolCoreSize() > 0) {
                customThreadExecutionService.executeOnAppThread(mapKey, () -> signalHandler.processAnomalyEvent(anomalyEvent));
            } else {
                signalHandler.processAnomalyEvent(anomalyEvent);
            }
        } catch (Exception e) {
            metrics.updateErrors();
            log.error("Error while processing event, anomalyData:{}", anomalyEvent, e);
        }
    }

    public void validateAndProcessMLESignals(SignalProtos.SignalDetails signalDetails) {
        try {
            if (signalDetails == null) {
                log.error("AI-OPS: Signal details is null.");
                metrics.updateErrors();
                return;
            }

            if (signalDetails.getSignalId().trim().isEmpty()) {
                log.error("AI-OPS: Invalid mle generated signal/incidents is received. Signal details:{}",
                        signalDetails);
                metrics.updateErrors();
                return;
            }

            if (signalDetails.getAccountId().trim().isEmpty()) {
                log.error("AI-OPS: Invalid mle generated signal/incidents is received. No account identifier found " +
                        "in Signal details:{}", signalDetails);
                metrics.updateErrors();
                return;
            }

            if (signalDetails.getAnomalyDetailsList().isEmpty()) {
                log.error("AI-OPS: Invalid mle generated signal/incidents is received, No anomaly list found in signal details: {}", signalDetails);
                return;
            }

            log.debug("AI-OPS: Signal Id:{}, Anomaly list size {}", signalDetails.getSignalId(),
                    signalDetails.getAnomalyDetailsList().size());


            if (signalDetails.getSignalType().trim().isEmpty()) {
                log.error("AI-OPS: Invalid mle generated signal/incidents is received. Signal has empty signal type ." +
                        " Signal details:{}", signalDetails);
                metrics.updateErrors();
                return;
            }

            if (signalDetails.getSignalStatus().trim().isEmpty()) {
                log.error("AI-OPS: Invalid mle generated signal/incidents is received. Signal has empty signal " +
                        "status . Signal details:{}", signalDetails);
                metrics.updateErrors();
                return;
            }

            if (signalDetails.getMetadataCount() == 0) {
                log.error("AI-OPS: Invalid mle generated signal/incidents is received. Signal has no metadata . " +
                        "Signal details:{}", signalDetails);
                metrics.updateErrors();
                return;
            }

            //forwarding the mle signals for processing.
            mleSignalProcessor.processSignalsProtoForMLE(signalDetails);
        } catch (Exception e) {
            metrics.updateErrors();
            log.error("AI-OPS: Error while processing signal/incidents, Signal Data:{}", signalDetails, e);
        }
    }
}