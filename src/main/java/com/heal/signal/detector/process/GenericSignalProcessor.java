package com.heal.signal.detector.process;

import com.appnomic.appsone.common.protbuf.AnomalyEventProtos;
import com.heal.configuration.enums.SignalType;
import com.heal.configuration.pojos.*;
import com.heal.configuration.pojos.opensearch.SignalDetails;
import com.heal.signal.detector.cache.CacheWrapper;
import com.heal.signal.detector.opensearch.SignalRepo;
import com.heal.signal.detector.pojos.FailedOpenSignalPojo;
import com.heal.signal.detector.pojos.SignalStatus;
import com.heal.signal.detector.service.ForwarderToQueue;
import com.heal.signal.detector.util.Commons;
import com.heal.signal.detector.util.HealthMetrics;
import com.heal.signal.detector.util.LocalQueues;
import com.heal.signal.detector.util.RedisUtilities;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class GenericSignalProcessor {

    @Autowired
    RedisUtilities redisUtilities;

    @Autowired
    Commons commons;

    @Autowired
    HealthMetrics metrics;

    @Autowired
    SignalRepo signalRepo;

    @Autowired
    ForwarderToQueue forwarder;

    @Autowired
    CacheWrapper wrapper;

    @Autowired
    LocalQueues localQueues;

    @Value("${signal.severity.id.high:433}")
    String highSeverityIdSignal;

    @Value("${signal.severity.id.medium:432}")
    String mediumSeverityIdSignal;

    @Value("${signal.severity.id.low:431}")
    String lowSeverityIdSignal;

    /**
     * Common method to process both EARLY_WARNING and PROBLEM signals.
     * @param signalType SignalType.EARLY_WARNING or SignalType.PROBLEM
     * @param anomalyEvent The anomaly event
     * @param kpiEntity The KPI entity
     * @param account The account
     * @param appIds Set of application IDs
     * @param signalIds Optional signal id provided by caller. If present, method skips searching for open signals and proceeds to create/update accordingly.sent, method skips searching for open signals and proceeds to create/update accordingly.
     * @return The signal ID if a new signal is created, or null if no new signal is created or an error occurs.
     */
    public String processSignalEvent(
            SignalType signalType,
            AnomalyEventProtos.AnomalyEvent anomalyEvent,
            BasicKpiEntity kpiEntity,
            Account account,
            Set<String> appIds,
            Set<String> signalIds
    ) {
        long st = System.currentTimeMillis();
        Set<SignalDetails> openSignalsFromOS;
        try {
            try {
                openSignalsFromOS = signalRepo.getOpenSignals(anomalyEvent.getAccountId(), true, true);
            } catch (Exception e) {
                log.error("Error occurred while fetching open signals. So pushing in failed queue. Will be processing it later " +
                        "for accountIdentifier: {}, anomalyId: {}",
                        account.getIdentifier(), anomalyEvent.getAnomalyId(), e);
                localQueues.addToFailedOpenSignalQueue(FailedOpenSignalPojo.builder()
                        .signalType(signalType)
                        .anomalyEvent(anomalyEvent)
                        .account(account)
                        .kpiEntity(kpiEntity)
                        .appIds(appIds)
                        .signalIds(signalIds)
                        .build());
                return null;
            }

            Set<Service> services = anomalyEvent.getKpis().getSvcIdList()
                    .stream()
                    .map(serviceIdentifier -> {
                        Service service = wrapper.getServiceDetailsByIdentifier(anomalyEvent.getAccountId(), serviceIdentifier);
                        if (service == null) {
                            log.error("Invalid service identifier, Anomaly event details:{}", anomalyEvent);
                            metrics.updateErrors();
                        }
                        return service;
                    }).filter(Objects::nonNull)
                    .collect(Collectors.toSet());

            long start = System.currentTimeMillis();
            Set<String> serviceIdentifiers = services.stream().map(Service::getIdentifier).collect(Collectors.toSet());

            Set<ControllerAlias> serviceAliasSet = redisUtilities.getServiceAliases();
            Map<String, String> dcDrServiceMap = new HashMap<>();
            Map<String, String> drDcServiceMap = new HashMap<>();
            if (serviceAliasSet != null && !serviceAliasSet.isEmpty()) {
                dcDrServiceMap.putAll(serviceAliasSet.stream().collect(Collectors.toMap(ControllerAlias::getDcControllerIdentifier, ControllerAlias::getDrControllerIdentifier)));
                drDcServiceMap.putAll(serviceAliasSet.stream().collect(Collectors.toMap(ControllerAlias::getDrControllerIdentifier, ControllerAlias::getDcControllerIdentifier)));
            }

            Set<SignalDetails> signals = new HashSet<>();
            Set<SignalDetails> openSignals = new HashSet<>();
            try {
                signals = commons.processAndGetOpenSignals(services, account.getIdentifier(), anomalyEvent.getAnomalyId(),
                        dcDrServiceMap, drDcServiceMap, openSignalsFromOS, signalType == SignalType.EARLY_WARNING, appIds);
            } catch (Exception e) {
                log.error("Error occurred while fetching open signals from Redis. So pushing in failed queue. Will be processing it later " +
                        "for accountIdentifier: {}, anomalyId: {}", account.getIdentifier(), anomalyEvent.getAnomalyId(), e);
                localQueues.addToFailedOpenSignalQueue(FailedOpenSignalPojo.builder()
                        .signalType(signalType)
                        .anomalyEvent(anomalyEvent)
                        .account(account)
                        .kpiEntity(kpiEntity)
                        .appIds(appIds)
                        .signalIds(signalIds)
                        .build());

                return null;
            }

            if (!signals.isEmpty() && signalIds != null && !signalIds.isEmpty()) {
                openSignals = signals.stream()
                        .filter(s -> signalIds.contains(s.getSignalId()))
                        .collect(Collectors.toSet());
            }

            log.debug("Open signals in Generic processor, anomaly id:{}, signal ids: {}",
                    anomalyEvent.getAnomalyId(), openSignals.stream().map(SignalDetails::getSignalId).collect(Collectors.joining()));

            metrics.updateProcessDetails("GenericOpenSignals", System.currentTimeMillis() - start);

            log.debug("Open {} signals for anomalyId:{}, accountId:{}, serviceIdentifiers:{}, signal ids:{}",
                    signalType.name(), anomalyEvent.getAnomalyId(), account.getIdentifier(), serviceIdentifiers,
                    openSignals.stream().map(SignalDetails::getSignalId).collect(Collectors.joining(", ")));

            Map<String, String> metaData = commons.getSignalMetaData(anomalyEvent, signalType);
            Map<String, String> anomalyEventMetadataMap = new HashMap<>(anomalyEvent.getKpis().getMetadataMap());

            if (openSignals.isEmpty()) {
                start = System.currentTimeMillis();
                log.info("No {} signal is found. So, we will create a new {} signal for anomalyId:{}, accountId:{}, serviceId:{}.",
                        signalType.name(), signalType.name(), anomalyEvent.getAnomalyId(), anomalyEvent.getAccountId(), String.join(",", serviceIdentifiers));

                int severityId = Integer.parseInt(anomalyEvent.getKpis().getThresholdSeverity());
                int servicesHashCode = String.join("-", serviceIdentifiers).hashCode();
                String newSignalId = commons.createSignalId(signalType == SignalType.EARLY_WARNING ? "E" : "P", account.getId(), Integer.parseInt(anomalyEvent.getKpis().getKpiId()),
                        servicesHashCode, anomalyEvent.getEndTimeGMT() / 1000);

                SignalDetails signalDetails = commons.createSignal(newSignalId, anomalyEvent.getEndTimeGMT(), anomalyEvent.getEndTimeGMT(), 0L,
                        SignalStatus.OPEN.name(), Collections.singleton(anomalyEvent.getAnomalyId()), signalType, serviceIdentifiers,
                        serviceIdentifiers, Collections.singleton(anomalyEvent.getAnomalyId()), null, null, severityId, metaData, anomalyEvent.getAccountId());

                AnomalySummary anomalySummary = commons.getAnomalySummary(anomalyEvent.getKpis().getInstanceId(), kpiEntity.getIdentifier(),
                        kpiEntity.getCategoryDetails().getIdentifier(), serviceIdentifiers, signalType, severityId, anomalyEvent.getEndTimeGMT(),
                        anomalyEvent.getKpis().getIsWorkload(), anomalyEvent.getAnomalyId(), anomalyEvent.getKpis().getKpiAttribute(),
                        kpiEntity.getGroupIdentifier(), anomalyEvent.getThresholdType(), anomalyEvent.getOperationType(),
                        anomalyEventMetadataMap, anomalyEvent.getKpis().getValue(), anomalyEvent.getKpis().getThresholdsMap(), anomalyEvent.getAccountId());

                boolean insertStatus = signalRepo.insertSignal(signalDetails, anomalyEvent.getAccountId(), anomalySummary, true, appIds, anomalyEvent.getKpis().getIsWorkload(), Integer.parseInt(anomalyEvent.getKpis().getThresholdSeverity()));

                if (insertStatus) {
                    boolean queueUpdateStatus = signalRepo.updateAnomaly(anomalyEvent.getAnomalyId(), anomalyEvent.getEndTimeGMT(),
                            Collections.singleton(newSignalId), anomalyEvent.getAccountId());
                    if (queueUpdateStatus) {
                        log.trace("Anomaly updated into scheduler queue for {} signal, signalId:{}, anomalyId:{}",
                                signalType.name(), signalDetails.getSignalId(), anomalyEvent.getAnomalyId());
                    } else {
                        log.error("Anomaly not updated into scheduler queue for {} signal, signalId:{}, anomalyId:{}," +
                                " because of this anomaly wont be update with signal id.", signalType.name(), signalDetails.getSignalId(), anomalyEvent.getAnomalyId());
                    }
                    metrics.updateSignalOpenCount(1);
                    metrics.updateSnapshots(signalDetails.getSignalId() + "_CREATED", 1);
                    forwarder.sendSignalMessages(commons.getSignalProto(signalDetails, false,
                            false, false, false, anomalySummary));
                    log.info("{} signal is created for anomalyId:{}, signal  id:{}, severity:{}, type:{}", signalType.name(), anomalyEvent.getAnomalyId(),
                            signalDetails.getSignalId(), signalDetails.getSeverityId(), signalDetails.getSignalType());
                } else {
                    log.error("{} signal creation failed, anomaly event will dropped for anomaly:{}, signalId:{}",
                            signalType.name(), anomalyEvent.getAnomalyId(), signalDetails.getSignalId());
                }
                metrics.updateProcessDetails("GenericCreateSignals", System.currentTimeMillis() - start);
                return newSignalId;
            } else {
                // Update existing signals if applicable, or upgrade path
                start = System.currentTimeMillis();
                List<String> severityLevels = Arrays.asList(lowSeverityIdSignal, mediumSeverityIdSignal, highSeverityIdSignal);
                log.info("Open signals found for anomalyId:{}, accountId:{}, serviceIdentifiers:{}, signals:{}.",
                        anomalyEvent.getAnomalyId(), anomalyEvent.getAccountId(), serviceIdentifiers,
                        openSignals.stream().map(SignalDetails::getSignalId).collect(Collectors.joining(",")));

                openSignals.forEach(signal -> {
                    int severityId = signal.getSeverityId();
                    boolean severityUpdated = false;
                    if (severityLevels.indexOf(signal.getSeverityId().toString()) < severityLevels.indexOf(anomalyEvent.getKpis().getThresholdSeverity())) {
                        severityId = Integer.parseInt(anomalyEvent.getKpis().getThresholdSeverity());
                        severityUpdated = true;
                    }
                    Map<String, String> updatedMetaData = signal.getMetadata();
                    updatedMetaData.putAll(metaData);

                    AnomalySummary anomalySummary = commons.getAnomalySummary(anomalyEvent.getKpis().getInstanceId(), kpiEntity.getIdentifier(),
                            kpiEntity.getCategoryDetails().getIdentifier(), serviceIdentifiers, SignalType.valueOf(signal.getSignalType()),
                            severityId, anomalyEvent.getEndTimeGMT(), anomalyEvent.getKpis().getIsWorkload(), anomalyEvent.getAnomalyId(),
                            anomalyEvent.getKpis().getKpiAttribute(), kpiEntity.getGroupIdentifier(), anomalyEvent.getThresholdType(), anomalyEvent.getOperationType(),
                            anomalyEvent.getKpis().getMetadataMap(), anomalyEvent.getKpis().getValue(), anomalyEvent.getKpis().getThresholdsMap(), anomalyEvent.getAccountId());

                    boolean updateStatus = false;
                    if (signalType.name().equals(SignalType.PROBLEM.name()) && signal.getSignalType().equals(SignalType.EARLY_WARNING.name())) {
                        signal.setCurrentStatus(SignalStatus.UPGRADED.name());
                        String createdSignalId = processSignalEvent(SignalType.PROBLEM, anomalyEvent, kpiEntity, account, appIds, null);
                        if (signal.getRelatedSignals() == null) {
                            signal.setRelatedSignals(new HashSet<>());
                        }
                        signal.getRelatedSignals().add(createdSignalId);
                        updateStatus = signalRepo.signalDetailsUpdateStatus(System.currentTimeMillis(), SignalStatus.UPGRADED.name(), signal.getMetadata(), signal.getSignalId(), signal.getRelatedSignals(), account.getIdentifier(), signal.getStartedTime(), true);
                    } else {
                        updateStatus = signalRepo.updateSignal(anomalyEvent.getEndTimeGMT(), signal.getCurrentStatus(), signal.getCurrentStatus(), updatedMetaData, anomalyEvent.getAnomalyId(), signal.getSignalId(), anomalyEvent.getAccountId(),
                                severityId, anomalySummary, anomalyEvent.getStartTimeGMT(), true, appIds, anomalyEvent.getKpis().getIsWorkload(), Integer.parseInt(anomalyEvent.getKpis().getThresholdSeverity()));
                    }

                    if (updateStatus) {
                        metrics.updateSignalUpdateCount(1);
                        metrics.updateSnapshots(signal.getSignalId() + "_UPDATED", 1);

                        log.trace("Signal updated in OS for {} signal, signalId:{}, anomalyId:{}", signalType.name(),
                                signal.getSignalId(), anomalyEvent.getAnomalyId());
                        forwarder.sendSignalMessages(commons.getSignalProto(signal, false,
                                severityUpdated, false, signal.getSignalType().equalsIgnoreCase(SignalStatus.UPGRADED.name()), anomalySummary));
                    } else {
                        log.error("Failed to update signal in OS for {} signal, signalId:{}, anomalyId:{}",
                                signalType.name(), signal.getSignalId(), anomalyEvent.getAnomalyId());
                    }
                });
                metrics.updateProcessDetails("GenericUpdateSignals", System.currentTimeMillis() - start);
            }
        } catch (Exception e) {
            log.error("Error occurred while processing anomaly event. anomalyEvent:{}, account:{}, kpiEntity:{}", anomalyEvent, account, kpiEntity, e);
            metrics.updateErrors();
        } finally {
            long processTime = System.currentTimeMillis() - st;
            log.debug("{}:Anomaly process time for {} signal is {} ms.", anomalyEvent.getAnomalyId(), signalType.name(), processTime);
            metrics.updateProcessDetails("GenericAnomalyProcessTime", processTime);
            metrics.updateProcessDetailsCounter("GenericAnomalyProcessCounter", processTime);
        }
        return null;
    }
}
