package com.heal.signal.detector.process;

import com.heal.configuration.pojos.*;
import com.heal.configuration.pojos.opensearch.SignalDetails;
import com.heal.signal.detector.util.HealthMetrics;
import com.heal.signal.detector.util.RedisUtilities;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class SignalDetailProcessor {
    @Autowired
    HealthMetrics metrics;

    @Autowired
    RedisUtilities redisUtilities;

    @Value("${signal.latest.events.count:10}")
    public int lastNEvents;

    public void updateSignalDetailsIntoRedis(String signalId, AnomalySummary anomalySummary, Set<String> appIds, boolean isWorkload, int anomalitySeverityId) {
        try {
            if (anomalySummary == null) {
                log.error("Anomaly summary is null for signalId:{}", signalId);
                return;
            }

            SignalSummary signalSummary = redisUtilities.getSignalSummary(signalId);
            if (signalSummary == null) {
                SignalSummary.AnomalySummary anomaly = SignalSummary.AnomalySummary.builder()
                        .anomalyId(anomalySummary.getAnomalyId())
                        .isWorkload(isWorkload)
                        .severityId(anomalitySeverityId)
                        .build();

                Set<SignalSummary.AnomalySummary> anomalySummaries = new HashSet<>();
                anomalySummaries.add(anomaly);

                signalSummary = SignalSummary.builder()
                        .signalId(signalId)
                        .startTime(anomalySummary.getEventTime())
                        .latestEventTime(anomalySummary.getEventTime())
                        .anomalies(anomalySummaries)
                        .signalType(anomalySummary.getSignalType().name())
                        .severityId(anomalySummary.getSeverityId())
                        .applicationIds(appIds)
                        .build();
            } else {
                signalSummary.getAnomalies().removeIf(a -> a.getAnomalyId().equals(anomalySummary.getAnomalyId()));
                SignalSummary.AnomalySummary anomaly = SignalSummary.AnomalySummary.builder()
                        .anomalyId(anomalySummary.getAnomalyId())
                        .isWorkload(isWorkload)
                        .severityId(anomalitySeverityId)
                        .build();
                signalSummary.setSignalType(anomalySummary.getSignalType().name());
                signalSummary.setSeverityId(anomalySummary.getSeverityId());
                signalSummary.setLatestEventTime(anomalySummary.getEventTime());
                signalSummary.getAnomalies().add(anomaly);
            }

            Set<AnomalySummary> anomalySummaries = redisUtilities.getSignalAnomalies(signalId);
            anomalySummaries.add(anomalySummary);

            Set<AnomalySummary> topNAnomalies = anomalySummaries.stream()
                    .sorted(Comparator.comparing(AnomalySummary::getEventTime, Comparator.reverseOrder()))
                    .limit(lastNEvents)
                    .collect(Collectors.toSet());

            redisUtilities.updateAnomalyForSignal(signalId, topNAnomalies);

            Map<String, SignalServiceSummary> servicesSummaryMap = redisUtilities.getSignalServiceSummary(signalId).stream()
                    .collect(Collectors.toMap(SignalServiceSummary::getServiceIdentifier, Function.identity()));
            log.trace("Signal summary, SignalId:{}, map: {}", signalId, servicesSummaryMap.keySet());

            Set<String> serviceIds = anomalySummary.getServiceId();

            SignalSummary finalSignalSummary = signalSummary;
            serviceIds.forEach(serviceId -> {
                SignalServiceSummary serviceSummary = servicesSummaryMap.getOrDefault(serviceId, new SignalServiceSummary());
                serviceSummary.setServiceIdentifier(serviceId);

                if (anomalySummary.getSevereAnomalyId() != null) {
                    serviceSummary.getSevereAnomalies().add(anomalySummary.getAnomalyId());
                } else {
                    serviceSummary.getDefaultAnomalies().add(anomalySummary.getAnomalyId());
                }

                if (anomalySummary.isTxnAnomaly()) {
                    serviceSummary.getRequests().add(anomalySummary.getTxnId());
                } else {
                    serviceSummary.getInstances().add(anomalySummary.getInstanceId());
                }

                serviceSummary.getCategories().put(anomalySummary.getKpiId(), anomalySummary.getCategoryId());
                serviceSummary.setLatestAnomalyTimeInGMT(anomalySummary.getEventTime());

                servicesSummaryMap.put(serviceId, serviceSummary);
            });

            redisUtilities.updateServicesSummaryForSignal(signalId, new HashSet<>(servicesSummaryMap.values()));

            if (anomalySummary.isTxnAnomaly() && anomalySummary.getTxnId() != null && !anomalySummary.getTxnId().trim().isEmpty()) {
                Map<String, SignalTransactionSummary> txnsSummaryMap = redisUtilities.getSignalTransactionSummary(signalId).stream()
                        .collect(Collectors.toMap(SignalTransactionSummary::getTransactionIdentifier, Function.identity()));
                log.trace("Transactions summary, SignalId:{}, map: {}", signalId, txnsSummaryMap);

                serviceIds.forEach(serviceId -> {
                    SignalTransactionSummary txnSummary = txnsSummaryMap.getOrDefault(anomalySummary.getTxnId(), new SignalTransactionSummary());
                    txnSummary.setTransactionIdentifier(anomalySummary.getTxnId());
                    txnSummary.setServiceIdentifier(serviceId);

                    if (anomalySummary.getSevereAnomalyId() != null) {
                        txnSummary.getSevereAnomalies().add(anomalySummary.getAnomalyId());
                    } else {
                        txnSummary.getDefaultAnomalies().add(anomalySummary.getAnomalyId());
                    }

                    txnSummary.getKpiCategories().put(anomalySummary.getKpiId(), anomalySummary.getCategoryId());
                    txnSummary.getKpiLatestTime().put(anomalySummary.getKpiId(), anomalySummary.getEventTime());
                    txnSummary.getKpiSeverity().put(anomalySummary.getKpiId(), anomalySummary.getSeverityId() + "");
                    txnSummary.setLatestEventTimeInGMT(anomalySummary.getEventTime());

                    txnsSummaryMap.put(anomalySummary.getTxnId(), txnSummary);
                });

                redisUtilities.updateTransactionsSummaryForSignal(signalId, new HashSet<>(txnsSummaryMap.values()));

            } else {

                Map<String, SignalInstanceSummary> instsSummaryMap = redisUtilities.getSignalInstanceSummary(signalId).stream()
                        .collect(Collectors.toMap(SignalInstanceSummary::getInstanceIdentifier, Function.identity()));
                log.trace("Instances summary, SignalId:{}, map: {}", signalId, instsSummaryMap.keySet());

                SignalInstanceSummary instanceSummary = instsSummaryMap.getOrDefault(anomalySummary.getInstanceId(), new SignalInstanceSummary());
                instanceSummary.setInstanceIdentifier(anomalySummary.getInstanceId());

                if (anomalySummary.getSevereAnomalyId() != null) {
                    instanceSummary.getSevereAnomalies().add(anomalySummary.getAnomalyId());
                } else {
                    instanceSummary.getDefaultAnomalies().add(anomalySummary.getAnomalyId());
                }

                instanceSummary.getKpiCategories().put(anomalySummary.getKpiId(), anomalySummary.getCategoryId());
                instanceSummary.getKpiLatestTime().put(anomalySummary.getKpiId(), anomalySummary.getEventTime());
                instanceSummary.getKpiSeverity().put(anomalySummary.getKpiId(), anomalySummary.getSeverityId() + "");
                instanceSummary.setLatestEventTimeInGMT(anomalySummary.getEventTime());

                instsSummaryMap.put(anomalySummary.getInstanceId(), instanceSummary);

                redisUtilities.updateInstancesSummaryForSignal(signalId, new HashSet<>(instsSummaryMap.values()));
            }

            log.trace("Signal signalId:{}", signalId);

            redisUtilities.updateSignalSummary(signalId, signalSummary);
        } catch (Exception e) {
            metrics.updateErrors();
            log.error("Error occurred while updating signal summary details into redis. signalId:{}, anomalySummary:{}", signalId, anomalySummary, e);
        }
    }

    public void updateServiceSignals(String signalId, Set<String> serviceId, long signalStartTime, String signalStatus, String appIdentifier) {
        try {
            redisUtilities.updateServiceOpenSignalsForApp(signalId, signalStartTime, serviceId, signalStatus, appIdentifier);
        } catch (Exception e) {
            metrics.updateErrors();
            log.error("Error occurred while updating service signal into redis. signalId:{}, serviceId:{}, signalStatus:{}",
                    signalId, serviceId, signalStatus, e);
        }
    }

    public void updateSignalDetails(SignalDetails signalDetail, boolean isDelete) {
        try {
            if (!isDelete) {
                redisUtilities.updateSignalDetails(signalDetail);
            } else {
                redisUtilities.deleteSignalDetails(signalDetail.getSignalId());
            }
        } catch (Exception e) {
            metrics.updateErrors();
            log.error("Error occurred while updating signal details into redis. signalId:{}", signalDetail.getSignalId(), e);
        }
    }

    /**
     * Removes an anomaly from Redis for the given signalId and anomalyId.
     * Updates summaries and cleans up if needed.
     * 
     * @param signalId The ID of the signal from which the anomaly should be removed.
     * @param anomalyId The ID of the anomaly to be removed.
     */
    public void removeAnomalyFromSignalInRedis(String signalId, String anomalyId) {
        try {
            // Remove anomaly from signal summary
            SignalSummary signalSummary = redisUtilities.getSignalSummary(signalId);
            if (signalSummary != null) {
                signalSummary.getAnomalies().removeIf(a -> a.getAnomalyId().equals(anomalyId));
                // Optionally update latestEventTime if needed
                // ...additional logic if required...
            }

            // Remove anomaly from anomaly summaries
            Set<AnomalySummary> anomalySummaries = redisUtilities.getSignalAnomalies(signalId);
            anomalySummaries.removeIf(a -> a.getAnomalyId().equals(anomalyId));

            // Update topNAnomalies
            Set<AnomalySummary> topNAnomalies = anomalySummaries.stream()
                    .sorted(Comparator.comparing(AnomalySummary::getEventTime, Comparator.reverseOrder()))
                    .limit(lastNEvents)
                    .collect(Collectors.toSet());
            redisUtilities.updateAnomalyForSignal(signalId, topNAnomalies);

            // Remove anomaly from service summaries
            Map<String, SignalServiceSummary> servicesSummaryMap = redisUtilities.getSignalServiceSummary(signalId).stream()
                    .collect(Collectors.toMap(SignalServiceSummary::getServiceIdentifier, Function.identity()));
            servicesSummaryMap.values().forEach(serviceSummary -> {
                serviceSummary.getSevereAnomalies().remove(anomalyId);
                serviceSummary.getDefaultAnomalies().remove(anomalyId);
                // Optionally update latestAnomalyTimeInGMT if needed
            });
            redisUtilities.updateServicesSummaryForSignal(signalId, new HashSet<>(servicesSummaryMap.values()));

            //TODO: check anomaly type and then remove

            // Remove anomaly from instance summaries
            Map<String, SignalInstanceSummary> instsSummaryMap = redisUtilities.getSignalInstanceSummary(signalId).stream()
                    .collect(Collectors.toMap(SignalInstanceSummary::getInstanceIdentifier, Function.identity()));
            instsSummaryMap.values().forEach(instanceSummary -> {
                instanceSummary.getSevereAnomalies().remove(anomalyId);
                instanceSummary.getDefaultAnomalies().remove(anomalyId);
            });
            redisUtilities.updateInstancesSummaryForSignal(signalId, new HashSet<>(instsSummaryMap.values()));

            // Remove anomaly from transaction summaries
            Map<String, SignalTransactionSummary> txnsSummaryMap = redisUtilities.getSignalTransactionSummary(signalId).stream()
                    .collect(Collectors.toMap(SignalTransactionSummary::getTransactionIdentifier, Function.identity()));
            txnsSummaryMap.values().forEach(txnSummary -> {
                txnSummary.getSevereAnomalies().remove(anomalyId);
                txnSummary.getDefaultAnomalies().remove(anomalyId);
            });
            redisUtilities.updateTransactionsSummaryForSignal(signalId, new HashSet<>(txnsSummaryMap.values()));

            log.info("Removed anomalyId:{} from signalId:{} in Redis.", anomalyId, signalId);
        } catch (Exception e) {
            log.error("Error while removing anomalyId:{} from signalId:{} in Redis.", anomalyId, signalId, e);
        }
    }
}
