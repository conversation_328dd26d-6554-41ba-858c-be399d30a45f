package com.heal.signal.detector.process;

import com.appnomic.appsone.common.protbuf.AnomalyEventProtos;
import com.heal.configuration.enums.SignalType;
import com.heal.configuration.pojos.*;
import com.heal.configuration.pojos.opensearch.Anomalies;
import com.heal.configuration.pojos.opensearch.SignalDetails;
import com.heal.signal.detector.cache.CacheWrapper;
import com.heal.signal.detector.opensearch.SignalRepo;
import com.heal.signal.detector.pojos.SignalStatus;
import com.heal.signal.detector.service.ForwarderToQueue;
import com.heal.signal.detector.util.Commons;
import com.heal.signal.detector.util.Constants;
import com.heal.signal.detector.util.HealthMetrics;
import com.heal.signal.detector.util.RedisUtilities;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import java.util.*;
import java.util.stream.Collectors;

@org.springframework.stereotype.Service
@Slf4j
public class SignalHandler {

    @Autowired
    Commons commons;

    @Autowired
    BatchSignalProcessor batchProcessor;

    @Autowired
    WarningSignalProcessor warningProcessor;

    @Autowired
    ProblemSignalProcessor problemProcessor;

    @Autowired
    GenericSignalProcessor genericSignalProcessor;

    @Autowired
    InfoSignalProcessor infoProcessor;

    @Autowired
    HealthMetrics metrics;

    @Autowired
    CacheWrapper wrapper;

    @Autowired
    SignalRepo signalRepo;

    @Autowired
    RedisUtilities redisUtilities;

    @Autowired
    ForwarderToQueue forwarder;

    @Value("${offset.from.gmt:********}")
    private long offsetFromGMT;

    @Value("${signal.severity.id.high:433}")
    String highSeverityIdSignal;

    @Value("${signal.severity.id.medium:432}")
    String mediumSeverityIdSignal;

    @Value("${signal.severity.id.low:431}")
    String lowSeverityIdSignal;


    public void processAnomalyEvent(AnomalyEventProtos.AnomalyEvent anomalyEvent) {
        log.trace("Anomaly event:{}", anomalyEvent.getAnomalyId());
        long st = System.currentTimeMillis();

        try {
            Account account = wrapper.getAccounts().parallelStream()
                    .filter(f -> f.getIdentifier().equals(anomalyEvent.getAccountId()))
                    .findAny()
                    .orElse(null);
            if (account == null) {
                log.error("Invalid account identifier, Anomaly event details:{}", anomalyEvent);
                return;
            }

            boolean isWorkload = anomalyEvent.getKpis().getIsWorkload();

            CompInstKpiEntity kpiEntity = null;
            if (!anomalyEvent.getKpis().getKpiId().trim().isEmpty()) {
                int kpiId = Integer.parseInt(anomalyEvent.getKpis().getKpiId());
                kpiEntity = wrapper.getInstanceKPIDetails(anomalyEvent.getAccountId(), anomalyEvent.getKpis().getInstanceId(), kpiId);
            }
            log.debug("KPI data:{}", kpiEntity);

            // Check instance wise maintenance window for component instance kpis
            if (!isWorkload && kpiEntity != null && kpiEntity.getIsMaintenanceExcluded() == 0) {
                List<MaintenanceDetails> maintenanceDetailsList =
                        wrapper.getInstanceMaintenanceDetails(anomalyEvent.getAccountId(), anomalyEvent.getKpis().getInstanceId());
                boolean isUnderMaintenance = commons.isUnderMaintenance(maintenanceDetailsList, anomalyEvent.getEndTimeGMT() + offsetFromGMT);
                if (isUnderMaintenance) {
                    metrics.updateMaintenanceEvents();
                    return;
                }
            }

            Set<String> svcIds = new HashSet<>();
            if (anomalyEvent.getKpis().getSvcIdCount() > 0) {
                svcIds = new HashSet<>(anomalyEvent.getKpis().getSvcIdList());
            }

            // Check maintenance window for service wise for all kpis.
            if (!svcIds.isEmpty() && (isWorkload || (kpiEntity != null && kpiEntity.getIsMaintenanceExcluded() == 0)) &&
                    commons.isServicesUnderMaintenance(Collections.singleton(anomalyEvent.getAccountId()), svcIds, "")) {
                metrics.updateMaintenanceEvents();
                return;
            }

            // Check the anomaly event is Batch KPI
            if (!anomalyEvent.getBatchInfo().getKpiId().trim().isEmpty()) {
                batchProcessor.processBatchJobEvent(anomalyEvent, account);
                return;
            }

            if (!isWorkload && kpiEntity == null) {
                log.error("Invalid kpi id received, Anomaly details:{}", anomalyEvent);
                metrics.updateErrors();
                return;
            }

            // Check the anomaly event for Info KPI.
            if (kpiEntity != null && kpiEntity.getIsInfo() == 1) {
                infoProcessor.processInfoEvent(anomalyEvent, kpiEntity, account);
                return;
            }

            // Check the anomaly event for Txn KPI,entry point and problem event kpis list.
            Service service = wrapper.getServiceDetailsByIdentifier(anomalyEvent.getAccountId(), anomalyEvent.getKpis().getSvcId(0));
            if (service == null) {
                log.error("Could not find service details for serviceId:{} in redis for anomalyId:{}", anomalyEvent.getKpis().getSvcId(0), anomalyEvent.getAnomalyId());
                return;
            }

            List<BasicKpiEntity> txnKpis = wrapper.getComponentKpis(anomalyEvent.getAccountId(), Constants.TRANSACTION_COMPONENT_IDENTIFIER);
            if (txnKpis.isEmpty()) {
                log.error("Could not find transaction kpis in redis for anomalyId:{}, accountId:{}.", anomalyEvent.getAccountId(), anomalyEvent.getAnomalyId());
                return;
            }

            BasicKpiEntity txnKpi = txnKpis.parallelStream()
                    .filter(e -> anomalyEvent.getKpis().getKpiId().equalsIgnoreCase(e.getId() + ""))
                    .findAny()
                    .orElse(null);

            Anomalies anomaly = signalRepo.getAnomalyById(anomalyEvent.getAnomalyId(), anomalyEvent.getAccountId(), anomalyEvent.getEndTimeGMT());

            Set<String> appIds = new HashSet<>(anomalyEvent.getAppIdList());

            Set<String> signalIds = null;
            Set<String> openSignalIds = new HashSet<>();
            // Collect only signalIds for services referenced in the anomaly event across all apps
            Set<String> anomalyServiceIds = new HashSet<>(anomalyEvent.getKpis().getSvcIdList());
            appIds.forEach(appId -> {
                try {
                    Map<String, Map<String, Long>> openSignals = redisUtilities.getOpenSignalsForApp(appId);
                    if (openSignals == null || openSignals.isEmpty()) return;
                    anomalyServiceIds.forEach(svcId -> {
                        Map<String, Long> signalTimeMap = openSignals.get(svcId);
                        if (signalTimeMap != null && !signalTimeMap.isEmpty()) {
                            openSignalIds.addAll(signalTimeMap.keySet());
                        }
                    });
                } catch (Exception e) {
                    log.error("Error fetching open signals for appId:{} while aggregating signalIds for anomalyId:{}", appId, anomalyEvent.getAnomalyId(), e);
                }
            });
            if (!openSignalIds.isEmpty()) {
                // Try to narrow down to signals already containing this anomaly (if summaries present)
                Set<String> matchedByAnomaly = openSignalIds.stream()
                        .map(redisUtilities::getSignalSummary)
                        .filter(Objects::nonNull)
                        .filter(ss -> ss.getAnomalies() != null && ss.getAnomalies().stream()
                                .anyMatch(a -> anomalyEvent.getAnomalyId().equals(a.getAnomalyId())))
                        .map(SignalSummary::getSignalId)
                        .collect(Collectors.toSet());
                signalIds = matchedByAnomaly.isEmpty() ? openSignalIds : matchedByAnomaly;
            }

                handleAnomalyEvent(anomalyEvent,
                        anomalyEvent.getKpis().getMetadataMap().getOrDefault("anomalyStatus", "OPEN"),
                        isWorkload,
                        signalIds,
                        anomalyEvent.getAccountId(),
                        anomalyEvent.getAnomalyId(),
                        Integer.parseInt(anomalyEvent.getKpis().getThresholdSeverity()),
                        anomalyEvent.getKpis().getMetadataMap(),
                        txnKpi,
                        kpiEntity,
                        account,
                        anomaly,
                        appIds);
        } catch (Exception e) {
            metrics.updateErrors();
            log.error("Error while processing event, anomalyData:{}", anomalyEvent, e);
        } finally {
            long processTime = System.currentTimeMillis() - st;
            log.debug("{}:Anomaly process time for signal is {} ms.", anomalyEvent.getAnomalyId(), processTime);
            metrics.updateProcessDetails("AnomalyProcessTime", processTime);
            metrics.updateProcessDetailsCounter("AnomalyProcessCounter", processTime);
        }
    }

    /**
     * Handles anomaly event processing for open, ongoing, and close anomaly types.
     * Updates or creates signals as per the business rules described.
     *
     * @param anomalyEvent The anomaly event containing details about the anomaly.
     * @param anomalyStatus The status of the anomaly event (OPEN, ONGOING, CLOSE).
     * @param isWorkload Indicates if the anomaly is related to workload.
     * @param signalIds The ID of the signal associated with the anomaly, if applicable.
     * @param accountIdentifier The identifier of the account associated with the anomaly.
     * @param anomalyId The identifier of the anomaly.
     * @param severityId The severity ID of the anomaly.
     * @param metaData Metadata associated with the anomaly event.
     * @param txnKpi The transaction KPI entity, if applicable.
     * @param kpiEntity The KPI entity associated with the anomaly.
     * @param account The account details for the anomaly.
     * @param anomaly The anomaly details.
     * @param appIds Set of application IDs associated with the anomaly.
     */
    public void handleAnomalyEvent(
            AnomalyEventProtos.AnomalyEvent anomalyEvent,
            String anomalyStatus, // "OPEN", "ONGOING", "CLOSE"
            boolean isWorkload,
            Set<String> signalIds,
            String accountIdentifier,
            String anomalyId,
            int severityId,
            Map<String, String> metaData,
            BasicKpiEntity txnKpi,
            CompInstKpiEntity kpiEntity,
            Account account,
            Anomalies anomaly,
            Set<String> appIds
    ) {

        List<String> severityLevels = Arrays.asList(lowSeverityIdSignal, mediumSeverityIdSignal, highSeverityIdSignal);
        final BasicKpiEntity basicKpiEntity = txnKpi != null ? txnKpi : kpiEntity;
        log.info("Processed anomaly event with {}: {}", txnKpi != null ? "txnKpi" : "kpiEntity", basicKpiEntity.getIdentifier());

        if ("OPEN".equalsIgnoreCase(anomalyStatus)) {
            SignalType signalType = (isWorkload && txnKpi != null) ? SignalType.PROBLEM : SignalType.EARLY_WARNING;
            genericSignalProcessor.processSignalEvent(signalType, anomalyEvent, basicKpiEntity, account, appIds, signalIds);
            log.info("Processed anomaly event with SignalType: {}", signalType);
        } else if ("ONGOING".equalsIgnoreCase(anomalyStatus)) {
            Set<String> anomalySignalIds = new HashSet<>(anomaly != null && anomaly.getSignalIds() != null ? anomaly.getSignalIds() : Collections.emptySet());
            if (signalIds != null) {
                anomalySignalIds.addAll(signalIds);
            }
            if (!anomalySignalIds.isEmpty()) {
                List<SignalDetails> signalDetails = signalRepo.getSignalById(anomalySignalIds, accountIdentifier);
                List<SignalDetails> openSignalDetails = (signalDetails == null ? Collections.<SignalDetails>emptyList() : signalDetails)
                        .stream()
                        .filter(s -> s.getCurrentStatus().equalsIgnoreCase(SignalStatus.OPEN.name()))
                        .toList();
                if (openSignalDetails.isEmpty()) {
                    // Use same flow as Open Anomaly
                    handleAnomalyEvent(anomalyEvent, "OPEN", isWorkload, null, accountIdentifier, anomalyId, severityId, metaData, txnKpi, kpiEntity, account, anomaly, appIds);
                    return;
                }
                openSignalDetails.forEach(signal -> {
                    if (severityLevels.indexOf(signal.getSeverityId().toString()) < severityLevels.indexOf(String.valueOf(severityId))) {
                        // Update severity for last signal, update anomaly with signalId
                        Map<String, String> updatedMetaData = signal.getMetadata();
                        updatedMetaData.putAll(metaData);
                        AnomalySummary anomalySummary = commons.getAnomalySummary(anomalyEvent.getKpis().getInstanceId(), basicKpiEntity.getIdentifier(),
                                basicKpiEntity.getCategoryDetails().getIdentifier(), anomaly != null ? anomaly.getServiceId() : null, SignalType.valueOf(signal.getSignalType()),
                                severityId, anomalyEvent.getEndTimeGMT(), anomalyEvent.getKpis().getIsWorkload(), anomalyEvent.getAnomalyId(),
                                anomalyEvent.getKpis().getKpiAttribute(), basicKpiEntity.getGroupIdentifier(), anomalyEvent.getThresholdType(), anomalyEvent.getOperationType(),
                                anomalyEvent.getKpis().getMetadataMap(), anomalyEvent.getKpis().getValue(), anomalyEvent.getKpis().getThresholdsMap(), anomalyEvent.getAccountId());

                        boolean updateStatus = signalRepo.updateSignal(anomalyEvent.getEndTimeGMT(), signal.getCurrentStatus(), signal.getCurrentStatus(), updatedMetaData, anomalyEvent.getAnomalyId(), signal.getSignalId(), accountIdentifier,
                                severityId, anomalySummary, anomalyEvent.getStartTimeGMT(), true, appIds, isWorkload, Integer.parseInt(anomalyEvent.getKpis().getThresholdSeverity()));
                        if (updateStatus) {
                            forwarder.sendSignalMessages(commons.getSignalProto(signal, false,
                                    true, false, false, anomalySummary));
                            log.info("Updated signal with higher severity for anomalyId:{}, signalId:{}, severityId:{}", anomalyEvent.getAnomalyId(), signal.getSignalId(), severityId);
                        }
                    } else {
                        log.debug("Signal already has higher or equal severity for {} signal, signalId:{}, anomalyId:{}",
                                signal.getSignalType(), signal.getSignalId(), anomalyEvent.getAnomalyId());
                    }
                });
            } else {
                // Use same flow as Open Anomaly
                log.warn("No open signals found for anomalyId:{}, processing as OPEN anomaly.", anomalyEvent.getAnomalyId());
                handleAnomalyEvent(anomalyEvent, "OPEN", isWorkload, null, accountIdentifier, anomalyId, severityId, metaData, txnKpi, kpiEntity, account, anomaly, appIds);
                return;
            }
        } else if ("CLOSE".equalsIgnoreCase(anomalyStatus)) {
            Set<String> anomalySignalIds = new HashSet<>(anomaly != null && anomaly.getSignalIds() != null ? anomaly.getSignalIds() : Collections.emptySet());
            if (signalIds != null) {
                anomalySignalIds.addAll(signalIds);
            }
            if (!anomalySignalIds.isEmpty()) {
                boolean updateStatus = false;

                List<SignalDetails> signalDetails = signalRepo.getSignalById(anomalySignalIds, accountIdentifier);
                List<SignalDetails> openSignalDetails = (signalDetails == null ? Collections.<SignalDetails>emptyList() : signalDetails)
                        .stream()
                        .filter(s -> s.getCurrentStatus().equalsIgnoreCase(SignalStatus.OPEN.name()))
                        .toList();
                if (openSignalDetails.isEmpty()) {
                    log.error("No open signal found for anomalyId:{} to close", anomalyEvent.getAnomalyId());
                    return;
                }

                for (SignalDetails signal : openSignalDetails) {
                    //                    signalRepo.removeAnomalyFromSignal(anomalyId, signal.getSignalId(), accountIdentifier, anomalyEvent.getStartTimeGMT(), signal.getCurrentStatus(), anomaly.getServiceId(), false);
                    signal.getAnomalies().remove(anomalyEvent.getAnomalyId());
                    // If no anomalies under signal, close signal
                    if (signal.getAnomalies().isEmpty()) {
                        String closingReason = "All anomalies closed under this signal.";
                        log.info("Closing signal: {}, reason: {}", signal.getSignalId(), closingReason);
                        closeSignal(signal, accountIdentifier, appIds, closingReason, null);
                    } else {
                        SignalSummary signalSummary = redisUtilities.getSignalSummary(signal.getSignalId());
                        if (signalSummary != null) {
                            signalSummary.getAnomalies().removeIf(anomalySummary -> anomalyId.equalsIgnoreCase(anomalySummary.getAnomalyId()));
                            // Determine the highest remaining anomaly severity using defined ordering (low < medium < high)
                            int updatedSeverityId = signalSummary.getAnomalies().stream()
                                    .map(SignalSummary.AnomalySummary::getSeverityId)
                                    .max(Comparator.comparingInt(s -> {
                                        int idx = severityLevels.indexOf(String.valueOf(s));
                                        return idx < 0 ? -1 : idx; // unknown severities rank lowest
                                    }))
                                    .orElse(signal.getSeverityId());
                            signalSummary.setSeverityId(updatedSeverityId);
                            signal.setSeverityId(updatedSeverityId);
                            if (signalSummary.getSignalType().equalsIgnoreCase(SignalType.PROBLEM.name())) {
                                // ...logic to check if anomalyId is workload...
                                boolean hasWorkload = signalSummary.getAnomalies().stream().anyMatch(SignalSummary.AnomalySummary::isWorkload);
                                if (!hasWorkload) {
                                    // Downgrade signal and create EARLY_WARNING
                                    signal.setCurrentStatus(SignalStatus.DOWNGRADED.name());
                                    signalSummary.setSignalType(SignalType.EARLY_WARNING.name());
                                    String newSignalId;
                                    newSignalId = genericSignalProcessor.processSignalEvent(SignalType.EARLY_WARNING, anomalyEvent, basicKpiEntity, account, appIds, null);
                                    signal.getRelatedSignals().add(newSignalId);
                                    signalRepo.signalDetailsUpdateStatus(System.currentTimeMillis(), SignalStatus.DOWNGRADED.name(), signal.getMetadata(), signal.getSignalId(), signal.getRelatedSignals(), accountIdentifier, signal.getStartedTime(), true);
                                    log.info("Downgraded signal: {}, created EARLY_WARNING signal: {}", signal.getSignalId(), newSignalId);
                                    // TODO: Should I delete the old signal details?
                                }
                            }
                            log.info("Signal summary updated for signalId: {}, remaining anomalies: {}", signal.getSignalId(), signalSummary.getAnomalies().size());
                            redisUtilities.updateSignalSummary(signal.getSignalId(), signalSummary);
                        }
                    }
                    updateStatus = signalRepo.removeAnomalyFromSignal(anomalyEvent.getAnomalyId(), signal.getSignalId(), accountIdentifier, anomalyEvent.getStartTimeGMT(), signal.getCurrentStatus(), anomaly != null ? anomaly.getServiceId() : null, true, appIds);

                    if (updateStatus) {
                        AnomalySummary anomalySummary = commons.getAnomalySummary(anomalyEvent.getKpis().getInstanceId(), basicKpiEntity.getIdentifier(),
                                basicKpiEntity.getCategoryDetails().getIdentifier(), anomaly != null ? anomaly.getServiceId() : null, SignalType.valueOf(signal.getSignalType()),
                                severityId, anomalyEvent.getEndTimeGMT(), anomalyEvent.getKpis().getIsWorkload(), anomalyEvent.getAnomalyId(),
                                anomalyEvent.getKpis().getKpiAttribute(), basicKpiEntity.getGroupIdentifier(), anomalyEvent.getThresholdType(), anomalyEvent.getOperationType(),
                                anomalyEvent.getKpis().getMetadataMap(), anomalyEvent.getKpis().getValue(), anomalyEvent.getKpis().getThresholdsMap(), anomalyEvent.getAccountId());
                        forwarder.sendSignalMessages(commons.getSignalProto(signal, false,
                                true, false, signal.getSignalType().equalsIgnoreCase(SignalStatus.DOWNGRADED.name()), anomalySummary));
                    }
                    log.info("Signal with anomalyId:{} is successfully removed from signalId:{}", anomalyEvent.getAnomalyId(), signal.getSignalId());
                }
            } else {
                log.error("No signal IDs found for anomalyId:{} to close.", anomalyEvent.getAnomalyId());
                return;
            }
        }
    }

    /**
     * Closes the signal and updates the status in the database.
     * Sends a message to the queue with the updated signal details.
     *
     * @param signalDetails The details of the signal to be closed.
     * @param accountId The identifier of the account associated with the signal.
     * @param appIds The set of application IDs associated with the signal.
     * @param closingReason The reason for closing the signal.
     * @param endTime The end time for the signal closure, if null current time will be used.
     */
    public void closeSignal(SignalDetails signalDetails, String accountId, Set<String> appIds, String closingReason, String endTime) {
        signalDetails.setCurrentStatus(SignalStatus.CLOSED.name());
        if (closingReason != null) {
            signalDetails.getMetadata().put("closing_reason", closingReason);
        }
        signalDetails.getMetadata().put("end_time", endTime != null ? endTime : String.valueOf(System.currentTimeMillis()));

        appIds.forEach(appId -> {
            boolean isUpdated = signalRepo.closeSignal(signalDetails, accountId, appId);
            log.info("Dead signal : {} is successfully closed.", signalDetails.getSignalId());
            metrics.updateSignalCloseCount();
            metrics.updateSnapshots(signalDetails.getSignalId() + "_CLOSED", 1);

            if (isUpdated) {
                forwarder.sendSignalMessages(commons.getSignalProto(signalDetails, false,
                        false, false, true, null));
            }
        });
        log.info("Signal with ID: {} is successfully closed for account: {} and apps: {}", signalDetails.getSignalId(), accountId, appIds);
    }
}
