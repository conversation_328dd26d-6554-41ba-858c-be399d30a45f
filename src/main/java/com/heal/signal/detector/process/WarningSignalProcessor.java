package com.heal.signal.detector.process;

import com.appnomic.appsone.common.protbuf.AnomalyEventProtos;
import com.heal.configuration.enums.SignalType;
import com.heal.configuration.pojos.*;
import com.heal.configuration.pojos.opensearch.SignalDetails;
import com.heal.signal.detector.cache.CacheWrapper;
import com.heal.signal.detector.opensearch.SignalRepo;
import com.heal.signal.detector.pojos.*;
import com.heal.signal.detector.service.ForwarderToQueue;
import com.heal.signal.detector.util.Commons;
import com.heal.signal.detector.util.HealthMetrics;
import com.heal.signal.detector.util.LocalQueues;
import com.heal.signal.detector.util.RedisUtilities;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class WarningSignalProcessor {
    @Autowired
    RedisUtilities redisUtilities;

    @Autowired
    Commons commons;

    @Autowired
    HealthMetrics metrics;

    @Autowired
    SignalRepo signalRepo;

    @Autowired
    ForwarderToQueue forwarder;

    @Value("${early.warning.processing.include.outbounds:1}")
    int includeOutbounds;

    @Autowired
    CacheWrapper wrapper;

    @Autowired
    LocalQueues localQueues;

    @Value("${signal.severity.id.high:433}")
    String highSeverityIdSignal;

    @Value("${signal.severity.id.medium:432}")
    String mediumSeverityIdSignal;

    @Value("${signal.severity.id.low:431}")
    String lowSeverityIdSignal;

    public void processEarlyWarningEvent(AnomalyEventProtos.AnomalyEvent anomalyEvent, BasicKpiEntity kpiEntity, Account account) {
        long st = System.currentTimeMillis();

        Set<SignalDetails> openSignalsFromOS;
        try {

            try {
                openSignalsFromOS = signalRepo.getOpenSignals(anomalyEvent.getAccountId(), true, true);
            } catch (Exception e) {
                log.error("Error occurred while fetching open signals. So pushing in failed queue. Will be processing it later " +
                        "for accountIdentifier: {}, anomalyId: {}", account.getIdentifier(), anomalyEvent.getAnomalyId(), e);

                localQueues.addToFailedOpenSignalQueue(FailedOpenSignalPojo.builder()
                        .signalType(SignalType.EARLY_WARNING)
                        .anomalyEvent(anomalyEvent)
                        .account(account)
                        .kpiEntity(kpiEntity)
                        .build());

                return;
            }

            Set<Service> services = anomalyEvent.getKpis().getSvcIdList()
                    .stream()
                    .map(serviceIdentifier -> {
                        Service service = wrapper.getServiceDetailsByIdentifier(anomalyEvent.getAccountId(), serviceIdentifier);
                        if (service == null) {
                            log.error("Invalid service identifier, Anomaly event details:{}", anomalyEvent);
                            metrics.updateErrors();
                        }
                        return service;
                    }).filter(Objects::nonNull)
                    .collect(Collectors.toSet());

            long start = System.currentTimeMillis();

            Set<String> serviceIdentifiers = services.stream().map(Service::getIdentifier).collect(Collectors.toSet());

            Set<ControllerAlias> serviceAliasSet = redisUtilities.getServiceAliases();
            Map<String, String> dcDrServiceMap = new HashMap<>();
            Map<String, String> drDcServiceMap = new HashMap<>();
            if (serviceAliasSet != null && !serviceAliasSet.isEmpty()) {
                dcDrServiceMap.putAll(serviceAliasSet.stream().collect(Collectors.toMap(ControllerAlias::getDcControllerIdentifier, ControllerAlias::getDrControllerIdentifier)));
                drDcServiceMap.putAll(serviceAliasSet.stream().collect(Collectors.toMap(ControllerAlias::getDrControllerIdentifier, ControllerAlias::getDcControllerIdentifier)));
            }

            Set<SignalDetails> signals;
            try {
                signals = commons.processAndGetOpenSignals(services, account.getIdentifier(), anomalyEvent.getAnomalyId(),
                        dcDrServiceMap, drDcServiceMap, openSignalsFromOS, true, new HashSet<>(anomalyEvent.getAppIdList()));
            } catch (Exception e) {
                log.error("Error occurred while fetching open signals from Redis. So pushing in failed queue. Will be processing it later " +
                        "for accountIdentifier: {}, anomalyId: {}", account.getIdentifier(), anomalyEvent.getAnomalyId(), e);
                localQueues.addToFailedOpenSignalQueue(FailedOpenSignalPojo.builder().signalType(SignalType.EARLY_WARNING)
                        .anomalyEvent(anomalyEvent)
                        .account(account)
                        .kpiEntity(kpiEntity)
                        .build());

                return;
            }

            log.debug("Open signals in EW processor, anomaly id:{}, signal ids: {}",
                    anomalyEvent.getAnomalyId(), signals.stream().map(SignalDetails::getSignalId).collect(Collectors.joining()));

            metrics.updateProcessDetails("WarnOpenSignals", System.currentTimeMillis() - start);

            log.debug("Open EarlyWarning signals for anomalyId:{}, accountId:{}, serviceIdentifiers:{}, signal ids:{}",
                    anomalyEvent.getAnomalyId(), account.getIdentifier(), serviceIdentifiers,
                    signals.stream().map(SignalDetails::getSignalId).collect(Collectors.joining(", ")));

            Map<String, String> metaData = commons.getSignalMetaData(anomalyEvent, SignalType.EARLY_WARNING);
            //NOTE:- Proto will give immutable map. Don't remove new HashMap<>() here, else java.lang.UnsupportedOperationException
            // will come in case of map.put().
            Map<String, String> anomalyEventMetadataMap = new HashMap<>(anomalyEvent.getKpis().getMetadataMap());

            if (signals.isEmpty()) {
                start = System.currentTimeMillis();
                log.info("No early warning signal is found. So, we will create a new early warning signal for anomalyId:{}, accountId:{}, serviceId:{}.",
                        anomalyEvent.getAnomalyId(), anomalyEvent.getAccountId(), String.join(",", serviceIdentifiers));

                int severityId = wrapper.getSeverityId(anomalyEvent.getKpis().getThresholdSeverity());

                int servicesHashCode = String.join("-", serviceIdentifiers).hashCode();

                String signalId = commons.createSignalId("E", account.getId(), Integer.parseInt(anomalyEvent.getKpis().getKpiId()),
                        servicesHashCode, anomalyEvent.getEndTimeGMT() / 1000);

                SignalDetails signalDetails = commons.createSignal(signalId, anomalyEvent.getEndTimeGMT(), anomalyEvent.getEndTimeGMT(), 0L,
                        SignalStatus.OPEN.name(), Collections.singleton(anomalyEvent.getAnomalyId()), SignalType.EARLY_WARNING, serviceIdentifiers,
                        serviceIdentifiers, Collections.singleton(anomalyEvent.getAnomalyId()), null, null, severityId, metaData, anomalyEvent.getAccountId());

                AnomalySummary anomalySummary = commons.getAnomalySummary(anomalyEvent.getKpis().getInstanceId(), kpiEntity.getIdentifier(),
                        kpiEntity.getCategoryDetails().getIdentifier(), serviceIdentifiers, SignalType.EARLY_WARNING, severityId, anomalyEvent.getEndTimeGMT(),
                        anomalyEvent.getKpis().getIsWorkload(), anomalyEvent.getAnomalyId(), anomalyEvent.getKpis().getKpiAttribute(),
                        kpiEntity.getGroupIdentifier(), anomalyEvent.getThresholdType(), anomalyEvent.getOperationType(),
                        anomalyEventMetadataMap, anomalyEvent.getKpis().getValue(), anomalyEvent.getKpis().getThresholdsMap(), anomalyEvent.getAccountId());

                boolean insertStatus = signalRepo.insertSignal(signalDetails, anomalyEvent.getAccountId(), anomalySummary, true, new HashSet<>(anomalyEvent.getAppIdList()), anomalyEvent.getKpis().getIsWorkload(), Integer.parseInt(anomalyEvent.getKpis().getThresholdSeverity()));

                if (insertStatus) {
                    boolean queueUpdateStatus = signalRepo.updateAnomaly(anomalyEvent.getAnomalyId(), anomalyEvent.getEndTimeGMT(),
                            Collections.singleton(signalId), anomalyEvent.getAccountId());
                    if (queueUpdateStatus) {
                        log.trace("Anomaly updated into scheduler queue for warning signal, signalId:{}, anomalyId:{}",
                                signalDetails.getSignalId(), anomalyEvent.getAnomalyId());
                    } else {
                        log.error("Anomaly not updated into scheduler queue for warning signal, signalId:{}, anomalyId:{}," +
                                " because of this anomaly wont be update with signal id.", signalDetails.getSignalId(), anomalyEvent.getAnomalyId());
                    }
//                signalRepo.serviceSignalDetailsCreate(account.getIdentifier(), signalId, signalDetails.getServiceIds());

                    metrics.updateSignalOpenCount(1);
                    metrics.updateSnapshots(signalDetails.getSignalId() + "_CREATED", 1);

                    forwarder.sendSignalMessages(commons.getSignalProto(signalDetails, false,
                            false, false, false, anomalySummary));

                    log.info("Early warning signal is created for anomalyId:{}, signal  id:{}, severity:{}, type:{}", anomalyEvent.getAnomalyId(),
                            signalDetails.getSignalId(), signalDetails.getSeverityId(), signalDetails.getSignalType());
                } else {
                    log.error("Early warning signal creation failed, anomaly event will dropped for anomaly:{}, signalId:{}",
                            anomalyEvent.getAnomalyId(), signalDetails.getSignalId());
                }
                metrics.updateProcessDetails("WarnCreateSignals", System.currentTimeMillis() - start);

            } else {

                start = System.currentTimeMillis();
                log.info("Open signals found for anomalyId:{}, accountId:{}, serviceIdentifiers:{}, signals:{}.",
                        anomalyEvent.getAnomalyId(), anomalyEvent.getAccountId(), serviceIdentifiers,
                        signals.stream().map(SignalDetails::getSignalId).collect(Collectors.joining(",")));

                OpenSignals openSignals = populateOpenSignalDetails(anomalyEvent, signals, serviceIdentifiers, metaData);
                int severityId = wrapper.getSeverityId(anomalyEvent.getKpis().getThresholdSeverity());

                Set<String> signalIds = new HashSet<>();

                //No problems found.
                if (openSignals.getPSignalHelper().getSignals().isEmpty()) {

                    // Update the early warnings with current anomaly data(Add anomaly to existing early warning).
                    openSignals.getEwSignalHelper().getSignals().forEach(ewSignalId -> {
                        SignalDetails signalDetails = openSignals.getEwSignalHelper().getSignalDetailsMap().get(ewSignalId);
                        SignalChanges signalChanges = openSignals.getEwSignalHelper().getSignalDetailChanges().get(ewSignalId);

                        AnomalySummary anomalySummary = commons.getAnomalySummary(anomalyEvent.getKpis().getInstanceId(), kpiEntity.getIdentifier(),
                                kpiEntity.getCategoryDetails().getIdentifier(), serviceIdentifiers, SignalType.EARLY_WARNING,
                                severityId, anomalyEvent.getEndTimeGMT(), anomalyEvent.getKpis().getIsWorkload(), anomalyEvent.getAnomalyId(),
                                anomalyEvent.getKpis().getKpiAttribute(), kpiEntity.getGroupIdentifier(), anomalyEvent.getThresholdType(), anomalyEvent.getOperationType(),
                                anomalyEventMetadataMap, anomalyEvent.getKpis().getValue(), anomalyEvent.getKpis().getThresholdsMap(), anomalyEvent.getAccountId());

                        boolean updateSignal = signalRepo.updateSignal(anomalyEvent.getEndTimeGMT(), SignalStatus.OPEN.name(), SignalStatus.OPEN.name(),
                                signalDetails.getMetadata(), anomalyEvent.getAnomalyId(), ewSignalId, account.getIdentifier(),
                                signalChanges.isSeverityChanged() ? severityId : signalDetails.getSeverityId(),
                                anomalySummary, signalDetails.getStartedTime(), true, new HashSet<>(anomalyEvent.getAppIdList()), anomalyEvent.getKpis().getIsWorkload(), Integer.parseInt(anomalyEvent.getKpis().getThresholdSeverity()));

                        if (updateSignal) {
                            signalIds.add(ewSignalId);

                            metrics.updateSignalUpdateCount(1);
                            metrics.updateSnapshots(signalDetails.getSignalId() + "_UPDATED", 1);

                            forwarder.sendSignalMessages(commons.getSignalProto(signalDetails, false,
                                    signalChanges.isSeverityChanged(), signalChanges.isServiceAdded(), false, anomalySummary));

                            log.info("Early warning signal is updated for anomalyId:{}, signal id:{}, severity:{}, type:{}",
                                    anomalyEvent.getAnomalyId(), signalDetails.getSignalId(), signalDetails.getSeverityId(), signalDetails.getSignalType());
                        } else {
                            log.error("Early warning signal update is failed for anomalyId:{}, signal id:{}, severity:{}, type:{}",
                                    anomalyEvent.getAnomalyId(), signalDetails.getSignalId(), signalDetails.getSeverityId(), signalDetails.getSignalType());
                        }

                    });

                    boolean queueUpdateStatus = signalRepo.updateAnomaly(anomalyEvent.getAnomalyId(), anomalyEvent.getEndTimeGMT(),
                            signalIds, anomalyEvent.getAccountId());
                    if (queueUpdateStatus) {
                        log.trace("Anomaly updated into scheduler queue for warning signal, signalIds:{}, anomalyId:{}", signalIds, anomalyEvent.getAnomalyId());
                    } else {
                        log.error("Anomaly not updated into scheduler queue for warning signal, signalIds:{}, anomalyId:{}," +
                                " because of this anomaly wont be update with signal id.", signalIds, anomalyEvent.getAnomalyId());
                    }
                } else {
                    // Update the problem with current anomaly data.
                    openSignals.getPSignalHelper().getSignals().forEach(pSignalId -> {
                        // Update the problem with current anomaly details
                        SignalDetails signalDetails = openSignals.getPSignalHelper().getSignalDetailsMap().get(pSignalId);
                        SignalChanges signalChanges = openSignals.getPSignalHelper().getSignalDetailChanges().get(pSignalId);

                        AnomalySummary anomalySummary = commons.getAnomalySummary(anomalyEvent.getKpis().getInstanceId(), kpiEntity.getIdentifier(),
                                kpiEntity.getCategoryDetails().getIdentifier(), serviceIdentifiers, SignalType.PROBLEM,
                                severityId, anomalyEvent.getEndTimeGMT(), anomalyEvent.getKpis().getIsWorkload(), anomalyEvent.getAnomalyId(),
                                anomalyEvent.getKpis().getKpiAttribute(), kpiEntity.getGroupIdentifier(), anomalyEvent.getThresholdType(),
                                anomalyEvent.getOperationType(), anomalyEventMetadataMap, anomalyEvent.getKpis().getValue(),
                                anomalyEvent.getKpis().getThresholdsMap(), anomalyEvent.getAccountId());

                        boolean updateSignal = signalRepo.updateSignal(anomalyEvent.getEndTimeGMT(), SignalStatus.OPEN.name(), SignalStatus.OPEN.name(),
                                signalDetails.getMetadata(), anomalyEvent.getAnomalyId(), pSignalId, account.getIdentifier(),
                                signalChanges.isSeverityChanged() ? severityId : signalDetails.getSeverityId(),
                                anomalySummary, signalDetails.getStartedTime(), true, new HashSet<>(anomalyEvent.getAppIdList()), anomalyEvent.getKpis().getIsWorkload(), Integer.parseInt(anomalyEvent.getKpis().getThresholdSeverity()));

                        boolean updateSignalStatus = signalRepo.signalDetailsUpdateStatus(anomalyEvent.getEndTimeGMT(), SignalStatus.OPEN.name(),
                                signalDetails.getMetadata(), pSignalId, openSignals.getEwSignalHelper().getSignals(), anomalyEvent.getAccountId(),
                                signalDetails.getStartedTime(), false);

                        if (updateSignalStatus && updateSignal) {
                            forwarder.sendSignalMessages(commons.getSignalProto(signalDetails, false,
                                    signalChanges.isSeverityChanged(), signalChanges.isServiceAdded(), false, anomalySummary));
                            signalIds.add(pSignalId);

                            metrics.updateSignalUpdateCount(1);
                            metrics.updateSnapshots(signalDetails.getSignalId() + "_UPDATED", 1);
                            log.info("Problem signal is updated for anomalyId:{}, signal id:{}, severity:{}, type:{}",
                                    anomalyEvent.getAnomalyId(), signalDetails.getSignalId(), signalDetails.getSeverityId(), signalDetails.getSignalType());

                        } else {
                            log.error("Problem signal updated is failed for anomalyId:{}, signal id:{}, severity:{}, type:{}",
                                    anomalyEvent.getAnomalyId(), signalDetails.getSignalId(), signalDetails.getSeverityId(), signalDetails.getSignalType());

                        }
                    });

                    boolean queueUpdateStatus = signalRepo.updateAnomaly(anomalyEvent.getAnomalyId(), anomalyEvent.getEndTimeGMT(),
                            signalIds, anomalyEvent.getAccountId());
                    if (queueUpdateStatus) {
                        log.trace("Anomaly updated into scheduler queue for warning signal, signalIds:{}, anomalyId:{}", signalIds, anomalyEvent.getAnomalyId());
                    } else {
                        log.error("Anomaly not updated into scheduler queue for warning signal, signalIds:{}, anomalyId:{}," +
                                " because of this anomaly wont be update with signal id.", signalIds, anomalyEvent.getAnomalyId());
                    }

                    // Update the early warnings with current anomaly data.(Upgrade early warning)
                    openSignals.getEwSignalHelper().getSignals().forEach(ewSignalId -> {
                        SignalDetails signalDetails = openSignals.getEwSignalHelper().getSignalDetailsMap().get(ewSignalId);
                        SignalChanges signalChanges = openSignals.getEwSignalHelper().getSignalDetailChanges().get(ewSignalId);

                        signalDetails.getStatusDetails().add(SignalStatus.UPGRADED.name());
                        signalDetails.getMetadata().put("end_time", anomalyEvent.getEndTimeGMT() + "");

                        if (signalDetails.getRelatedSignals() != null) {
                            signalDetails.getRelatedSignals().addAll(openSignals.getPSignalHelper().getSignals());
                        } else {
                            signalDetails.setRelatedSignals(new HashSet<>(openSignals.getPSignalHelper().getSignals()));
                        }

                        boolean updateStatus = signalRepo.signalDetailsUpdateStatus(anomalyEvent.getEndTimeGMT(), SignalStatus.UPGRADED.name(),
                                signalDetails.getMetadata(), ewSignalId, signalDetails.getRelatedSignals(), anomalyEvent.getAccountId(),
                                signalDetails.getStartedTime(), false);

                        if (updateStatus) {

                            metrics.updateSignalUpdateCount(1);
                            metrics.updateSnapshots(signalDetails.getSignalId() + "_UPDATED", 1);

                            AnomalySummary anomalySummary = commons.getAnomalySummary(anomalyEvent.getKpis().getInstanceId(), kpiEntity.getIdentifier(),
                                    kpiEntity.getCategoryDetails().getIdentifier(), serviceIdentifiers, SignalType.PROBLEM,
                                    severityId, anomalyEvent.getEndTimeGMT(), anomalyEvent.getKpis().getIsWorkload(), anomalyEvent.getAnomalyId(),
                                    anomalyEvent.getKpis().getKpiAttribute(), kpiEntity.getGroupIdentifier(), anomalyEvent.getThresholdType(),
                                    anomalyEvent.getOperationType(), anomalyEventMetadataMap, anomalyEvent.getKpis().getValue(),
                                    anomalyEvent.getKpis().getThresholdsMap(), anomalyEvent.getAccountId());

                            forwarder.sendSignalMessages(commons.getSignalProto(signalDetails, false,
                                    signalChanges.isSeverityChanged(), signalChanges.isServiceAdded(), true, anomalySummary));

                            log.info("WSP:Early warning id:{} is upgraded to problem id:{}, anomalyId:{}, signal  id:{}, severity:{}, type:{}",
                                    ewSignalId, openSignals.getPSignalHelper(), anomalyEvent.getAnomalyId(), signalDetails.getSignalId(),
                                    signalDetails.getSeverityId(), signalDetails.getSignalType());

                        } else {
                            log.error("WSP:Early warning id:{} upgrade is failed for problem id:{}, anomalyId:{}, signal  id:{}, severity:{}, type:{}",
                                    ewSignalId, openSignals.getPSignalHelper(), anomalyEvent.getAnomalyId(), signalDetails.getSignalId(),
                                    signalDetails.getSeverityId(), signalDetails.getSignalType());
                        }
                    });
                }

                metrics.updateProcessDetails("WarnUpdateSignals", System.currentTimeMillis() - start);
            }
        } catch (Exception e) {
            log.error("Error occurred while processing anomaly event. anomalyEvent:{}, account:{}, kpiEntity:{}", anomalyEvent, account, kpiEntity, e);
            metrics.updateErrors();
        } finally {
            long processTime = System.currentTimeMillis() - st;
            log.debug("{}:Anomaly process time for warning signal is {} ms.", anomalyEvent.getAnomalyId(), processTime);
            metrics.updateProcessDetails("WarnAnomalyProcessTime", processTime);
            metrics.updateProcessDetailsCounter("WarnAnomalyProcessCounter", processTime);
        }
    }

    private OpenSignals populateOpenSignalDetails(AnomalyEventProtos.AnomalyEvent anomalyEvent, Set<SignalDetails> openSignals,
                                                  Set<String> serviceIdentifiers, Map<String, String> metaData) {

//        int higherSeverityId = wrapper.getSeverityId("Severe");
        List<String> severityLevels = Arrays.asList(lowSeverityIdSignal, mediumSeverityIdSignal, highSeverityIdSignal);

        SignalHelper ewSignalHelper = new SignalHelper();
        ewSignalHelper.getAnomalies().add(anomalyEvent.getAnomalyId());
        ewSignalHelper.getRcaAnomalies().add(anomalyEvent.getAnomalyId());
        ewSignalHelper.getRcaServices().addAll(serviceIdentifiers);

        SignalHelper pSignalHelper = new SignalHelper();
        String severityId = anomalyEvent.getKpis().getThresholdSeverity();
        pSignalHelper.getAnomalies().add(anomalyEvent.getAnomalyId());
        pSignalHelper.getRcaAnomalies().add(anomalyEvent.getAnomalyId());
        pSignalHelper.getRcaServices().addAll(serviceIdentifiers);

        Set<String> serviceIds = new HashSet<>();

        boolean isValidAnomaly = true;
        for (SignalDetails signalDetails : openSignals) {

            if (anomalyEvent.getEndTimeGMT() < signalDetails.getStartedTime()) {
                log.error("We will drop this anomaly because anomaly time:{} is less than the signal id:{}, start time:{} for anomaly:{}",
                        anomalyEvent.getEndTimeGMT(), signalDetails.getSignalId(), signalDetails.getStartedTime(), anomalyEvent);
                isValidAnomaly = false;
                break;
            }
            SignalChanges signalChanges = SignalChanges.builder()
                    .isSeverityChanged(false)
                    .isServiceAdded(false)
                    .build();

            signalDetails.setAccountIdentifiers(new HashSet<>() {{
                add(anomalyEvent.getAccountId());
                if (signalDetails.getAccountIdentifiers() != null) {
                    addAll(signalDetails.getAccountIdentifiers());
                }
            }});

            signalDetails.getMetadata().putAll(metaData);

            if (signalDetails.getSignalType().equalsIgnoreCase(SignalType.EARLY_WARNING.name())) {
                ewSignalHelper.getSignals().add(signalDetails.getSignalId());
                ewSignalHelper.getAnomalies().addAll(signalDetails.getAnomalies());
                ewSignalHelper.getAffectedServices().addAll(signalDetails.getServiceIds());
                if (signalDetails.getRootCauseServiceIds() != null) {
                    ewSignalHelper.getRcaServices().addAll(signalDetails.getRootCauseServiceIds());
                }
                if (signalDetails.getRootCauseAnomalyIds() != null) {
                    ewSignalHelper.getRcaAnomalies().addAll(signalDetails.getRootCauseAnomalyIds());
                }

                // Check the signal is updating from default to severe
//                if (higherSeverityId != signalDetails.getSeverityId() && higherSeverityId == severityId) {
//                    signalDetails.setSeverityId(severityId);
//                    signalChanges.setSeverityChanged(true);
//                }
                if (severityLevels.indexOf(signalDetails.getSeverityId().toString()) < severityLevels.indexOf(severityId)) {
                    signalDetails.setSeverityId(Integer.valueOf(severityId));
                    signalChanges.setSeverityChanged(true);
                }

                signalChanges.setServiceAdded(!signalDetails.getServiceIds().containsAll(serviceIdentifiers));
                ewSignalHelper.getSignalDetailChanges().put(signalDetails.getSignalId(), signalChanges);
                ewSignalHelper.getSignalDetailsMap().put(signalDetails.getSignalId(), signalDetails);

            } else if (signalDetails.getServiceIds().containsAll(serviceIdentifiers)) {
                pSignalHelper.getSignals().add(signalDetails.getSignalId());

                // Check the signal is updating from default to severe
//                if (higherSeverityId != signalDetails.getSeverityId() && higherSeverityId == severityId) {
//                    signalDetails.setSeverityId(severityId);
//                    signalChanges.setSeverityChanged(true);
//                }
                if (severityLevels.indexOf(signalDetails.getSeverityId().toString()) < severityLevels.indexOf(severityId)) {
                    signalDetails.setSeverityId(Integer.valueOf(severityId));
                    signalChanges.setSeverityChanged(true);
                }

                signalChanges.setServiceAdded(!signalDetails.getServiceIds().containsAll(serviceIdentifiers));

                pSignalHelper.getSignalDetailChanges().put(signalDetails.getSignalId(), signalChanges);
                pSignalHelper.getSignalDetailsMap().put(signalDetails.getSignalId(), signalDetails);
            } else if (signalDetails.getSignalType().equalsIgnoreCase(SignalType.PROBLEM.name())) {
                pSignalHelper.getSignals().add(signalDetails.getSignalId());
                pSignalHelper.getAnomalies().addAll(signalDetails.getAnomalies());
                pSignalHelper.getAffectedServices().addAll(signalDetails.getServiceIds());
                if (signalDetails.getRootCauseServiceIds() != null) {
                    pSignalHelper.getRcaServices().addAll(signalDetails.getRootCauseServiceIds());
                }
                if (signalDetails.getRootCauseAnomalyIds() != null) {
                    pSignalHelper.getRcaAnomalies().addAll(signalDetails.getRootCauseAnomalyIds());
                }

                // Check the signal is updating from default to severe
//                if (higherSeverityId != signalDetails.getSeverityId() && higherSeverityId == severityId) {
//                    signalDetails.setSeverityId(severityId);
//                    signalChanges.setSeverityChanged(true);
//                }
                if (severityLevels.indexOf(signalDetails.getSeverityId().toString()) < severityLevels.indexOf(severityId)) {
                    signalDetails.setSeverityId(Integer.valueOf(severityId));
                    signalChanges.setSeverityChanged(true);
                }

                signalChanges.setServiceAdded(!signalDetails.getServiceIds().containsAll(serviceIdentifiers));
                pSignalHelper.getSignalDetailChanges().put(signalDetails.getSignalId(), signalChanges);
                pSignalHelper.getSignalDetailsMap().put(signalDetails.getSignalId(), signalDetails);
            }

            serviceIds.addAll(signalDetails.getServiceIds());
        }
        serviceIds.addAll(serviceIdentifiers);

        return OpenSignals.builder()
                .ewSignalHelper(ewSignalHelper)
                .pSignalHelper(pSignalHelper)
                .isValidAnomaly(isValidAnomaly)
                .serviceIds(serviceIds)
                .build();

    }
}
