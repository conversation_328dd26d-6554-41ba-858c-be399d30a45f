package com.heal.signal.detector.process;

import com.appnomic.appsone.common.protbuf.SignalProtos;
import com.heal.configuration.enums.SignalType;
import com.heal.configuration.pojos.AnomalySummary;
import com.heal.configuration.pojos.BasicEntity;
import com.heal.configuration.pojos.SignalSummary;
import com.heal.configuration.pojos.opensearch.SignalDetails;
import com.heal.signal.detector.cache.CacheWrapper;
import com.heal.signal.detector.opensearch.SignalRepo;
import com.heal.signal.detector.pojos.SignalStatus;
import com.heal.signal.detector.service.ForwarderToQueue;
import com.heal.signal.detector.util.Commons;
import com.heal.signal.detector.util.HealthMetrics;
import com.heal.signal.detector.util.RedisUtilities;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> - 04-03-2024
 */

@Slf4j
@Component
public class MLESignalProcessor {

    @Autowired
    HealthMetrics healthMetrics;
    @Autowired
    SignalRepo signalRepo;
    @Autowired
    Commons commons;
    @Autowired
    CacheWrapper cacheWrapper;
    @Autowired
    RedisUtilities redisUtilities;
    @Autowired
    ForwarderToQueue forwarder;
    @Autowired
    SignalHandler signalHandler;

    public void processSignalsProtoForMLE(SignalProtos.SignalDetails signalDetails) {
        log.debug("AI-OPS: Method:processSignalsProtoForMLE - MLE Signal Processor. SignalID {}",
                signalDetails.getSignalId());
        long startTime = System.currentTimeMillis();

        try {
            // To process closed signals
            if (signalDetails.getSignalStatus().equalsIgnoreCase(SignalStatus.CLOSED.name()) || signalDetails.getMetadataMap().containsKey("end_time")) {
                List<SignalDetails> signals = signalRepo.getSignalById(Collections.singleton(signalDetails.getSignalId()), signalDetails.getAccountId());
                for(SignalDetails signal : signals) {
                    SignalSummary signalSummary = redisUtilities.getSignalSummary(signal.getSignalId());
                    signalHandler.closeSignal(signal, signalDetails.getAccountId(), signalSummary.getApplicationIds(),
                            signalDetails.getMetadataMap().getOrDefault("closing_reason", null),
                            signalDetails.getMetadataMap().getOrDefault("end_time", null));
                }
                return;
            }
            if (signalDetails.getAnomalyDetailsList().size() != 1) {
                log.error("AI-OPS: Rejecting signals as it contains {} anomaly. SignalID {}",
                        signalDetails.getAnomalyDetailsList().size(), signalDetails.getSignalId());
                healthMetrics.updateErrors();
                return;
            }

            SignalDetails signalById = signalRepo.getMLESignalById(signalDetails.getSignalId(), signalDetails.getStartTime(),
                    signalDetails.getAccountId());

            Set<String> anomalyIds = signalDetails.getAnomalyDetailsList()
                    .stream()
                    .map(SignalProtos.AnomalyDetail::getAnomalyId)
                    .collect(Collectors.toSet());

            SignalProtos.AnomalyDetail anomalyEvent = signalDetails.getAnomalyDetailsList().get(0);
            log.debug("AI-OPS: anomaly event {} for signal id {}", anomalyEvent, signalDetails.getSignalId());

            int anomalySeverityId = Long.valueOf(anomalyEvent.getSeverityId()).intValue();
            int signalSeverityId = Long.valueOf(signalDetails.getSignalSeverityId()).intValue();

            log.info("AI-OPS: severity id found from anomaly details coming from signal protos. signalId {}, anomaly " +
                            "severity Id {}, signal severity Id {}, signal update time {}",
                    signalDetails.getSignalId(), anomalySeverityId, signalSeverityId, signalDetails.getUpdateTime());

            anomalySeverityId = signalSeverityId == 0 ? anomalySeverityId : signalSeverityId;

            //NOTE:- Proto will give immutable map. Don't remove new HashMap<>() here, else java.lang.UnsupportedOperationException
            // will come in case of map.put().
            Map<String, String> metadataMap = new HashMap<>(anomalyEvent.getMetadataMap());
            metadataMap.put("Source", "MLE");

            if (signalById == null) {
                log.warn("AI-OPS: No open signals found for the account:{}, signalId:{}",
                        signalDetails.getAccountId(), signalDetails.getSignalId());

                Set<String> rootCauseServiceIds = new HashSet<>();
                if (!signalDetails.getRelatedSignalsList().isEmpty()) {
                    List<SignalDetails> relatedSignalDetails = signalRepo.getMLERelatedSignalsById(new HashSet<>(signalDetails.getRelatedSignalsList()),
                            signalDetails.getAccountId());
                    if (relatedSignalDetails != null && !relatedSignalDetails.isEmpty()) {
                        rootCauseServiceIds.addAll(relatedSignalDetails.stream()
                                .map(SignalDetails::getRootCauseServiceIds)
                                .flatMap(Collection::stream)
                                .collect(Collectors.toSet()));
                    } else {
                        log.warn("AI-OPS: No related signals details found in OpenSearch for signal ids {}.", signalDetails.getRelatedSignalsList());
                    }
                }

                Set<String> serviceIds = new HashSet<>(anomalyEvent.getServiceIdsList());

                Set<String> appIds = serviceIds.stream()
                    .flatMap(serviceId -> cacheWrapper.getApplicationsMappedToService(signalDetails.getAccountId(), serviceId).stream())
                    .map(BasicEntity::getName)
                    .collect(Collectors.toSet());


                List<String> relatedSignal = signalDetails.getRelatedSignalsList();
                log.info("AI-OPS: total related signal is {}, Entry point service Id {} ", relatedSignal.size(), signalDetails.getEntryServiceIdsList());
                Set<String> relatedSignalSet = new HashSet<>();
                if (!relatedSignal.isEmpty()) {
                    log.debug("AI-OPS: Related signal for signal Id {} with related signal {}",
                            signalDetails.getSignalId(), relatedSignal);
                    relatedSignalSet.addAll(relatedSignal);
                }

                Set<String> entryServiceId = signalDetails.getEntryServiceIdsList().isEmpty() ? null : new HashSet<>(signalDetails.getEntryServiceIdsList());

                SignalDetails newSignal = commons.createSignal(signalDetails.getSignalId(), signalDetails.getStartTime(),
                        signalDetails.getUpdateTime(), 0L, signalDetails.getSignalStatus(),
                        Collections.singleton(anomalyEvent.getAnomalyId()), SignalType.valueOf(signalDetails.getSignalType()),
                        serviceIds, rootCauseServiceIds.isEmpty() ? serviceIds : rootCauseServiceIds,
                        anomalyIds, entryServiceId, relatedSignalSet, anomalySeverityId, metadataMap, signalDetails.getAccountId());

                AnomalySummary anomalySummary = commons.getAnomalySummary(anomalyEvent.getInstanceId(), anomalyEvent.getKpiId(),
                        anomalyEvent.getCategoryId(), new HashSet<>(anomalyEvent.getServiceIdsList()), SignalType.valueOf(signalDetails.getSignalType()),
                        anomalySeverityId, anomalyEvent.getAnomalyTime(), anomalyEvent.getIsWorkLoad(), anomalyEvent.getAnomalyId(),
                        anomalyEvent.getKpiAttribute(), anomalyEvent.getKpiGroupId(), anomalyEvent.getThresholdType(), anomalyEvent.getOperationType(),
                        metadataMap, anomalyEvent.getKpiValue(), anomalyEvent.getThresholdsMap(), signalDetails.getAccountId());

                boolean insertStatus = signalRepo.insertSignal(newSignal, signalDetails.getAccountId(), anomalySummary, false, appIds, anomalyEvent.getIsWorkLoad(), anomalySeverityId);
                if (insertStatus) {
                    log.info("AI-OPS: MLE signal inserted successfully for the anomalyId:{}, accountId:{} with signalID:{}",
                            anomalyEvent.getAnomalyId(), signalDetails.getAccountId(), signalDetails.getSignalId());

                    // TODO: send signal protos to rmq with updated value
                    forwarder.sendSignalMessages(commons.getSignalProto(newSignal, false,
                            false, false, false, anomalySummary));
                    log.debug("AI-OPS: Pushed signal protos to rmq. AnomalyId:{}, SignalId:{}, AccountId:{}", anomalyEvent.getAnomalyId(), newSignal.getSignalId(),
                            signalDetails.getAccountId());

                } else {
                    log.error("AI-OPS: Fail to insert mle generated anomalyId:{},signalId:{}, accountId:{}", anomalyEvent.getAnomalyId(),
                            signalDetails.getSignalId(), signalDetails.getAccountId());
                }
                healthMetrics.updateProcessDetails("MLEBasedCreateSignals", System.currentTimeMillis() - startTime);
            } else {
                log.debug("AI-OPS signal details found in OS is signalId:{}, accountId:{}, anomalyId:{}", signalById, signalDetails.getAccountId(), anomalyEvent.getAnomalyId());

                if (signalById.getCurrentStatus().equals(SignalStatus.CLOSED.name()) || signalById.getCurrentStatus().equals(SignalStatus.UPGRADED.name()) || signalById.getCurrentStatus().equals(SignalStatus.DOWNGRADED.name())) {
                    log.warn("AI-OPS Signal {} is already in {} state. No further update allowed on it.", signalById.getSignalId(), signalById.getCurrentStatus());
                    return;
                }

                SignalSummary signalSummary = redisUtilities.getSignalSummary(signalById.getSignalId());

                boolean isSeverityChanged = anomalySeverityId > signalById.getSeverityId();
                boolean isServiceAdded = !signalById.getServiceIds().containsAll(anomalyEvent.getServiceIdsList());
                AnomalySummary anomalySummary = commons.getAnomalySummary(anomalyEvent.getInstanceId(), anomalyEvent.getKpiId(),
                        anomalyEvent.getCategoryId(), new HashSet<>(anomalyEvent.getServiceIdsList()), SignalType.valueOf(signalDetails.getSignalType()),
                        anomalySeverityId, anomalyEvent.getAnomalyTime(), anomalyEvent.getIsWorkLoad(), anomalyEvent.getAnomalyId(),
                        anomalyEvent.getKpiAttribute(), anomalyEvent.getKpiGroupId(), anomalyEvent.getThresholdType(),
                        anomalyEvent.getOperationType(), metadataMap, anomalyEvent.getKpiValue(), anomalyEvent.getThresholdsMap(), signalDetails.getAccountId());

                log.debug("AI-OPS: related signal for signal id {} is {}", signalDetails.getSignalId(), signalDetails.getRelatedSignalsList().size());
                Set<String> relatedSignalList = new HashSet<>(signalDetails.getRelatedSignalsList());

                boolean updateStatus = signalRepo.updateMLESignal(signalDetails.getUpdateTime(), signalDetails.getSignalStatus(),
                        metadataMap, anomalyEvent.getAnomalyId(), signalDetails.getSignalId(), signalDetails.getAccountId(),
                        isSeverityChanged ? anomalySeverityId : signalById.getSeverityId(), anomalySummary, relatedSignalList, signalById, signalSummary.getApplicationIds(), anomalyEvent.getIsWorkLoad(), anomalySeverityId);

                if (updateStatus) {
                    log.info("AI-OPS: MLE signal updated successfully for the accountId:{} with signalId:{}, anomalyId:{}",
                            signalDetails.getAccountId(), signalDetails.getSignalId(), anomalyEvent.getAnomalyId());

                    // TODO: send signal protos to rmq with updated value
                    forwarder.sendSignalMessages(commons.getSignalProto(signalById, false,
                            isSeverityChanged, isServiceAdded, true, anomalySummary));
                    log.debug("AI-OPS: push signal protos to rmq. SignalId:{}, AccountId:{}, anomalyId:{}",
                            signalDetails.getSignalId(), signalDetails.getAccountId(), anomalyEvent.getAnomalyId());
                } else {
                    log.error("AI-OPS: Fail to update mle generated signalId:{}, accountId:{}, anomalyId:{}",
                            signalDetails.getSignalId(), signalDetails.getAccountId(), anomalyEvent.getAnomalyId());
                }
                healthMetrics.updateProcessDetails("MLEBasedUpdateSignals", System.currentTimeMillis() - startTime);

            }

        } catch (Exception ex) {
            healthMetrics.updateErrors();
            log.error("AI-OPS: Error in processing mle signal. Signal Details:{}", signalDetails, ex);
        }
    }
}
