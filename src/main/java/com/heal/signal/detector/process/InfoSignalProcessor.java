package com.heal.signal.detector.process;

import com.appnomic.appsone.common.protbuf.AnomalyEventProtos;
import com.heal.configuration.enums.SignalType;
import com.heal.configuration.pojos.Account;
import com.heal.configuration.pojos.AnomalySummary;
import com.heal.configuration.pojos.CompInstKpiEntity;
import com.heal.configuration.pojos.Service;
import com.heal.configuration.pojos.opensearch.SignalDetails;
import com.heal.signal.detector.cache.CacheWrapper;
import com.heal.signal.detector.opensearch.SignalRepo;
import com.heal.signal.detector.pojos.FailedOpenSignalPojo;
import com.heal.signal.detector.pojos.SignalStatus;
import com.heal.signal.detector.service.ForwarderToQueue;
import com.heal.signal.detector.util.Commons;
import com.heal.signal.detector.util.HealthMetrics;
import com.heal.signal.detector.util.LocalQueues;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

@Component
@Slf4j
public class InfoSignalProcessor {
    @Autowired
    Commons commons;

    @Autowired
    HealthMetrics metrics;

    @Autowired
    SignalRepo signalRepo;

    @Autowired
    ForwarderToQueue forwarder;

    @Autowired
    CacheWrapper wrapper;

    @Autowired
    LocalQueues localQueues;

    public void processInfoEvent(AnomalyEventProtos.AnomalyEvent anomalyEvent, CompInstKpiEntity kpiEntity, Account account) {

        long st = System.currentTimeMillis();
        Set<SignalDetails> openSignalsFromOS;
        try {

            try {
                openSignalsFromOS = signalRepo.getOpenSignals(account.getIdentifier(), true, true);
            } catch (Exception e) {
                log.error("Error occurred while fetching open signals. So pushing in failed queue. Will be processing it later " +
                                "for accountIdentifier: {}, anomalyId: {}",
                        account.getIdentifier(), anomalyEvent.getAnomalyId(), e);

                localQueues.addToFailedOpenSignalQueue(FailedOpenSignalPojo.builder()
                        .signalType(SignalType.INFO)
                        .anomalyEvent(anomalyEvent)
                        .account(account)
                        .compInstKpiEntity(kpiEntity)
                        .build());
                return;
            }

            String serviceId = anomalyEvent.getKpis().getSvcId(0);

            Service service = wrapper.getServiceDetailsByIdentifier(anomalyEvent.getAccountId(), serviceId);
            if (service == null) {
                log.error("Invalid service identifier, Anomaly event details:{}", anomalyEvent);
                metrics.updateErrors();
                return;
            }

            SignalDetails signalDetails = openSignalsFromOS
                    .parallelStream()
                    .filter(s -> s.getSignalType().equalsIgnoreCase(SignalType.INFO.name()))
                    .filter(s -> s.getAccountIdentifiers().contains(anomalyEvent.getAccountId()))
                    .filter(s -> s.getServiceIds().contains(serviceId))
                    .filter(s -> s.getMetadata().containsKey("category_id")
                            && s.getMetadata().get("category_id").equalsIgnoreCase(kpiEntity.getCategoryDetails().getIdentifier()))
                    .findAny()
                    .orElse(null);

            log.debug("Open info signal: anomaly id:{}, category id:{}, service id:{}, account id:{}, signal details:{}", anomalyEvent.getAnomalyId(),
                    kpiEntity.getCategoryDetails(), serviceId, anomalyEvent.getAccountId(), (signalDetails != null) ? signalDetails.getSignalId() : null);

            int severityId = Integer.parseInt(anomalyEvent.getKpis().getThresholdSeverity());

            Map<String, String> metaData = commons.getSignalMetaData(anomalyEvent, SignalType.INFO);
            //NOTE:- Proto will give immutable map. Don't remove new HashMap<>() here, else java.lang.UnsupportedOperationException
            // will come in case of map.put().
            Map<String, String> anomalyEventMetadataMap = new HashMap<>(anomalyEvent.getKpis().getMetadataMap());

            if (signalDetails == null) {
                log.info("No info signal is found. So, we will create a new info signal for anomaly id:{}, accountId:{}, serviceId:{}, categoryId:{}.",
                        anomalyEvent.getAnomalyId(), anomalyEvent.getAccountId(), serviceId, kpiEntity.getCategoryDetails().getIdentifier());

                metaData.put("category_id", kpiEntity.getCategoryDetails().getIdentifier());

                String signalId = commons.createSignalId("I", account.getId(), kpiEntity.getCategoryDetails().getId(), service.getId(), anomalyEvent.getEndTimeGMT() / 1000);

                signalDetails = commons.createSignal(signalId, anomalyEvent.getEndTimeGMT(), anomalyEvent.getEndTimeGMT(), 0L, SignalStatus.OPEN.name(),
                        Collections.singleton(anomalyEvent.getAnomalyId()), SignalType.INFO, Collections.singleton(serviceId), Collections.singleton(serviceId),
                        Collections.singleton(anomalyEvent.getAnomalyId()), null, null, severityId, metaData, anomalyEvent.getAccountId());

                AnomalySummary anomalySummary = commons.getAnomalySummary(anomalyEvent.getKpis().getInstanceId(), kpiEntity.getIdentifier(),
                        kpiEntity.getCategoryDetails().getIdentifier(), Collections.singleton(service.getIdentifier()), SignalType.INFO,
                        severityId, anomalyEvent.getEndTimeGMT(), false, anomalyEvent.getAnomalyId(),
                        anomalyEvent.getKpis().getKpiAttribute(), kpiEntity.getGroupIdentifier(), anomalyEvent.getThresholdType(), anomalyEvent.getOperationType(),
                        anomalyEventMetadataMap, anomalyEvent.getKpis().getValue(), anomalyEvent.getKpis().getThresholdsMap(), anomalyEvent.getAccountId());

                boolean insertStatus = signalRepo.insertSignal(signalDetails, anomalyEvent.getAccountId(), anomalySummary, false, new HashSet<>(anomalyEvent.getAppIdList()), false, Integer.parseInt(anomalyEvent.getKpis().getThresholdSeverity()));
                metrics.updateSignalOpenCount(1);
                metrics.updateSnapshots(signalDetails.getSignalId() + "_CREATED", 1);

                if (insertStatus) {
                    forwarder.sendSignalMessages(commons.getSignalProto(signalDetails, false,
                            false, false, false, anomalySummary));
                }
                log.info("Info signal is created, anomaly id:{}, category id:{}, service id:{}, account id:{}, signal details:{}", anomalyEvent.getAnomalyId(),
                        kpiEntity.getCategoryDetails(), serviceId, anomalyEvent.getAccountId(), signalDetails);

            } else {

                boolean isSeverityUpdated = false;
                if (severityId > signalDetails.getSeverityId()) {
                    signalDetails.setSeverityId(severityId);
                    isSeverityUpdated = true;
                }

                AnomalySummary anomalySummary = commons.getAnomalySummary(anomalyEvent.getKpis().getInstanceId(), kpiEntity.getIdentifier(),
                        kpiEntity.getCategoryDetails().getIdentifier(), Collections.singleton(service.getIdentifier()), SignalType.INFO,
                        signalDetails.getSeverityId(), anomalyEvent.getEndTimeGMT(), false, anomalyEvent.getAnomalyId(),
                        anomalyEvent.getKpis().getKpiAttribute(), kpiEntity.getGroupIdentifier(), anomalyEvent.getThresholdType(), anomalyEvent.getOperationType(),
                        anomalyEventMetadataMap, anomalyEvent.getKpis().getValue(), anomalyEvent.getKpis().getThresholdsMap(), anomalyEvent.getAccountId());

                boolean updateStatus = signalRepo.updateSignal(anomalyEvent.getEndTimeGMT(), SignalStatus.OPEN.name(),
                        SignalStatus.OPEN.name(), metaData, anomalyEvent.getAnomalyId(), signalDetails.getSignalId(),
                        anomalyEvent.getAccountId(), signalDetails.getSeverityId(), anomalySummary, signalDetails.getStartedTime(), false, new HashSet<>(anomalyEvent.getAppIdList()), false, Integer.parseInt(anomalyEvent.getKpis().getThresholdSeverity()));
                metrics.updateSignalUpdateCount(1);
                metrics.updateSnapshots(signalDetails.getSignalId() + "_UPDATED", 1);

                if (updateStatus) {
                    forwarder.sendSignalMessages(commons.getSignalProto(signalDetails, false,
                            isSeverityUpdated, false, false, anomalySummary));
                }
                log.info("Info signal is updated,anomaly id:{}, signal id:{}", anomalyEvent.getAnomalyId(), signalDetails.getSignalId());
            }
            boolean queueUpdateStatus = signalRepo.updateAnomaly(anomalyEvent.getAnomalyId(), anomalyEvent.getEndTimeGMT(),
                    Collections.singleton(signalDetails.getSignalId()), anomalyEvent.getAccountId());
            if (queueUpdateStatus) {
                log.trace("Anomaly updated into scheduler queue for info signal, signalId:{}, anomalyId:{}", signalDetails.getSignalId(), anomalyEvent.getAnomalyId());
            } else {
                log.error("Anomaly not updated into scheduler queue for info signal, signalId:{}, anomalyId:{}, because of this anomaly wont be update with signal id.", signalDetails.getSignalId(), anomalyEvent.getAnomalyId());
            }
        } catch (Exception e) {
            log.error("Error occurred while processing info signal. Anomaly details:{}", anomalyEvent, e);
            metrics.updateErrors();
        } finally {
            long processTime = System.currentTimeMillis() - st;
            log.debug("{}:Anomaly process time for info signal is {} ms.", anomalyEvent.getAnomalyId(), processTime);
            metrics.updateProcessDetails("InfoAnomalyProcessTime", processTime);
            metrics.updateProcessDetailsCounter("InfoAnomalyProcessCounter", processTime);
        }
    }
}
