<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="30 seconds">
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/signal-detector.log</file>

        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>logs/signal-detector_%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>15MB</maxFileSize>
            <maxHistory>10</maxHistory>
            <totalSizeCap>10GB</totalSizeCap>
        </rollingPolicy>

        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS, Asia/Kolkata} [%thread] %-5level %logger{36}:%line - %msg%n</pattern>
        </encoder>
    </appender>

    <appender name="CORE_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/signal-detector-core.log</file>

        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>logs/signal-detector-core_%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>15MB</maxFileSize>
            <maxHistory>10</maxHistory>
            <totalSizeCap>10GB</totalSizeCap>
        </rollingPolicy>

        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS, Asia/Kolkata} [%thread] %-5level %logger{36}:%line - %msg%n</pattern>
        </encoder>
    </appender>

    <appender name="STATS_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>./logs/signal-detector-stats.log</file>

        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>./logs/signal-detector-stats_%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>15MB</maxFileSize>
            <maxHistory>10</maxHistory>
            <totalSizeCap>10GB</totalSizeCap>
        </rollingPolicy>

        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS, Asia/Kolkata} [%thread] %-5level %logger{36}:%line - %msg%n</pattern>
        </encoder>
    </appender>

    <logger name="com.heal.signal.detector" level="INFO" additivity="false">
        <appender-ref ref="FILE"/>
    </logger>

    <logger name="com.heal.signal.detector.scheduler.StatisticsScheduler" level="DEBUG" additivity="false">
        <appender-ref ref="STATS_FILE"/>
    </logger>

    <root level="INFO">
        <appender-ref ref="CORE_FILE"/>
    </root>
</configuration>